2025-07-30 20:10:41,025 - __main__ - INFO - composite13_66 开始进化第 1 代
2025-07-30 20:10:41,025 - __main__ - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-07-30 20:10:41,025 - StatsExpert - INFO - 开始统计分析
2025-07-30 20:10:41,050 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 9955.0, 'max': 117054.0, 'mean': 76847.5, 'std': 44115.43363325357}, 'diversity': 0.9235690235690236, 'clusters': {'clusters': 9, 'cluster_sizes': [2, 1, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-30 20:10:41,050 - PathExpert - INFO - 开始路径结构分析
2025-07-30 20:10:41,093 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-07-30 20:10:41,093 - PathExpert - INFO - 路径结构分析完成
2025-07-30 20:10:41,093 - EliteExpert - INFO - 开始精英解分析
2025-07-30 20:10:41,093 - EliteExpert - WARNING - 没有精英解可供分析
2025-07-30 20:10:41,748 - LandscapeExpert - INFO - 开始景观分析
2025-07-30 20:10:41,748 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-30 20:10:41,748 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 0/5)
- population_size: 10
- cost_stats: min 9955.0, mean 76847.5, max 117054.0, std 44115.43363325357
- diversity: 0.9235690235690236
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Not available (Δ Not available, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: []
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}
- elite_diversity: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

### Output Example
```json
{
  "search_space_features": {"ruggedness": 0.5, "modality": "multi", "deceptiveness": "unknown"},
  "population_state": {"diversity": 0.5, "convergence": 0.4, "clustering": "weak"},
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "unknown",
  "evolution_direction": {"recommended_focus": "explore", "operators": []}
}
```

### Formatting Guidelines (read carefully, DO NOT output this section)
1. Do NOT copy or repeat any *_sample values above; at least 50% of elements in difficult_regions and opportunity_regions must differ from those samples.
2. Use [] (empty array) when a list has no content; never output the string "None" or a null value.
3. For search_space_features use enumerated strings only:
    • ruggedness: "low" | "medium" | "high"
    • modality: "mono" | "multi" | "unknown"
    • deceptiveness: "low" | "moderate" | "high" | "unknown"
4. diversity and convergence must be floats with exactly 3 decimal places (e.g. 0.742).
5. Follow the JSON schema strictly; no additional keys or commentary.

2025-07-30 20:10:41,748 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-30 20:10:43,369 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: 400 Client Error: Bad Request for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 20:10:45,373 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 2/3)
2025-07-30 20:10:47,047 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 20:10:49,048 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 3/3)
2025-07-30 20:10:50,634 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 20:10:50,636 - LandscapeExpert - INFO - LLM返回的分析结果: API请求失败: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 20:10:50,636 - LandscapeExpert - WARNING - 解析景观分析结果失败: Unable to extract valid JSON data from the response
2025-07-30 20:10:50,636 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-30 20:10:50,636 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.5, 'modality': 'unknown', 'deceptiveness': 'unknown'}, 'population_state': {'diversity': 0.5, 'convergence': 0.5, 'clustering': 'unknown'}, 'difficult_regions': [], 'opportunity_regions': [], 'evolution_phase': 'unknown', 'evolution_direction': {'recommended_focus': 'balance', 'operators': []}, 'raw_text': 'API请求失败: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ'}
2025-07-30 20:10:50,636 - __main__ - INFO - 开始策略分配阶段
2025-07-30 20:10:50,637 - StrategyExpert - INFO - 开始策略分配分析
2025-07-30 20:10:50,637 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 0
- Population: 10 individuals
  • diversity: 0.5
  • best_cost: 9955.0
  • mean_cost: 76847.5
- Search-space: ruggedness 0.5, modality unknown, deceptiveness unknown
- Evolution phase: unknown
- Landscape focus suggestion: balance
- Spatial density summary: None
- Elite diversity: N/A
- Difficult regions (# 0): None
- Opportunity regions (# 0): None
- Last-iteration feedback: None

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Formatting Guidelines (DO NOT output this section)
1. global_explore_ratio must be rounded to exactly 2 decimal places (e.g., 0.80).
2. The individual_assignments mapping MUST include every index from 0 to (population_size - 1) without omission.
3. Valid values are "explore" or "exploit" (lowercase).
4. rationale ≤ 40 words.

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-30 20:10:50,637 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-30 20:10:50,637 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-30 20:10:52,232 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 20:10:54,233 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 2/3)
2025-07-30 20:10:55,854 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 20:10:57,855 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 3/3)
2025-07-30 20:10:59,492 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 20:10:59,494 - StrategyExpert - INFO - LLM返回的策略分配结果: API请求失败: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 20:10:59,494 - StrategyExpert - WARNING - 解析策略分配结果时出错: Unable to extract valid JSON data from the response
2025-07-30 20:10:59,494 - StrategyExpert - INFO - 策略分配完成: ['explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-07-30 20:10:59,494 - __main__ - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-07-30 20:10:59,494 - __main__ - INFO - 策略分配完整报告: API请求失败: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 20:10:59,494 - __main__ - INFO - 策略分配阶段完成
2025-07-30 20:10:59,494 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-07-30 20:10:59,494 - __main__ - INFO - 策略分配完整报告: API请求失败: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 20:10:59,494 - __main__ - INFO - 开始进化阶段
2025-07-30 20:10:59,494 - __main__ - INFO - 为个体 0 生成探索路径
2025-07-30 20:10:59,494 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-07-30 20:10:59,494 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-07-30 20:10:59,494 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-07-30 20:10:59,494 - ExplorationExpert - INFO - 计算路径成本
2025-07-30 20:10:59,628 - ExplorationExpert - INFO - 探索路径生成完成，成本: 53872.0, 路径长度: 66
2025-07-30 20:10:59,628 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [57, 53, 4, 16, 37, 35, 31, 23, 18, 28, 30, 29, 3, 64, 10, 52, 12, 8, 60, 6, 55, 56, 54, 2, 1, 19, 14, 24, 21, 20, 13, 32, 40, 48, 22, 34, 26, 43, 44, 17, 15, 27, 46, 51, 25, 36, 42, 49, 41, 47, 9, 61, 58, 62, 5, 7, 11, 63, 59, 39, 45, 38, 50, 33, 0, 65], 'cur_cost': 53872.0}
2025-07-30 20:10:59,628 - __main__ - INFO - 为个体 1 生成利用路径
2025-07-30 20:10:59,628 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-30 20:10:59,628 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-30 20:10:59,628 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 100377.0
2025-07-30 20:11:00,818 - ExploitationExpert - INFO - res_population_num: 1
2025-07-30 20:11:00,819 - ExploitationExpert - INFO - res_population_costs: [9570.0]
2025-07-30 20:11:00,819 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-07-30 20:11:00,820 - ExploitationExpert - INFO - populations_num: 10
2025-07-30 20:11:00,820 - ExploitationExpert - INFO - populations: [{'tour': [57, 53, 4, 16, 37, 35, 31, 23, 18, 28, 30, 29, 3, 64, 10, 52, 12, 8, 60, 6, 55, 56, 54, 2, 1, 19, 14, 24, 21, 20, 13, 32, 40, 48, 22, 34, 26, 43, 44, 17, 15, 27, 46, 51, 25, 36, 42, 49, 41, 47, 9, 61, 58, 62, 5, 7, 11, 63, 59, 39, 45, 38, 50, 33, 0, 65], 'cur_cost': 53872.0}, {'tour': array([ 8,  4, 26, 56, 46, 52, 42, 43, 65, 27, 33, 11, 58, 64, 24, 34, 53,
        5, 59,  3, 16, 28, 19, 48, 45, 54,  7, 17, 20, 41, 60,  1, 37, 38,
        0, 23, 40, 44, 15, 35, 10, 55,  9, 51, 36, 30, 47, 39, 18, 31, 22,
        2, 50, 14, 63,  6, 62, 32, 29, 57, 25, 61, 49, 21, 12, 13],
      dtype=int64), 'cur_cost': 100377.0}, {'tour': array([36, 26, 25, 31, 33, 28, 30, 35, 34, 37, 27, 24, 29, 32, 18, 16, 23,
       22, 12, 17, 15, 14, 20, 21, 13, 19,  9, 11,  7,  3,  1,  0, 10,  8,
        2,  6,  4,  5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52,
       63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42],
      dtype=int64), 'cur_cost': 9955.0}, {'tour': array([38, 53,  2, 35, 34,  7, 16, 30,  8, 24,  1, 41, 20,  6, 45, 29,  9,
       43, 65, 61, 23, 21, 26, 55, 40, 44, 36, 22, 11, 14, 37, 15, 17, 10,
       25,  4, 12, 27, 58, 49, 39, 32,  0, 57, 50,  3, 54, 18, 52, 28, 59,
       64, 13,  5, 62, 42, 19, 63, 48, 47, 60, 56, 46, 33, 31, 51],
      dtype=int64), 'cur_cost': 109030.0}, {'tour': array([47, 33, 49, 54,  8, 18, 46, 62, 55, 15, 50, 21, 17,  0, 29, 64, 39,
       57, 42,  7, 23, 52, 28, 22, 59, 30,  4, 34, 43,  2, 38, 45, 31, 48,
       26, 27,  5, 32, 63,  9, 16, 11, 53, 37, 40, 58, 14,  1, 44, 25, 51,
       36, 24,  3,  6, 61, 41, 19, 12, 35, 65, 13, 20, 56, 10, 60],
      dtype=int64), 'cur_cost': 117054.0}, {'tour': array([59, 24, 39,  6, 56, 44,  3, 28, 26, 20, 37,  9, 29, 14, 38, 10,  8,
       50,  7, 65, 25, 22,  4, 32, 64, 23, 33, 36, 48, 57, 21, 35, 16, 40,
       46, 49, 43, 41,  1, 55, 30, 52, 17, 47, 63, 54, 62,  5, 11, 42, 15,
       18,  2, 31, 13,  0, 12, 34, 53, 61, 60, 51, 58, 19, 45, 27],
      dtype=int64), 'cur_cost': 104247.0}, {'tour': array([61, 32, 47, 42, 12, 63, 30, 65,  6,  2, 54,  3,  1, 18, 41, 16, 27,
       36, 40, 55, 23, 26, 17, 11, 46, 34, 31, 64, 48, 10, 25, 60, 52, 43,
       37, 15, 19, 21, 44,  9, 50, 28, 20, 45, 39, 49, 35, 51, 33, 14, 24,
       29, 56,  5, 13,  0, 62, 59,  7,  8, 22, 38,  4, 57, 53, 58],
      dtype=int64), 'cur_cost': 92927.0}, {'tour': array([26, 21, 32, 52, 27, 35, 58,  3, 64, 53, 16,  2, 62, 65,  8,  7, 44,
       40,  0, 20, 54, 33, 28, 25,  1, 47,  6, 14, 46,  4, 23, 63, 59, 57,
       55, 43, 11, 45, 51, 22, 12, 49, 42, 24,  5, 13, 30, 50,  9, 36, 41,
       17, 60, 18, 10, 31, 15, 29, 39, 48, 56, 37, 19, 34, 61, 38],
      dtype=int64), 'cur_cost': 101136.0}, {'tour': array([ 0, 16, 58, 34,  9, 45, 21, 60, 53, 14, 23, 31, 20, 47, 29, 39, 37,
       55, 52, 26, 50, 51,  1, 44, 65,  8, 36,  3,  4, 10, 13, 18, 12, 38,
       33, 49, 63,  6, 15, 22,  5, 19, 17, 32, 28, 43, 59, 35, 46, 25, 11,
       41, 27, 56, 42, 24, 64, 48,  7, 61, 40, 62, 57, 54, 30,  2],
      dtype=int64), 'cur_cost': 109785.0}, {'tour': array([20, 25, 45,  6, 37, 48, 49, 54,  1, 16, 26, 31, 42, 51,  9, 50, 10,
       22,  0, 34, 14, 41, 56,  8, 28, 52, 47, 11, 29, 58, 60, 63, 59, 61,
       12, 15, 23,  7, 32, 35, 13, 30, 53, 55,  4, 57, 21, 65, 39, 62, 46,
        3,  2, 38, 17, 44, 33, 36, 40, 18, 27, 19, 24,  5, 43, 64],
      dtype=int64), 'cur_cost': 104116.0}]
2025-07-30 20:11:00,823 - ExploitationExpert - INFO - 局部搜索耗时: 1.19秒
2025-07-30 20:11:00,823 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-07-30 20:11:00,824 - __main__ - WARNING - 个体 1 的利用路径生成失败，保留原个体
2025-07-30 20:11:00,824 - __main__ - INFO - 为个体 2 生成探索路径
2025-07-30 20:11:00,824 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-07-30 20:11:00,824 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-07-30 20:11:00,828 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-07-30 20:11:00,828 - ExplorationExpert - INFO - 计算路径成本
2025-07-30 20:11:00,828 - ExplorationExpert - INFO - 探索路径生成完成，成本: 101456.0, 路径长度: 66
2025-07-30 20:11:00,828 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [4, 18, 30, 52, 12, 6, 55, 2, 40, 22, 26, 43, 15, 27, 46, 25, 36, 42, 49, 47, 9, 62, 39, 3, 21, 31, 8, 24, 60, 61, 57, 58, 16, 20, 7, 5, 64, 38, 34, 0, 54, 48, 1, 14, 35, 33, 56, 29, 17, 50, 59, 19, 65, 53, 63, 41, 45, 23, 10, 32, 44, 28, 51, 13, 11, 37], 'cur_cost': 101456.0}
2025-07-30 20:11:00,829 - __main__ - INFO - 为个体 3 生成利用路径
2025-07-30 20:11:00,829 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-30 20:11:00,829 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-30 20:11:00,829 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 110608.0
2025-07-30 20:11:02,325 - ExploitationExpert - INFO - res_population_num: 1
2025-07-30 20:11:02,325 - ExploitationExpert - INFO - res_population_costs: [9570.0]
2025-07-30 20:11:02,325 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-07-30 20:11:02,326 - ExploitationExpert - INFO - populations_num: 10
2025-07-30 20:11:02,326 - ExploitationExpert - INFO - populations: [{'tour': [57, 53, 4, 16, 37, 35, 31, 23, 18, 28, 30, 29, 3, 64, 10, 52, 12, 8, 60, 6, 55, 56, 54, 2, 1, 19, 14, 24, 21, 20, 13, 32, 40, 48, 22, 34, 26, 43, 44, 17, 15, 27, 46, 51, 25, 36, 42, 49, 41, 47, 9, 61, 58, 62, 5, 7, 11, 63, 59, 39, 45, 38, 50, 33, 0, 65], 'cur_cost': 53872.0}, {'tour': array([ 8,  4, 26, 56, 46, 52, 42, 43, 65, 27, 33, 11, 58, 64, 24, 34, 53,
        5, 59,  3, 16, 28, 19, 48, 45, 54,  7, 17, 20, 41, 60,  1, 37, 38,
        0, 23, 40, 44, 15, 35, 10, 55,  9, 51, 36, 30, 47, 39, 18, 31, 22,
        2, 50, 14, 63,  6, 62, 32, 29, 57, 25, 61, 49, 21, 12, 13],
      dtype=int64), 'cur_cost': 100377.0}, {'tour': [4, 18, 30, 52, 12, 6, 55, 2, 40, 22, 26, 43, 15, 27, 46, 25, 36, 42, 49, 47, 9, 62, 39, 3, 21, 31, 8, 24, 60, 61, 57, 58, 16, 20, 7, 5, 64, 38, 34, 0, 54, 48, 1, 14, 35, 33, 56, 29, 17, 50, 59, 19, 65, 53, 63, 41, 45, 23, 10, 32, 44, 28, 51, 13, 11, 37], 'cur_cost': 101456.0}, {'tour': array([48, 21, 41,  9, 32, 58,  7, 61, 40, 60, 65, 28, 52, 46, 14, 63, 55,
       50,  3, 31, 10,  2, 22, 54, 24, 47, 44, 29, 57, 56, 53, 49, 20, 39,
       42, 27,  5, 18, 38,  0, 13, 35, 64, 30, 33, 11,  1, 45, 34, 12, 37,
       59, 43, 26,  4,  8, 15, 19, 62, 17, 16, 51, 36, 23,  6, 25],
      dtype=int64), 'cur_cost': 110608.0}, {'tour': array([47, 33, 49, 54,  8, 18, 46, 62, 55, 15, 50, 21, 17,  0, 29, 64, 39,
       57, 42,  7, 23, 52, 28, 22, 59, 30,  4, 34, 43,  2, 38, 45, 31, 48,
       26, 27,  5, 32, 63,  9, 16, 11, 53, 37, 40, 58, 14,  1, 44, 25, 51,
       36, 24,  3,  6, 61, 41, 19, 12, 35, 65, 13, 20, 56, 10, 60],
      dtype=int64), 'cur_cost': 117054.0}, {'tour': array([59, 24, 39,  6, 56, 44,  3, 28, 26, 20, 37,  9, 29, 14, 38, 10,  8,
       50,  7, 65, 25, 22,  4, 32, 64, 23, 33, 36, 48, 57, 21, 35, 16, 40,
       46, 49, 43, 41,  1, 55, 30, 52, 17, 47, 63, 54, 62,  5, 11, 42, 15,
       18,  2, 31, 13,  0, 12, 34, 53, 61, 60, 51, 58, 19, 45, 27],
      dtype=int64), 'cur_cost': 104247.0}, {'tour': array([61, 32, 47, 42, 12, 63, 30, 65,  6,  2, 54,  3,  1, 18, 41, 16, 27,
       36, 40, 55, 23, 26, 17, 11, 46, 34, 31, 64, 48, 10, 25, 60, 52, 43,
       37, 15, 19, 21, 44,  9, 50, 28, 20, 45, 39, 49, 35, 51, 33, 14, 24,
       29, 56,  5, 13,  0, 62, 59,  7,  8, 22, 38,  4, 57, 53, 58],
      dtype=int64), 'cur_cost': 92927.0}, {'tour': array([26, 21, 32, 52, 27, 35, 58,  3, 64, 53, 16,  2, 62, 65,  8,  7, 44,
       40,  0, 20, 54, 33, 28, 25,  1, 47,  6, 14, 46,  4, 23, 63, 59, 57,
       55, 43, 11, 45, 51, 22, 12, 49, 42, 24,  5, 13, 30, 50,  9, 36, 41,
       17, 60, 18, 10, 31, 15, 29, 39, 48, 56, 37, 19, 34, 61, 38],
      dtype=int64), 'cur_cost': 101136.0}, {'tour': array([ 0, 16, 58, 34,  9, 45, 21, 60, 53, 14, 23, 31, 20, 47, 29, 39, 37,
       55, 52, 26, 50, 51,  1, 44, 65,  8, 36,  3,  4, 10, 13, 18, 12, 38,
       33, 49, 63,  6, 15, 22,  5, 19, 17, 32, 28, 43, 59, 35, 46, 25, 11,
       41, 27, 56, 42, 24, 64, 48,  7, 61, 40, 62, 57, 54, 30,  2],
      dtype=int64), 'cur_cost': 109785.0}, {'tour': array([20, 25, 45,  6, 37, 48, 49, 54,  1, 16, 26, 31, 42, 51,  9, 50, 10,
       22,  0, 34, 14, 41, 56,  8, 28, 52, 47, 11, 29, 58, 60, 63, 59, 61,
       12, 15, 23,  7, 32, 35, 13, 30, 53, 55,  4, 57, 21, 65, 39, 62, 46,
        3,  2, 38, 17, 44, 33, 36, 40, 18, 27, 19, 24,  5, 43, 64],
      dtype=int64), 'cur_cost': 104116.0}]
2025-07-30 20:11:02,329 - ExploitationExpert - INFO - 局部搜索耗时: 1.50秒
2025-07-30 20:11:02,329 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-07-30 20:11:02,329 - __main__ - WARNING - 个体 3 的利用路径生成失败，保留原个体
2025-07-30 20:11:02,330 - __main__ - INFO - 为个体 4 生成探索路径
2025-07-30 20:11:02,330 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-07-30 20:11:02,330 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-07-30 20:11:02,332 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-07-30 20:11:02,332 - ExplorationExpert - INFO - 计算路径成本
2025-07-30 20:11:02,332 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12845.0, 路径长度: 66
2025-07-30 20:11:02,332 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 12, 8, 5, 4, 6, 2, 10, 1, 7, 3, 9, 11, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 16, 18, 17, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12845.0}
2025-07-30 20:11:02,332 - __main__ - INFO - 为个体 5 生成利用路径
2025-07-30 20:11:02,332 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-30 20:11:02,332 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-30 20:11:02,332 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 91149.0
2025-07-30 20:11:02,390 - ExploitationExpert - INFO - res_population_num: 4
2025-07-30 20:11:02,390 - ExploitationExpert - INFO - res_population_costs: [9570.0, 9544, 9538, 9526]
2025-07-30 20:11:02,390 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 47, 49, 40, 43, 48, 46, 45, 44, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       18, 17, 12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 47, 49, 40, 43, 48, 46, 45, 44, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 16, 18, 17, 12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-07-30 20:11:02,392 - ExploitationExpert - INFO - populations_num: 10
2025-07-30 20:11:02,392 - ExploitationExpert - INFO - populations: [{'tour': [57, 53, 4, 16, 37, 35, 31, 23, 18, 28, 30, 29, 3, 64, 10, 52, 12, 8, 60, 6, 55, 56, 54, 2, 1, 19, 14, 24, 21, 20, 13, 32, 40, 48, 22, 34, 26, 43, 44, 17, 15, 27, 46, 51, 25, 36, 42, 49, 41, 47, 9, 61, 58, 62, 5, 7, 11, 63, 59, 39, 45, 38, 50, 33, 0, 65], 'cur_cost': 53872.0}, {'tour': array([ 8,  4, 26, 56, 46, 52, 42, 43, 65, 27, 33, 11, 58, 64, 24, 34, 53,
        5, 59,  3, 16, 28, 19, 48, 45, 54,  7, 17, 20, 41, 60,  1, 37, 38,
        0, 23, 40, 44, 15, 35, 10, 55,  9, 51, 36, 30, 47, 39, 18, 31, 22,
        2, 50, 14, 63,  6, 62, 32, 29, 57, 25, 61, 49, 21, 12, 13],
      dtype=int64), 'cur_cost': 100377.0}, {'tour': [4, 18, 30, 52, 12, 6, 55, 2, 40, 22, 26, 43, 15, 27, 46, 25, 36, 42, 49, 47, 9, 62, 39, 3, 21, 31, 8, 24, 60, 61, 57, 58, 16, 20, 7, 5, 64, 38, 34, 0, 54, 48, 1, 14, 35, 33, 56, 29, 17, 50, 59, 19, 65, 53, 63, 41, 45, 23, 10, 32, 44, 28, 51, 13, 11, 37], 'cur_cost': 101456.0}, {'tour': array([48, 21, 41,  9, 32, 58,  7, 61, 40, 60, 65, 28, 52, 46, 14, 63, 55,
       50,  3, 31, 10,  2, 22, 54, 24, 47, 44, 29, 57, 56, 53, 49, 20, 39,
       42, 27,  5, 18, 38,  0, 13, 35, 64, 30, 33, 11,  1, 45, 34, 12, 37,
       59, 43, 26,  4,  8, 15, 19, 62, 17, 16, 51, 36, 23,  6, 25],
      dtype=int64), 'cur_cost': 110608.0}, {'tour': [0, 12, 8, 5, 4, 6, 2, 10, 1, 7, 3, 9, 11, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 16, 18, 17, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12845.0}, {'tour': array([ 8, 36, 25, 57, 64, 63,  4, 53, 28,  2, 16, 49, 44,  6, 14, 31, 18,
       27, 45, 58, 33, 29, 34, 30, 15, 20,  3,  5, 54, 39, 50, 40, 38, 11,
       65, 51, 42, 62, 56, 23, 37, 13, 22,  0, 41, 12, 21, 32, 19, 47, 17,
       46, 10, 24, 52, 26, 43, 59,  1,  7, 35, 60, 48,  9, 55, 61],
      dtype=int64), 'cur_cost': 91149.0}, {'tour': array([61, 32, 47, 42, 12, 63, 30, 65,  6,  2, 54,  3,  1, 18, 41, 16, 27,
       36, 40, 55, 23, 26, 17, 11, 46, 34, 31, 64, 48, 10, 25, 60, 52, 43,
       37, 15, 19, 21, 44,  9, 50, 28, 20, 45, 39, 49, 35, 51, 33, 14, 24,
       29, 56,  5, 13,  0, 62, 59,  7,  8, 22, 38,  4, 57, 53, 58],
      dtype=int64), 'cur_cost': 92927.0}, {'tour': array([26, 21, 32, 52, 27, 35, 58,  3, 64, 53, 16,  2, 62, 65,  8,  7, 44,
       40,  0, 20, 54, 33, 28, 25,  1, 47,  6, 14, 46,  4, 23, 63, 59, 57,
       55, 43, 11, 45, 51, 22, 12, 49, 42, 24,  5, 13, 30, 50,  9, 36, 41,
       17, 60, 18, 10, 31, 15, 29, 39, 48, 56, 37, 19, 34, 61, 38],
      dtype=int64), 'cur_cost': 101136.0}, {'tour': array([ 0, 16, 58, 34,  9, 45, 21, 60, 53, 14, 23, 31, 20, 47, 29, 39, 37,
       55, 52, 26, 50, 51,  1, 44, 65,  8, 36,  3,  4, 10, 13, 18, 12, 38,
       33, 49, 63,  6, 15, 22,  5, 19, 17, 32, 28, 43, 59, 35, 46, 25, 11,
       41, 27, 56, 42, 24, 64, 48,  7, 61, 40, 62, 57, 54, 30,  2],
      dtype=int64), 'cur_cost': 109785.0}, {'tour': array([20, 25, 45,  6, 37, 48, 49, 54,  1, 16, 26, 31, 42, 51,  9, 50, 10,
       22,  0, 34, 14, 41, 56,  8, 28, 52, 47, 11, 29, 58, 60, 63, 59, 61,
       12, 15, 23,  7, 32, 35, 13, 30, 53, 55,  4, 57, 21, 65, 39, 62, 46,
        3,  2, 38, 17, 44, 33, 36, 40, 18, 27, 19, 24,  5, 43, 64],
      dtype=int64), 'cur_cost': 104116.0}]
2025-07-30 20:11:02,395 - ExploitationExpert - INFO - 局部搜索耗时: 0.06秒
2025-07-30 20:11:02,395 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-07-30 20:11:02,395 - __main__ - WARNING - 个体 5 的利用路径生成失败，保留原个体
2025-07-30 20:11:02,395 - __main__ - INFO - 为个体 6 生成探索路径
2025-07-30 20:11:02,395 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-07-30 20:11:02,396 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-07-30 20:11:02,398 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-07-30 20:11:02,398 - ExplorationExpert - INFO - 计算路径成本
2025-07-30 20:11:02,398 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12311.0, 路径长度: 66
2025-07-30 20:11:02,398 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 2, 4, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12311.0}
2025-07-30 20:11:02,398 - __main__ - INFO - 为个体 7 生成利用路径
2025-07-30 20:11:02,398 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-30 20:11:02,398 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-30 20:11:02,398 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 92213.0
2025-07-30 20:11:02,454 - ExploitationExpert - INFO - res_population_num: 6
2025-07-30 20:11:02,454 - ExploitationExpert - INFO - res_population_costs: [9570.0, 9544, 9538, 9526, 9521, 9521]
2025-07-30 20:11:02,454 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 47, 49, 40, 43, 48, 46, 45, 44, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       18, 17, 12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 47, 49, 40, 43, 48, 46, 45, 44, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 16, 18, 17, 12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-07-30 20:11:02,456 - ExploitationExpert - INFO - populations_num: 10
2025-07-30 20:11:02,457 - ExploitationExpert - INFO - populations: [{'tour': [57, 53, 4, 16, 37, 35, 31, 23, 18, 28, 30, 29, 3, 64, 10, 52, 12, 8, 60, 6, 55, 56, 54, 2, 1, 19, 14, 24, 21, 20, 13, 32, 40, 48, 22, 34, 26, 43, 44, 17, 15, 27, 46, 51, 25, 36, 42, 49, 41, 47, 9, 61, 58, 62, 5, 7, 11, 63, 59, 39, 45, 38, 50, 33, 0, 65], 'cur_cost': 53872.0}, {'tour': array([ 8,  4, 26, 56, 46, 52, 42, 43, 65, 27, 33, 11, 58, 64, 24, 34, 53,
        5, 59,  3, 16, 28, 19, 48, 45, 54,  7, 17, 20, 41, 60,  1, 37, 38,
        0, 23, 40, 44, 15, 35, 10, 55,  9, 51, 36, 30, 47, 39, 18, 31, 22,
        2, 50, 14, 63,  6, 62, 32, 29, 57, 25, 61, 49, 21, 12, 13],
      dtype=int64), 'cur_cost': 100377.0}, {'tour': [4, 18, 30, 52, 12, 6, 55, 2, 40, 22, 26, 43, 15, 27, 46, 25, 36, 42, 49, 47, 9, 62, 39, 3, 21, 31, 8, 24, 60, 61, 57, 58, 16, 20, 7, 5, 64, 38, 34, 0, 54, 48, 1, 14, 35, 33, 56, 29, 17, 50, 59, 19, 65, 53, 63, 41, 45, 23, 10, 32, 44, 28, 51, 13, 11, 37], 'cur_cost': 101456.0}, {'tour': array([48, 21, 41,  9, 32, 58,  7, 61, 40, 60, 65, 28, 52, 46, 14, 63, 55,
       50,  3, 31, 10,  2, 22, 54, 24, 47, 44, 29, 57, 56, 53, 49, 20, 39,
       42, 27,  5, 18, 38,  0, 13, 35, 64, 30, 33, 11,  1, 45, 34, 12, 37,
       59, 43, 26,  4,  8, 15, 19, 62, 17, 16, 51, 36, 23,  6, 25],
      dtype=int64), 'cur_cost': 110608.0}, {'tour': [0, 12, 8, 5, 4, 6, 2, 10, 1, 7, 3, 9, 11, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 16, 18, 17, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12845.0}, {'tour': array([ 8, 36, 25, 57, 64, 63,  4, 53, 28,  2, 16, 49, 44,  6, 14, 31, 18,
       27, 45, 58, 33, 29, 34, 30, 15, 20,  3,  5, 54, 39, 50, 40, 38, 11,
       65, 51, 42, 62, 56, 23, 37, 13, 22,  0, 41, 12, 21, 32, 19, 47, 17,
       46, 10, 24, 52, 26, 43, 59,  1,  7, 35, 60, 48,  9, 55, 61],
      dtype=int64), 'cur_cost': 91149.0}, {'tour': [0, 2, 4, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12311.0}, {'tour': array([28, 42, 18, 24, 34, 56, 20, 21, 31,  0,  4, 43, 62, 64, 54, 29, 45,
       57, 61, 58, 39,  6, 59, 49, 30,  7, 10, 12, 15, 25, 40, 48, 51, 44,
       47,  3, 19, 32, 50, 16,  1, 26,  8, 14, 23,  5, 22, 63, 17,  9, 55,
       65, 35,  2, 60, 53, 46, 13, 38, 27, 36, 37, 11, 41, 52, 33],
      dtype=int64), 'cur_cost': 92213.0}, {'tour': array([ 0, 16, 58, 34,  9, 45, 21, 60, 53, 14, 23, 31, 20, 47, 29, 39, 37,
       55, 52, 26, 50, 51,  1, 44, 65,  8, 36,  3,  4, 10, 13, 18, 12, 38,
       33, 49, 63,  6, 15, 22,  5, 19, 17, 32, 28, 43, 59, 35, 46, 25, 11,
       41, 27, 56, 42, 24, 64, 48,  7, 61, 40, 62, 57, 54, 30,  2],
      dtype=int64), 'cur_cost': 109785.0}, {'tour': array([20, 25, 45,  6, 37, 48, 49, 54,  1, 16, 26, 31, 42, 51,  9, 50, 10,
       22,  0, 34, 14, 41, 56,  8, 28, 52, 47, 11, 29, 58, 60, 63, 59, 61,
       12, 15, 23,  7, 32, 35, 13, 30, 53, 55,  4, 57, 21, 65, 39, 62, 46,
        3,  2, 38, 17, 44, 33, 36, 40, 18, 27, 19, 24,  5, 43, 64],
      dtype=int64), 'cur_cost': 104116.0}]
2025-07-30 20:11:02,459 - ExploitationExpert - INFO - 局部搜索耗时: 0.06秒
2025-07-30 20:11:02,459 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-07-30 20:11:02,459 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-07-30 20:11:02,459 - __main__ - INFO - 为个体 8 生成探索路径
2025-07-30 20:11:02,459 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-07-30 20:11:02,459 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-07-30 20:11:02,462 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-07-30 20:11:02,462 - ExplorationExpert - INFO - 计算路径成本
2025-07-30 20:11:02,462 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12332.0, 路径长度: 66
2025-07-30 20:11:02,462 - __main__ - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 14, 20, 15, 22, 12, 17, 18, 16, 23, 13, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12332.0}
2025-07-30 20:11:02,462 - __main__ - INFO - 为个体 9 生成利用路径
2025-07-30 20:11:02,462 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-30 20:11:02,463 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-30 20:11:02,463 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 105422.0
2025-07-30 20:11:02,531 - ExploitationExpert - INFO - res_population_num: 8
2025-07-30 20:11:02,531 - ExploitationExpert - INFO - res_population_costs: [9570.0, 9544, 9538, 9526, 9521, 9521, 9521, 9521]
2025-07-30 20:11:02,531 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 47, 49, 40, 43, 48, 46, 45, 44, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       18, 17, 12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 47, 49, 40, 43, 48, 46, 45, 44, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 16, 18, 17, 12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-07-30 20:11:02,531 - ExploitationExpert - INFO - populations_num: 10
2025-07-30 20:11:02,531 - ExploitationExpert - INFO - populations: [{'tour': [57, 53, 4, 16, 37, 35, 31, 23, 18, 28, 30, 29, 3, 64, 10, 52, 12, 8, 60, 6, 55, 56, 54, 2, 1, 19, 14, 24, 21, 20, 13, 32, 40, 48, 22, 34, 26, 43, 44, 17, 15, 27, 46, 51, 25, 36, 42, 49, 41, 47, 9, 61, 58, 62, 5, 7, 11, 63, 59, 39, 45, 38, 50, 33, 0, 65], 'cur_cost': 53872.0}, {'tour': array([ 8,  4, 26, 56, 46, 52, 42, 43, 65, 27, 33, 11, 58, 64, 24, 34, 53,
        5, 59,  3, 16, 28, 19, 48, 45, 54,  7, 17, 20, 41, 60,  1, 37, 38,
        0, 23, 40, 44, 15, 35, 10, 55,  9, 51, 36, 30, 47, 39, 18, 31, 22,
        2, 50, 14, 63,  6, 62, 32, 29, 57, 25, 61, 49, 21, 12, 13],
      dtype=int64), 'cur_cost': 100377.0}, {'tour': [4, 18, 30, 52, 12, 6, 55, 2, 40, 22, 26, 43, 15, 27, 46, 25, 36, 42, 49, 47, 9, 62, 39, 3, 21, 31, 8, 24, 60, 61, 57, 58, 16, 20, 7, 5, 64, 38, 34, 0, 54, 48, 1, 14, 35, 33, 56, 29, 17, 50, 59, 19, 65, 53, 63, 41, 45, 23, 10, 32, 44, 28, 51, 13, 11, 37], 'cur_cost': 101456.0}, {'tour': array([48, 21, 41,  9, 32, 58,  7, 61, 40, 60, 65, 28, 52, 46, 14, 63, 55,
       50,  3, 31, 10,  2, 22, 54, 24, 47, 44, 29, 57, 56, 53, 49, 20, 39,
       42, 27,  5, 18, 38,  0, 13, 35, 64, 30, 33, 11,  1, 45, 34, 12, 37,
       59, 43, 26,  4,  8, 15, 19, 62, 17, 16, 51, 36, 23,  6, 25],
      dtype=int64), 'cur_cost': 110608.0}, {'tour': [0, 12, 8, 5, 4, 6, 2, 10, 1, 7, 3, 9, 11, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 16, 18, 17, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12845.0}, {'tour': array([ 8, 36, 25, 57, 64, 63,  4, 53, 28,  2, 16, 49, 44,  6, 14, 31, 18,
       27, 45, 58, 33, 29, 34, 30, 15, 20,  3,  5, 54, 39, 50, 40, 38, 11,
       65, 51, 42, 62, 56, 23, 37, 13, 22,  0, 41, 12, 21, 32, 19, 47, 17,
       46, 10, 24, 52, 26, 43, 59,  1,  7, 35, 60, 48,  9, 55, 61],
      dtype=int64), 'cur_cost': 91149.0}, {'tour': [0, 2, 4, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12311.0}, {'tour': array([28, 42, 18, 24, 34, 56, 20, 21, 31,  0,  4, 43, 62, 64, 54, 29, 45,
       57, 61, 58, 39,  6, 59, 49, 30,  7, 10, 12, 15, 25, 40, 48, 51, 44,
       47,  3, 19, 32, 50, 16,  1, 26,  8, 14, 23,  5, 22, 63, 17,  9, 55,
       65, 35,  2, 60, 53, 46, 13, 38, 27, 36, 37, 11, 41, 52, 33],
      dtype=int64), 'cur_cost': 92213.0}, {'tour': [0, 14, 20, 15, 22, 12, 17, 18, 16, 23, 13, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12332.0}, {'tour': array([23, 36,  1, 20, 41, 42, 62, 61, 64,  3, 45, 55, 34, 12, 21, 15, 22,
       51, 24, 37, 17, 28, 31, 44, 63,  9, 54, 57,  2, 27, 49,  8, 18, 29,
       11, 14, 33, 56, 39, 19,  7, 47,  0, 43, 46, 59, 48, 26, 13, 35, 60,
       30, 53,  5, 32, 50, 65, 52, 38, 25, 10,  4, 16, 58,  6, 40],
      dtype=int64), 'cur_cost': 105422.0}]
2025-07-30 20:11:02,531 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒
2025-07-30 20:11:02,531 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-07-30 20:11:02,531 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-07-30 20:11:02,531 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [57, 53, 4, 16, 37, 35, 31, 23, 18, 28, 30, 29, 3, 64, 10, 52, 12, 8, 60, 6, 55, 56, 54, 2, 1, 19, 14, 24, 21, 20, 13, 32, 40, 48, 22, 34, 26, 43, 44, 17, 15, 27, 46, 51, 25, 36, 42, 49, 41, 47, 9, 61, 58, 62, 5, 7, 11, 63, 59, 39, 45, 38, 50, 33, 0, 65], 'cur_cost': 53872.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [4, 18, 30, 52, 12, 6, 55, 2, 40, 22, 26, 43, 15, 27, 46, 25, 36, 42, 49, 47, 9, 62, 39, 3, 21, 31, 8, 24, 60, 61, 57, 58, 16, 20, 7, 5, 64, 38, 34, 0, 54, 48, 1, 14, 35, 33, 56, 29, 17, 50, 59, 19, 65, 53, 63, 41, 45, 23, 10, 32, 44, 28, 51, 13, 11, 37], 'cur_cost': 101456.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 8, 5, 4, 6, 2, 10, 1, 7, 3, 9, 11, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 16, 18, 17, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12845.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 4, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12311.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 14, 20, 15, 22, 12, 17, 18, 16, 23, 13, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12332.0}}]
2025-07-30 20:11:02,531 - __main__ - INFO - 进化阶段完成
2025-07-30 20:11:02,531 - StatsExpert - INFO - 开始统计分析
2025-07-30 20:11:02,558 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 12311.0, 'max': 110608.0, 'mean': 69258.5, 'std': 39914.4015544515}, 'diversity': 0.9121212121212121, 'clusters': {'clusters': 8, 'cluster_sizes': [1, 1, 1, 1, 3, 1, 1, 1]}, 'convergence': 0.0}
2025-07-30 20:11:02,558 - __main__ - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-07-30 20:11:02,558 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment ---
2025-07-30 20:11:02,558 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-07-30 20:11:02,558 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 0/5

#### Population metrics
- Min cost: 0 → 0 (Δ 0)
- Mean cost: 0 → 0
- Diversity: 0.000 → 0.000 (Δ 0)

#### Elite metrics
- Elite count: 8 → 8
- Best elite cost: 9521 → 9521 (Δ 0)
- Elite diversity: 0.206 → 0.206 (Δ 0.0)

#### Strategy performance
- Explore: 0 inds, success 0% , avg Δ 0
- Exploit: 0 inds, success 0% , avg Δ 0

#### Other indicators
- No-change individuals: 0
- Regression individuals: 0
- Historical trends: N/A

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

### Output Example
```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.5, "exploit": 0.5},
  "balance_state": "balanced",
  "recommendations": ["maintain_current_ratio"]
}
```

2025-07-30 20:11:02,558 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-30 20:11:04,173 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 20:11:06,174 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 2/3)
2025-07-30 20:11:07,787 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 20:11:09,788 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 3/3)
2025-07-30 20:11:11,408 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 20:11:11,410 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: API请求失败: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 20:11:11,410 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-07-30 20:11:11,410 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-07-30 20:11:11,410 - __main__ - INFO - composite13_66 开始进化第 2 代
2025-07-30 20:11:11,410 - __main__ - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-07-30 20:11:11,410 - StatsExpert - INFO - 开始统计分析
2025-07-30 20:11:11,430 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 12311.0, 'max': 110608.0, 'mean': 69258.5, 'std': 39914.4015544515}, 'diversity': 0.9121212121212121, 'clusters': {'clusters': 8, 'cluster_sizes': [1, 1, 1, 1, 3, 1, 1, 1]}, 'convergence': 0.0}
2025-07-30 20:11:11,430 - PathExpert - INFO - 开始路径结构分析
2025-07-30 20:11:11,434 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-07-30 20:11:11,434 - PathExpert - INFO - 路径结构分析完成
2025-07-30 20:11:11,434 - EliteExpert - INFO - 开始精英解分析
2025-07-30 20:11:11,438 - EliteExpert - INFO - 精英解分析完成
2025-07-30 20:11:11,441 - LandscapeExpert - INFO - 开始景观分析
2025-07-30 20:11:11,442 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-30 20:11:11,442 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 1/5)
- population_size: 10
- cost_stats: min 12311.0, mean 69258.5, max 110608.0, std 39914.4015544515
- diversity: 0.9121212121212121
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Not available (Δ Not available, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {'common_edges': {'(38, 51)': 0.75}, 'common_edge_ratio': 0.015151515151515152}
- elite_diversity: {'diversity_score': 0.23106060606060608}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

### Output Example
```json
{
  "search_space_features": {"ruggedness": 0.5, "modality": "multi", "deceptiveness": "unknown"},
  "population_state": {"diversity": 0.5, "convergence": 0.4, "clustering": "weak"},
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "unknown",
  "evolution_direction": {"recommended_focus": "explore", "operators": []}
}
```

### Formatting Guidelines (read carefully, DO NOT output this section)
1. Do NOT copy or repeat any *_sample values above; at least 50% of elements in difficult_regions and opportunity_regions must differ from those samples.
2. Use [] (empty array) when a list has no content; never output the string "None" or a null value.
3. For search_space_features use enumerated strings only:
    • ruggedness: "low" | "medium" | "high"
    • modality: "mono" | "multi" | "unknown"
    • deceptiveness: "low" | "moderate" | "high" | "unknown"
4. diversity and convergence must be floats with exactly 3 decimal places (e.g. 0.742).
5. Follow the JSON schema strictly; no additional keys or commentary.

2025-07-30 20:11:11,443 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-30 20:11:13,059 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 20:11:15,063 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 2/3)
2025-07-30 20:11:16,674 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 20:11:18,674 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 3/3)
2025-07-30 20:11:20,267 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 20:11:20,269 - LandscapeExpert - INFO - LLM返回的分析结果: API请求失败: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 20:11:20,269 - LandscapeExpert - WARNING - 解析景观分析结果失败: Unable to extract valid JSON data from the response
2025-07-30 20:11:20,270 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-30 20:11:20,271 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.5, 'modality': 'unknown', 'deceptiveness': 'unknown'}, 'population_state': {'diversity': 0.5, 'convergence': 0.5, 'clustering': 'unknown'}, 'difficult_regions': [], 'opportunity_regions': [], 'evolution_phase': 'unknown', 'evolution_direction': {'recommended_focus': 'balance', 'operators': []}, 'raw_text': 'API请求失败: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ'}
2025-07-30 20:11:20,271 - __main__ - INFO - 开始策略分配阶段
2025-07-30 20:11:20,271 - StrategyExpert - INFO - 开始策略分配分析
2025-07-30 20:11:20,271 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 1
- Population: 10 individuals
  • diversity: 0.5
  • best_cost: 12311.0
  • mean_cost: 69258.5
- Search-space: ruggedness 0.5, modality unknown, deceptiveness unknown
- Evolution phase: unknown
- Landscape focus suggestion: balance
- Spatial density summary: None
- Elite diversity: N/A
- Difficult regions (# 0): None
- Opportunity regions (# 0): None
- Last-iteration feedback: API请求失败: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Formatting Guidelines (DO NOT output this section)
1. global_explore_ratio must be rounded to exactly 2 decimal places (e.g., 0.80).
2. The individual_assignments mapping MUST include every index from 0 to (population_size - 1) without omission.
3. Valid values are "explore" or "exploit" (lowercase).
4. rationale ≤ 40 words.

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-30 20:11:20,272 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-30 20:11:20,272 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-30 20:11:21,839 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 20:11:23,842 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 2/3)
2025-07-30 20:11:25,454 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 20:11:27,454 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 3/3)
2025-07-30 20:11:29,026 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 20:11:29,028 - StrategyExpert - INFO - LLM返回的策略分配结果: API请求失败: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 20:11:29,028 - StrategyExpert - WARNING - 解析策略分配结果时出错: Unable to extract valid JSON data from the response
2025-07-30 20:11:29,029 - StrategyExpert - INFO - 策略分配完成: ['explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-07-30 20:11:29,029 - __main__ - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-07-30 20:11:29,029 - __main__ - INFO - 策略分配完整报告: API请求失败: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 20:11:29,029 - __main__ - INFO - 策略分配阶段完成
2025-07-30 20:11:29,029 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-07-30 20:11:29,029 - __main__ - INFO - 策略分配完整报告: API请求失败: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 20:11:29,029 - __main__ - INFO - 开始进化阶段
2025-07-30 20:11:29,029 - __main__ - INFO - 为个体 0 生成探索路径
2025-07-30 20:11:29,029 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-07-30 20:11:29,029 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-07-30 20:11:29,029 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-07-30 20:11:29,029 - ExplorationExpert - INFO - 计算路径成本
2025-07-30 20:11:29,029 - ExplorationExpert - INFO - 探索路径生成完成，成本: 65258.0, 路径长度: 66
2025-07-30 20:11:29,029 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [60, 64, 61, 11, 53, 10, 14, 13, 9, 6, 56, 16, 36, 29, 31, 35, 22, 12, 27, 3, 20, 25, 37, 1, 57, 18, 32, 8, 5, 58, 17, 33, 26, 24, 48, 43, 50, 23, 40, 44, 15, 0, 54, 55, 39, 21, 49, 46, 47, 30, 4, 52, 2, 59, 63, 62, 41, 38, 51, 42, 34, 28, 19, 7, 65, 45], 'cur_cost': 65258.0}
2025-07-30 20:11:29,029 - __main__ - INFO - 为个体 1 生成利用路径
2025-07-30 20:11:29,029 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-30 20:11:29,029 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-30 20:11:29,029 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 103467.0
2025-07-30 20:11:29,103 - ExploitationExpert - INFO - res_population_num: 9
2025-07-30 20:11:29,103 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9526, 9538, 9544, 9570.0, 9521]
2025-07-30 20:11:29,104 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 47, 49, 40, 43, 48, 46, 45, 44, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 16, 18, 17, 12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 47, 49, 40, 43, 48, 46, 45, 44, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       18, 17, 12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-07-30 20:11:29,107 - ExploitationExpert - INFO - populations_num: 10
2025-07-30 20:11:29,107 - ExploitationExpert - INFO - populations: [{'tour': [60, 64, 61, 11, 53, 10, 14, 13, 9, 6, 56, 16, 36, 29, 31, 35, 22, 12, 27, 3, 20, 25, 37, 1, 57, 18, 32, 8, 5, 58, 17, 33, 26, 24, 48, 43, 50, 23, 40, 44, 15, 0, 54, 55, 39, 21, 49, 46, 47, 30, 4, 52, 2, 59, 63, 62, 41, 38, 51, 42, 34, 28, 19, 7, 65, 45], 'cur_cost': 65258.0}, {'tour': array([65, 32, 45, 63, 47, 40, 20, 39, 54, 56,  5, 16, 59, 24, 33, 21,  8,
       34,  6,  4, 10, 18, 53, 23, 57, 17,  1, 13, 11, 12, 26, 48,  0, 55,
       62, 28, 36, 46, 31, 50, 49, 43, 64, 44, 51, 58, 38, 52, 15, 19, 61,
       14, 30,  9, 22, 60, 41, 25, 37, 29, 42, 27,  2,  7,  3, 35],
      dtype=int64), 'cur_cost': 103467.0}, {'tour': [4, 18, 30, 52, 12, 6, 55, 2, 40, 22, 26, 43, 15, 27, 46, 25, 36, 42, 49, 47, 9, 62, 39, 3, 21, 31, 8, 24, 60, 61, 57, 58, 16, 20, 7, 5, 64, 38, 34, 0, 54, 48, 1, 14, 35, 33, 56, 29, 17, 50, 59, 19, 65, 53, 63, 41, 45, 23, 10, 32, 44, 28, 51, 13, 11, 37], 'cur_cost': 101456.0}, {'tour': array([48, 21, 41,  9, 32, 58,  7, 61, 40, 60, 65, 28, 52, 46, 14, 63, 55,
       50,  3, 31, 10,  2, 22, 54, 24, 47, 44, 29, 57, 56, 53, 49, 20, 39,
       42, 27,  5, 18, 38,  0, 13, 35, 64, 30, 33, 11,  1, 45, 34, 12, 37,
       59, 43, 26,  4,  8, 15, 19, 62, 17, 16, 51, 36, 23,  6, 25],
      dtype=int64), 'cur_cost': 110608.0}, {'tour': [0, 12, 8, 5, 4, 6, 2, 10, 1, 7, 3, 9, 11, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 16, 18, 17, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12845.0}, {'tour': array([ 8, 36, 25, 57, 64, 63,  4, 53, 28,  2, 16, 49, 44,  6, 14, 31, 18,
       27, 45, 58, 33, 29, 34, 30, 15, 20,  3,  5, 54, 39, 50, 40, 38, 11,
       65, 51, 42, 62, 56, 23, 37, 13, 22,  0, 41, 12, 21, 32, 19, 47, 17,
       46, 10, 24, 52, 26, 43, 59,  1,  7, 35, 60, 48,  9, 55, 61],
      dtype=int64), 'cur_cost': 91149.0}, {'tour': [0, 2, 4, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12311.0}, {'tour': array([28, 42, 18, 24, 34, 56, 20, 21, 31,  0,  4, 43, 62, 64, 54, 29, 45,
       57, 61, 58, 39,  6, 59, 49, 30,  7, 10, 12, 15, 25, 40, 48, 51, 44,
       47,  3, 19, 32, 50, 16,  1, 26,  8, 14, 23,  5, 22, 63, 17,  9, 55,
       65, 35,  2, 60, 53, 46, 13, 38, 27, 36, 37, 11, 41, 52, 33],
      dtype=int64), 'cur_cost': 92213.0}, {'tour': [0, 14, 20, 15, 22, 12, 17, 18, 16, 23, 13, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12332.0}, {'tour': array([23, 36,  1, 20, 41, 42, 62, 61, 64,  3, 45, 55, 34, 12, 21, 15, 22,
       51, 24, 37, 17, 28, 31, 44, 63,  9, 54, 57,  2, 27, 49,  8, 18, 29,
       11, 14, 33, 56, 39, 19,  7, 47,  0, 43, 46, 59, 48, 26, 13, 35, 60,
       30, 53,  5, 32, 50, 65, 52, 38, 25, 10,  4, 16, 58,  6, 40],
      dtype=int64), 'cur_cost': 105422.0}]
2025-07-30 20:11:29,109 - ExploitationExpert - INFO - 局部搜索耗时: 0.08秒
2025-07-30 20:11:29,109 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 6, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 6, 'cache_hits': 0, 'similarity_calculations': 15, 'cache_hit_rate': 0.0, 'cache_size': 15}}
2025-07-30 20:11:29,109 - __main__ - WARNING - 个体 1 的利用路径生成失败，保留原个体
2025-07-30 20:11:29,109 - __main__ - INFO - 为个体 2 生成探索路径
2025-07-30 20:11:29,109 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-07-30 20:11:29,109 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-07-30 20:11:29,114 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-07-30 20:11:29,114 - ExplorationExpert - INFO - 计算路径成本
2025-07-30 20:11:29,114 - ExplorationExpert - INFO - 探索路径生成完成，成本: 61810.0, 路径长度: 66
2025-07-30 20:11:29,114 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [50, 21, 34, 30, 29, 14, 17, 28, 22, 6, 23, 11, 64, 5, 65, 3, 2, 56, 0, 4, 16, 12, 13, 9, 63, 57, 53, 62, 7, 8, 27, 40, 43, 18, 36, 35, 15, 10, 58, 20, 48, 51, 45, 44, 38, 19, 25, 1, 37, 32, 33, 42, 47, 46, 24, 26, 39, 55, 61, 52, 54, 59, 49, 41, 31, 60], 'cur_cost': 61810.0}
2025-07-30 20:11:29,117 - __main__ - INFO - 为个体 3 生成利用路径
2025-07-30 20:11:29,117 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-30 20:11:29,117 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-30 20:11:29,117 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 99220.0
2025-07-30 20:11:29,197 - ExploitationExpert - INFO - res_population_num: 9
2025-07-30 20:11:29,198 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9526, 9538, 9544, 9570.0, 9521]
2025-07-30 20:11:29,198 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 47, 49, 40, 43, 48, 46, 45, 44, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 16, 18, 17, 12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 47, 49, 40, 43, 48, 46, 45, 44, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       18, 17, 12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-07-30 20:11:29,198 - ExploitationExpert - INFO - populations_num: 10
2025-07-30 20:11:29,198 - ExploitationExpert - INFO - populations: [{'tour': [60, 64, 61, 11, 53, 10, 14, 13, 9, 6, 56, 16, 36, 29, 31, 35, 22, 12, 27, 3, 20, 25, 37, 1, 57, 18, 32, 8, 5, 58, 17, 33, 26, 24, 48, 43, 50, 23, 40, 44, 15, 0, 54, 55, 39, 21, 49, 46, 47, 30, 4, 52, 2, 59, 63, 62, 41, 38, 51, 42, 34, 28, 19, 7, 65, 45], 'cur_cost': 65258.0}, {'tour': array([65, 32, 45, 63, 47, 40, 20, 39, 54, 56,  5, 16, 59, 24, 33, 21,  8,
       34,  6,  4, 10, 18, 53, 23, 57, 17,  1, 13, 11, 12, 26, 48,  0, 55,
       62, 28, 36, 46, 31, 50, 49, 43, 64, 44, 51, 58, 38, 52, 15, 19, 61,
       14, 30,  9, 22, 60, 41, 25, 37, 29, 42, 27,  2,  7,  3, 35],
      dtype=int64), 'cur_cost': 103467.0}, {'tour': [50, 21, 34, 30, 29, 14, 17, 28, 22, 6, 23, 11, 64, 5, 65, 3, 2, 56, 0, 4, 16, 12, 13, 9, 63, 57, 53, 62, 7, 8, 27, 40, 43, 18, 36, 35, 15, 10, 58, 20, 48, 51, 45, 44, 38, 19, 25, 1, 37, 32, 33, 42, 47, 46, 24, 26, 39, 55, 61, 52, 54, 59, 49, 41, 31, 60], 'cur_cost': 61810.0}, {'tour': array([16, 60, 50, 43, 52, 12, 36,  4,  9, 37, 23, 15, 35, 22, 24, 14, 42,
       17, 39, 57, 56, 40,  6, 10, 31, 30,  0, 29, 27, 34, 47, 49, 20, 51,
       59,  8,  5,  7, 38, 21, 44, 19, 53,  1, 55, 32, 48, 46, 61, 62, 63,
       26, 28,  3, 13, 64, 25,  2, 18, 58, 45, 65, 41, 33, 11, 54],
      dtype=int64), 'cur_cost': 99220.0}, {'tour': [0, 12, 8, 5, 4, 6, 2, 10, 1, 7, 3, 9, 11, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 23, 16, 18, 17, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12845.0}, {'tour': array([ 8, 36, 25, 57, 64, 63,  4, 53, 28,  2, 16, 49, 44,  6, 14, 31, 18,
       27, 45, 58, 33, 29, 34, 30, 15, 20,  3,  5, 54, 39, 50, 40, 38, 11,
       65, 51, 42, 62, 56, 23, 37, 13, 22,  0, 41, 12, 21, 32, 19, 47, 17,
       46, 10, 24, 52, 26, 43, 59,  1,  7, 35, 60, 48,  9, 55, 61],
      dtype=int64), 'cur_cost': 91149.0}, {'tour': [0, 2, 4, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12311.0}, {'tour': array([28, 42, 18, 24, 34, 56, 20, 21, 31,  0,  4, 43, 62, 64, 54, 29, 45,
       57, 61, 58, 39,  6, 59, 49, 30,  7, 10, 12, 15, 25, 40, 48, 51, 44,
       47,  3, 19, 32, 50, 16,  1, 26,  8, 14, 23,  5, 22, 63, 17,  9, 55,
       65, 35,  2, 60, 53, 46, 13, 38, 27, 36, 37, 11, 41, 52, 33],
      dtype=int64), 'cur_cost': 92213.0}, {'tour': [0, 14, 20, 15, 22, 12, 17, 18, 16, 23, 13, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12332.0}, {'tour': array([23, 36,  1, 20, 41, 42, 62, 61, 64,  3, 45, 55, 34, 12, 21, 15, 22,
       51, 24, 37, 17, 28, 31, 44, 63,  9, 54, 57,  2, 27, 49,  8, 18, 29,
       11, 14, 33, 56, 39, 19,  7, 47,  0, 43, 46, 59, 48, 26, 13, 35, 60,
       30, 53,  5, 32, 50, 65, 52, 38, 25, 10,  4, 16, 58,  6, 40],
      dtype=int64), 'cur_cost': 105422.0}]
2025-07-30 20:11:29,198 - ExploitationExpert - INFO - 局部搜索耗时: 0.08秒
2025-07-30 20:11:29,198 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 7, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 7, 'cache_hits': 0, 'similarity_calculations': 21, 'cache_hit_rate': 0.0, 'cache_size': 21}}
2025-07-30 20:11:29,198 - __main__ - WARNING - 个体 3 的利用路径生成失败，保留原个体
2025-07-30 20:11:29,198 - __main__ - INFO - 为个体 4 生成探索路径
2025-07-30 20:11:29,198 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-07-30 20:11:29,198 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-07-30 20:11:29,198 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-07-30 20:11:29,198 - ExplorationExpert - INFO - 计算路径成本
2025-07-30 20:11:29,198 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12910.0, 路径长度: 66
2025-07-30 20:11:29,198 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 11, 21, 10, 8, 2, 6, 4, 5, 9, 3, 7, 1, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12910.0}
2025-07-30 20:11:29,198 - __main__ - INFO - 为个体 5 生成利用路径
2025-07-30 20:11:29,198 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-30 20:11:29,198 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-30 20:11:29,198 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 106698.0
2025-07-30 20:11:29,265 - ExploitationExpert - INFO - res_population_num: 11
2025-07-30 20:11:29,265 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9526, 9538, 9544, 9570.0, 9521, 9521, 9521]
2025-07-30 20:11:29,265 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 47, 49, 40, 43, 48, 46, 45, 44, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 16, 18, 17, 12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 47, 49, 40, 43, 48, 46, 45, 44, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       18, 17, 12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-07-30 20:11:29,268 - ExploitationExpert - INFO - populations_num: 10
2025-07-30 20:11:29,269 - ExploitationExpert - INFO - populations: [{'tour': [60, 64, 61, 11, 53, 10, 14, 13, 9, 6, 56, 16, 36, 29, 31, 35, 22, 12, 27, 3, 20, 25, 37, 1, 57, 18, 32, 8, 5, 58, 17, 33, 26, 24, 48, 43, 50, 23, 40, 44, 15, 0, 54, 55, 39, 21, 49, 46, 47, 30, 4, 52, 2, 59, 63, 62, 41, 38, 51, 42, 34, 28, 19, 7, 65, 45], 'cur_cost': 65258.0}, {'tour': array([65, 32, 45, 63, 47, 40, 20, 39, 54, 56,  5, 16, 59, 24, 33, 21,  8,
       34,  6,  4, 10, 18, 53, 23, 57, 17,  1, 13, 11, 12, 26, 48,  0, 55,
       62, 28, 36, 46, 31, 50, 49, 43, 64, 44, 51, 58, 38, 52, 15, 19, 61,
       14, 30,  9, 22, 60, 41, 25, 37, 29, 42, 27,  2,  7,  3, 35],
      dtype=int64), 'cur_cost': 103467.0}, {'tour': [50, 21, 34, 30, 29, 14, 17, 28, 22, 6, 23, 11, 64, 5, 65, 3, 2, 56, 0, 4, 16, 12, 13, 9, 63, 57, 53, 62, 7, 8, 27, 40, 43, 18, 36, 35, 15, 10, 58, 20, 48, 51, 45, 44, 38, 19, 25, 1, 37, 32, 33, 42, 47, 46, 24, 26, 39, 55, 61, 52, 54, 59, 49, 41, 31, 60], 'cur_cost': 61810.0}, {'tour': array([16, 60, 50, 43, 52, 12, 36,  4,  9, 37, 23, 15, 35, 22, 24, 14, 42,
       17, 39, 57, 56, 40,  6, 10, 31, 30,  0, 29, 27, 34, 47, 49, 20, 51,
       59,  8,  5,  7, 38, 21, 44, 19, 53,  1, 55, 32, 48, 46, 61, 62, 63,
       26, 28,  3, 13, 64, 25,  2, 18, 58, 45, 65, 41, 33, 11, 54],
      dtype=int64), 'cur_cost': 99220.0}, {'tour': [0, 11, 21, 10, 8, 2, 6, 4, 5, 9, 3, 7, 1, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12910.0}, {'tour': array([24, 56,  0, 29, 42, 39, 25, 57, 40, 28, 21, 61, 23,  1, 20, 58, 47,
       65, 59, 36, 38, 27, 19, 53,  7, 46, 64, 44, 14,  3, 30, 32,  4, 63,
       49, 51,  9, 33, 26,  8, 54, 48, 11, 55, 13, 12, 18,  2, 62, 16, 10,
       60,  6, 43, 50, 45, 37, 41, 17, 35, 22, 15, 52, 31,  5, 34],
      dtype=int64), 'cur_cost': 106698.0}, {'tour': [0, 2, 4, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 8, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12311.0}, {'tour': array([28, 42, 18, 24, 34, 56, 20, 21, 31,  0,  4, 43, 62, 64, 54, 29, 45,
       57, 61, 58, 39,  6, 59, 49, 30,  7, 10, 12, 15, 25, 40, 48, 51, 44,
       47,  3, 19, 32, 50, 16,  1, 26,  8, 14, 23,  5, 22, 63, 17,  9, 55,
       65, 35,  2, 60, 53, 46, 13, 38, 27, 36, 37, 11, 41, 52, 33],
      dtype=int64), 'cur_cost': 92213.0}, {'tour': [0, 14, 20, 15, 22, 12, 17, 18, 16, 23, 13, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12332.0}, {'tour': array([23, 36,  1, 20, 41, 42, 62, 61, 64,  3, 45, 55, 34, 12, 21, 15, 22,
       51, 24, 37, 17, 28, 31, 44, 63,  9, 54, 57,  2, 27, 49,  8, 18, 29,
       11, 14, 33, 56, 39, 19,  7, 47,  0, 43, 46, 59, 48, 26, 13, 35, 60,
       30, 53,  5, 32, 50, 65, 52, 38, 25, 10,  4, 16, 58,  6, 40],
      dtype=int64), 'cur_cost': 105422.0}]
2025-07-30 20:11:29,270 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒
2025-07-30 20:11:29,270 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 8, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 8, 'cache_hits': 0, 'similarity_calculations': 28, 'cache_hit_rate': 0.0, 'cache_size': 28}}
2025-07-30 20:11:29,271 - __main__ - WARNING - 个体 5 的利用路径生成失败，保留原个体
2025-07-30 20:11:29,271 - __main__ - INFO - 为个体 6 生成探索路径
2025-07-30 20:11:29,271 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-07-30 20:11:29,271 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-07-30 20:11:29,278 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-07-30 20:11:29,278 - ExplorationExpert - INFO - 计算路径成本
2025-07-30 20:11:29,278 - ExplorationExpert - INFO - 探索路径生成完成，成本: 56266.0, 路径长度: 66
2025-07-30 20:11:29,278 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [64, 7, 17, 1, 16, 11, 2, 20, 6, 10, 58, 54, 23, 28, 32, 26, 14, 22, 18, 40, 21, 49, 13, 36, 0, 15, 31, 9, 19, 29, 24, 25, 4, 3, 37, 33, 43, 50, 39, 27, 12, 30, 38, 48, 44, 51, 42, 47, 46, 55, 61, 53, 60, 56, 63, 62, 59, 8, 65, 57, 52, 5, 34, 35, 45, 41], 'cur_cost': 56266.0}
2025-07-30 20:11:29,278 - __main__ - INFO - 为个体 7 生成利用路径
2025-07-30 20:11:29,279 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-30 20:11:29,279 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-30 20:11:29,279 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 107665.0
2025-07-30 20:11:29,345 - ExploitationExpert - INFO - res_population_num: 12
2025-07-30 20:11:29,345 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9526, 9538, 9544, 9570.0, 9521, 9521, 9521, 9521]
2025-07-30 20:11:29,345 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 47, 49, 40, 43, 48, 46, 45, 44, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 16, 18, 17, 12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 47, 49, 40, 43, 48, 46, 45, 44, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       18, 17, 12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-07-30 20:11:29,349 - ExploitationExpert - INFO - populations_num: 10
2025-07-30 20:11:29,349 - ExploitationExpert - INFO - populations: [{'tour': [60, 64, 61, 11, 53, 10, 14, 13, 9, 6, 56, 16, 36, 29, 31, 35, 22, 12, 27, 3, 20, 25, 37, 1, 57, 18, 32, 8, 5, 58, 17, 33, 26, 24, 48, 43, 50, 23, 40, 44, 15, 0, 54, 55, 39, 21, 49, 46, 47, 30, 4, 52, 2, 59, 63, 62, 41, 38, 51, 42, 34, 28, 19, 7, 65, 45], 'cur_cost': 65258.0}, {'tour': array([65, 32, 45, 63, 47, 40, 20, 39, 54, 56,  5, 16, 59, 24, 33, 21,  8,
       34,  6,  4, 10, 18, 53, 23, 57, 17,  1, 13, 11, 12, 26, 48,  0, 55,
       62, 28, 36, 46, 31, 50, 49, 43, 64, 44, 51, 58, 38, 52, 15, 19, 61,
       14, 30,  9, 22, 60, 41, 25, 37, 29, 42, 27,  2,  7,  3, 35],
      dtype=int64), 'cur_cost': 103467.0}, {'tour': [50, 21, 34, 30, 29, 14, 17, 28, 22, 6, 23, 11, 64, 5, 65, 3, 2, 56, 0, 4, 16, 12, 13, 9, 63, 57, 53, 62, 7, 8, 27, 40, 43, 18, 36, 35, 15, 10, 58, 20, 48, 51, 45, 44, 38, 19, 25, 1, 37, 32, 33, 42, 47, 46, 24, 26, 39, 55, 61, 52, 54, 59, 49, 41, 31, 60], 'cur_cost': 61810.0}, {'tour': array([16, 60, 50, 43, 52, 12, 36,  4,  9, 37, 23, 15, 35, 22, 24, 14, 42,
       17, 39, 57, 56, 40,  6, 10, 31, 30,  0, 29, 27, 34, 47, 49, 20, 51,
       59,  8,  5,  7, 38, 21, 44, 19, 53,  1, 55, 32, 48, 46, 61, 62, 63,
       26, 28,  3, 13, 64, 25,  2, 18, 58, 45, 65, 41, 33, 11, 54],
      dtype=int64), 'cur_cost': 99220.0}, {'tour': [0, 11, 21, 10, 8, 2, 6, 4, 5, 9, 3, 7, 1, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12910.0}, {'tour': array([24, 56,  0, 29, 42, 39, 25, 57, 40, 28, 21, 61, 23,  1, 20, 58, 47,
       65, 59, 36, 38, 27, 19, 53,  7, 46, 64, 44, 14,  3, 30, 32,  4, 63,
       49, 51,  9, 33, 26,  8, 54, 48, 11, 55, 13, 12, 18,  2, 62, 16, 10,
       60,  6, 43, 50, 45, 37, 41, 17, 35, 22, 15, 52, 31,  5, 34],
      dtype=int64), 'cur_cost': 106698.0}, {'tour': [64, 7, 17, 1, 16, 11, 2, 20, 6, 10, 58, 54, 23, 28, 32, 26, 14, 22, 18, 40, 21, 49, 13, 36, 0, 15, 31, 9, 19, 29, 24, 25, 4, 3, 37, 33, 43, 50, 39, 27, 12, 30, 38, 48, 44, 51, 42, 47, 46, 55, 61, 53, 60, 56, 63, 62, 59, 8, 65, 57, 52, 5, 34, 35, 45, 41], 'cur_cost': 56266.0}, {'tour': array([62, 24, 45, 49, 52, 56, 21, 44,  1, 14, 43, 51, 38, 54, 26,  6, 31,
       18, 17, 36, 59,  2, 41, 58,  3, 25, 22, 27, 53, 20, 16, 11, 29, 50,
       28, 19, 23, 46, 33,  5, 30, 48, 12,  4, 15, 13, 37, 57, 32, 10, 64,
        7, 63, 60, 35, 55, 39, 65, 47, 40, 34,  8,  9, 42,  0, 61],
      dtype=int64), 'cur_cost': 107665.0}, {'tour': [0, 14, 20, 15, 22, 12, 17, 18, 16, 23, 13, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12332.0}, {'tour': array([23, 36,  1, 20, 41, 42, 62, 61, 64,  3, 45, 55, 34, 12, 21, 15, 22,
       51, 24, 37, 17, 28, 31, 44, 63,  9, 54, 57,  2, 27, 49,  8, 18, 29,
       11, 14, 33, 56, 39, 19,  7, 47,  0, 43, 46, 59, 48, 26, 13, 35, 60,
       30, 53,  5, 32, 50, 65, 52, 38, 25, 10,  4, 16, 58,  6, 40],
      dtype=int64), 'cur_cost': 105422.0}]
2025-07-30 20:11:29,351 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒
2025-07-30 20:11:29,351 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 9, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 9, 'cache_hits': 0, 'similarity_calculations': 36, 'cache_hit_rate': 0.0, 'cache_size': 36}}
2025-07-30 20:11:29,351 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-07-30 20:11:29,351 - __main__ - INFO - 为个体 8 生成探索路径
2025-07-30 20:11:29,351 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-07-30 20:11:29,351 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-07-30 20:11:29,359 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-07-30 20:11:29,359 - ExplorationExpert - INFO - 计算路径成本
2025-07-30 20:11:29,359 - ExplorationExpert - INFO - 探索路径生成完成，成本: 48285.0, 路径长度: 66
2025-07-30 20:11:29,359 - __main__ - INFO - 个体 8 探索路径生成报告: {'new_tour': [62, 64, 3, 54, 52, 58, 0, 4, 8, 22, 37, 33, 1, 7, 55, 15, 24, 25, 6, 60, 11, 61, 39, 51, 43, 23, 36, 20, 26, 14, 27, 2, 59, 65, 57, 53, 56, 21, 19, 29, 30, 28, 35, 13, 16, 12, 9, 10, 18, 49, 45, 46, 44, 38, 40, 47, 17, 31, 34, 32, 48, 50, 42, 41, 5, 63], 'cur_cost': 48285.0}
2025-07-30 20:11:29,359 - __main__ - INFO - 为个体 9 生成利用路径
2025-07-30 20:11:29,360 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-30 20:11:29,360 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-30 20:11:29,360 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 112870.0
2025-07-30 20:11:29,431 - ExploitationExpert - INFO - res_population_num: 16
2025-07-30 20:11:29,431 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9526, 9538, 9544, 9570.0, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521]
2025-07-30 20:11:29,431 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 47, 49, 40, 43, 48, 46, 45, 44, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 16, 18, 17, 12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 47, 49, 40, 43, 48, 46, 45, 44, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       18, 17, 12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-07-30 20:11:29,447 - ExploitationExpert - INFO - populations_num: 10
2025-07-30 20:11:29,447 - ExploitationExpert - INFO - populations: [{'tour': [60, 64, 61, 11, 53, 10, 14, 13, 9, 6, 56, 16, 36, 29, 31, 35, 22, 12, 27, 3, 20, 25, 37, 1, 57, 18, 32, 8, 5, 58, 17, 33, 26, 24, 48, 43, 50, 23, 40, 44, 15, 0, 54, 55, 39, 21, 49, 46, 47, 30, 4, 52, 2, 59, 63, 62, 41, 38, 51, 42, 34, 28, 19, 7, 65, 45], 'cur_cost': 65258.0}, {'tour': array([65, 32, 45, 63, 47, 40, 20, 39, 54, 56,  5, 16, 59, 24, 33, 21,  8,
       34,  6,  4, 10, 18, 53, 23, 57, 17,  1, 13, 11, 12, 26, 48,  0, 55,
       62, 28, 36, 46, 31, 50, 49, 43, 64, 44, 51, 58, 38, 52, 15, 19, 61,
       14, 30,  9, 22, 60, 41, 25, 37, 29, 42, 27,  2,  7,  3, 35],
      dtype=int64), 'cur_cost': 103467.0}, {'tour': [50, 21, 34, 30, 29, 14, 17, 28, 22, 6, 23, 11, 64, 5, 65, 3, 2, 56, 0, 4, 16, 12, 13, 9, 63, 57, 53, 62, 7, 8, 27, 40, 43, 18, 36, 35, 15, 10, 58, 20, 48, 51, 45, 44, 38, 19, 25, 1, 37, 32, 33, 42, 47, 46, 24, 26, 39, 55, 61, 52, 54, 59, 49, 41, 31, 60], 'cur_cost': 61810.0}, {'tour': array([16, 60, 50, 43, 52, 12, 36,  4,  9, 37, 23, 15, 35, 22, 24, 14, 42,
       17, 39, 57, 56, 40,  6, 10, 31, 30,  0, 29, 27, 34, 47, 49, 20, 51,
       59,  8,  5,  7, 38, 21, 44, 19, 53,  1, 55, 32, 48, 46, 61, 62, 63,
       26, 28,  3, 13, 64, 25,  2, 18, 58, 45, 65, 41, 33, 11, 54],
      dtype=int64), 'cur_cost': 99220.0}, {'tour': [0, 11, 21, 10, 8, 2, 6, 4, 5, 9, 3, 7, 1, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12910.0}, {'tour': array([24, 56,  0, 29, 42, 39, 25, 57, 40, 28, 21, 61, 23,  1, 20, 58, 47,
       65, 59, 36, 38, 27, 19, 53,  7, 46, 64, 44, 14,  3, 30, 32,  4, 63,
       49, 51,  9, 33, 26,  8, 54, 48, 11, 55, 13, 12, 18,  2, 62, 16, 10,
       60,  6, 43, 50, 45, 37, 41, 17, 35, 22, 15, 52, 31,  5, 34],
      dtype=int64), 'cur_cost': 106698.0}, {'tour': [64, 7, 17, 1, 16, 11, 2, 20, 6, 10, 58, 54, 23, 28, 32, 26, 14, 22, 18, 40, 21, 49, 13, 36, 0, 15, 31, 9, 19, 29, 24, 25, 4, 3, 37, 33, 43, 50, 39, 27, 12, 30, 38, 48, 44, 51, 42, 47, 46, 55, 61, 53, 60, 56, 63, 62, 59, 8, 65, 57, 52, 5, 34, 35, 45, 41], 'cur_cost': 56266.0}, {'tour': array([62, 24, 45, 49, 52, 56, 21, 44,  1, 14, 43, 51, 38, 54, 26,  6, 31,
       18, 17, 36, 59,  2, 41, 58,  3, 25, 22, 27, 53, 20, 16, 11, 29, 50,
       28, 19, 23, 46, 33,  5, 30, 48, 12,  4, 15, 13, 37, 57, 32, 10, 64,
        7, 63, 60, 35, 55, 39, 65, 47, 40, 34,  8,  9, 42,  0, 61],
      dtype=int64), 'cur_cost': 107665.0}, {'tour': [62, 64, 3, 54, 52, 58, 0, 4, 8, 22, 37, 33, 1, 7, 55, 15, 24, 25, 6, 60, 11, 61, 39, 51, 43, 23, 36, 20, 26, 14, 27, 2, 59, 65, 57, 53, 56, 21, 19, 29, 30, 28, 35, 13, 16, 12, 9, 10, 18, 49, 45, 46, 44, 38, 40, 47, 17, 31, 34, 32, 48, 50, 42, 41, 5, 63], 'cur_cost': 48285.0}, {'tour': array([59,  7, 55, 12, 31, 10, 27, 60, 38, 33, 22, 50, 23, 64, 30, 41, 24,
       36, 29, 56, 43, 37, 46, 63, 14, 57, 35, 39, 49, 51,  5, 44,  0, 58,
       32, 62, 42, 52, 13, 15, 28,  2,  4, 47, 21, 40, 20, 16, 45, 61, 11,
       34, 18, 17,  6,  9, 48,  1, 26, 54,  8, 19,  3, 25, 65, 53],
      dtype=int64), 'cur_cost': 112870.0}]
2025-07-30 20:11:29,447 - ExploitationExpert - INFO - 局部搜索耗时: 0.09秒
2025-07-30 20:11:29,447 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 10, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 10, 'cache_hits': 0, 'similarity_calculations': 45, 'cache_hit_rate': 0.0, 'cache_size': 45}}
2025-07-30 20:11:29,447 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-07-30 20:11:29,447 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [60, 64, 61, 11, 53, 10, 14, 13, 9, 6, 56, 16, 36, 29, 31, 35, 22, 12, 27, 3, 20, 25, 37, 1, 57, 18, 32, 8, 5, 58, 17, 33, 26, 24, 48, 43, 50, 23, 40, 44, 15, 0, 54, 55, 39, 21, 49, 46, 47, 30, 4, 52, 2, 59, 63, 62, 41, 38, 51, 42, 34, 28, 19, 7, 65, 45], 'cur_cost': 65258.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [50, 21, 34, 30, 29, 14, 17, 28, 22, 6, 23, 11, 64, 5, 65, 3, 2, 56, 0, 4, 16, 12, 13, 9, 63, 57, 53, 62, 7, 8, 27, 40, 43, 18, 36, 35, 15, 10, 58, 20, 48, 51, 45, 44, 38, 19, 25, 1, 37, 32, 33, 42, 47, 46, 24, 26, 39, 55, 61, 52, 54, 59, 49, 41, 31, 60], 'cur_cost': 61810.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 11, 21, 10, 8, 2, 6, 4, 5, 9, 3, 7, 1, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12910.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [64, 7, 17, 1, 16, 11, 2, 20, 6, 10, 58, 54, 23, 28, 32, 26, 14, 22, 18, 40, 21, 49, 13, 36, 0, 15, 31, 9, 19, 29, 24, 25, 4, 3, 37, 33, 43, 50, 39, 27, 12, 30, 38, 48, 44, 51, 42, 47, 46, 55, 61, 53, 60, 56, 63, 62, 59, 8, 65, 57, 52, 5, 34, 35, 45, 41], 'cur_cost': 56266.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [62, 64, 3, 54, 52, 58, 0, 4, 8, 22, 37, 33, 1, 7, 55, 15, 24, 25, 6, 60, 11, 61, 39, 51, 43, 23, 36, 20, 26, 14, 27, 2, 59, 65, 57, 53, 56, 21, 19, 29, 30, 28, 35, 13, 16, 12, 9, 10, 18, 49, 45, 46, 44, 38, 40, 47, 17, 31, 34, 32, 48, 50, 42, 41, 5, 63], 'cur_cost': 48285.0}}]
2025-07-30 20:11:29,447 - __main__ - INFO - 进化阶段完成
2025-07-30 20:11:29,447 - StatsExpert - INFO - 开始统计分析
2025-07-30 20:11:29,474 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 12910.0, 'max': 112870.0, 'mean': 77444.9, 'std': 31673.033013748463}, 'diversity': 0.9538720538720539, 'clusters': {'clusters': 10, 'cluster_sizes': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-30 20:11:29,475 - __main__ - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-07-30 20:11:29,475 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment ---
2025-07-30 20:11:29,476 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-07-30 20:11:29,476 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 1/5

#### Population metrics
- Min cost: 0 → 0 (Δ 0)
- Mean cost: 0 → 0
- Diversity: 0.000 → 0.000 (Δ 0)

#### Elite metrics
- Elite count: 16 → 16
- Best elite cost: 9521 → 9521 (Δ 0)
- Elite diversity: 0.106 → 0.106 (Δ 0.0)

#### Strategy performance
- Explore: 0 inds, success 0% , avg Δ 0
- Exploit: 0 inds, success 0% , avg Δ 0

#### Other indicators
- No-change individuals: 0
- Regression individuals: 0
- Historical trends: N/A

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

### Output Example
```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.5, "exploit": 0.5},
  "balance_state": "balanced",
  "recommendations": ["maintain_current_ratio"]
}
```

2025-07-30 20:11:29,476 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-30 20:11:31,042 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 20:11:33,044 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 2/3)
2025-07-30 20:11:35,767 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 20:11:37,768 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 3/3)
2025-07-30 20:11:39,303 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 20:11:39,303 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: API请求失败: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 20:11:39,303 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-07-30 20:11:39,303 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-07-30 20:11:39,303 - __main__ - INFO - composite13_66 开始进化第 3 代
2025-07-30 20:11:39,303 - __main__ - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-07-30 20:11:39,303 - StatsExpert - INFO - 开始统计分析
2025-07-30 20:11:39,326 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 12910.0, 'max': 112870.0, 'mean': 77444.9, 'std': 31673.033013748463}, 'diversity': 0.9538720538720539, 'clusters': {'clusters': 10, 'cluster_sizes': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-30 20:11:39,327 - PathExpert - INFO - 开始路径结构分析
2025-07-30 20:11:39,328 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-07-30 20:11:39,328 - PathExpert - INFO - 路径结构分析完成
2025-07-30 20:11:39,328 - EliteExpert - INFO - 开始精英解分析
2025-07-30 20:11:39,337 - EliteExpert - INFO - 精英解分析完成
2025-07-30 20:11:39,339 - LandscapeExpert - INFO - 开始景观分析
2025-07-30 20:11:39,341 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-30 20:11:39,341 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 2/5)
- population_size: 10
- cost_stats: min 12910.0, mean 77444.9, max 112870.0, std 31673.033013748463
- diversity: 0.9538720538720539
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Not available (Δ Not available, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {'common_edges': {}, 'common_edge_ratio': 0.0}
- elite_diversity: {'diversity_score': 0.2141414141414142}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

### Output Example
```json
{
  "search_space_features": {"ruggedness": 0.5, "modality": "multi", "deceptiveness": "unknown"},
  "population_state": {"diversity": 0.5, "convergence": 0.4, "clustering": "weak"},
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "unknown",
  "evolution_direction": {"recommended_focus": "explore", "operators": []}
}
```

### Formatting Guidelines (read carefully, DO NOT output this section)
1. Do NOT copy or repeat any *_sample values above; at least 50% of elements in difficult_regions and opportunity_regions must differ from those samples.
2. Use [] (empty array) when a list has no content; never output the string "None" or a null value.
3. For search_space_features use enumerated strings only:
    • ruggedness: "low" | "medium" | "high"
    • modality: "mono" | "multi" | "unknown"
    • deceptiveness: "low" | "moderate" | "high" | "unknown"
4. diversity and convergence must be floats with exactly 3 decimal places (e.g. 0.742).
5. Follow the JSON schema strictly; no additional keys or commentary.

2025-07-30 20:11:39,341 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-30 20:11:40,957 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 20:11:42,959 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 2/3)
2025-07-30 20:11:44,518 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 20:11:46,519 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 3/3)
2025-07-30 20:11:48,100 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 20:11:48,100 - LandscapeExpert - INFO - LLM返回的分析结果: API请求失败: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 20:11:48,101 - LandscapeExpert - WARNING - 解析景观分析结果失败: Unable to extract valid JSON data from the response
2025-07-30 20:11:48,101 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-30 20:11:48,101 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.5, 'modality': 'unknown', 'deceptiveness': 'unknown'}, 'population_state': {'diversity': 0.5, 'convergence': 0.5, 'clustering': 'unknown'}, 'difficult_regions': [], 'opportunity_regions': [], 'evolution_phase': 'unknown', 'evolution_direction': {'recommended_focus': 'balance', 'operators': []}, 'raw_text': 'API请求失败: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ'}
2025-07-30 20:11:48,101 - __main__ - INFO - 开始策略分配阶段
2025-07-30 20:11:48,102 - StrategyExpert - INFO - 开始策略分配分析
2025-07-30 20:11:48,102 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 2
- Population: 10 individuals
  • diversity: 0.5
  • best_cost: 12910.0
  • mean_cost: 77444.9
- Search-space: ruggedness 0.5, modality unknown, deceptiveness unknown
- Evolution phase: unknown
- Landscape focus suggestion: balance
- Spatial density summary: None
- Elite diversity: N/A
- Difficult regions (# 0): None
- Opportunity regions (# 0): None
- Last-iteration feedback: API请求失败: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Formatting Guidelines (DO NOT output this section)
1. global_explore_ratio must be rounded to exactly 2 decimal places (e.g., 0.80).
2. The individual_assignments mapping MUST include every index from 0 to (population_size - 1) without omission.
3. Valid values are "explore" or "exploit" (lowercase).
4. rationale ≤ 40 words.

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-30 20:11:48,102 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-30 20:11:48,102 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-30 20:11:49,640 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 20:11:51,641 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 2/3)
2025-07-30 20:11:53,215 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 20:11:55,216 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 3/3)
2025-07-30 20:11:56,829 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 20:11:56,831 - StrategyExpert - INFO - LLM返回的策略分配结果: API请求失败: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 20:11:56,831 - StrategyExpert - WARNING - 解析策略分配结果时出错: Unable to extract valid JSON data from the response
2025-07-30 20:11:56,831 - StrategyExpert - INFO - 策略分配完成: ['explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-07-30 20:11:56,831 - __main__ - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-07-30 20:11:56,831 - __main__ - INFO - 策略分配完整报告: API请求失败: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 20:11:56,831 - __main__ - INFO - 策略分配阶段完成
2025-07-30 20:11:56,831 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-07-30 20:11:56,831 - __main__ - INFO - 策略分配完整报告: API请求失败: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 20:11:56,831 - __main__ - INFO - 开始进化阶段
2025-07-30 20:11:56,831 - __main__ - INFO - 为个体 0 生成探索路径
2025-07-30 20:11:56,831 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-07-30 20:11:56,831 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-07-30 20:11:56,831 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-07-30 20:11:56,831 - ExplorationExpert - INFO - 计算路径成本
2025-07-30 20:11:56,831 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12812.0, 路径长度: 66
2025-07-30 20:11:56,831 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 22, 20, 6, 2, 8, 5, 4, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 12, 17, 18, 16, 23, 13, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12812.0}
2025-07-30 20:11:56,831 - __main__ - INFO - 为个体 1 生成利用路径
2025-07-30 20:11:56,831 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-30 20:11:56,831 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-30 20:11:56,831 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 105058.0
2025-07-30 20:11:56,899 - ExploitationExpert - INFO - res_population_num: 17
2025-07-30 20:11:56,899 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9526, 9538, 9544, 9570.0, 9521]
2025-07-30 20:11:56,899 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 47, 49, 40, 43, 48, 46, 45, 44, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 16, 18, 17, 12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 47, 49, 40, 43, 48, 46, 45, 44, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       18, 17, 12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-07-30 20:11:56,899 - ExploitationExpert - INFO - populations_num: 10
2025-07-30 20:11:56,899 - ExploitationExpert - INFO - populations: [{'tour': [0, 22, 20, 6, 2, 8, 5, 4, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 12, 17, 18, 16, 23, 13, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12812.0}, {'tour': array([55, 27,  5, 30, 24, 46, 31, 38, 37, 48, 57, 18, 40, 26, 20, 51, 16,
        1, 33,  2, 62, 23, 61, 50, 36,  3,  7, 19, 39,  8, 22, 63, 15, 42,
       29,  0, 13, 34, 12, 21, 49, 45, 56, 28, 32, 11, 52,  4, 60, 53, 64,
       65, 54, 10, 43, 25, 44,  6, 17, 41, 14,  9, 35, 59, 47, 58],
      dtype=int64), 'cur_cost': 105058.0}, {'tour': [50, 21, 34, 30, 29, 14, 17, 28, 22, 6, 23, 11, 64, 5, 65, 3, 2, 56, 0, 4, 16, 12, 13, 9, 63, 57, 53, 62, 7, 8, 27, 40, 43, 18, 36, 35, 15, 10, 58, 20, 48, 51, 45, 44, 38, 19, 25, 1, 37, 32, 33, 42, 47, 46, 24, 26, 39, 55, 61, 52, 54, 59, 49, 41, 31, 60], 'cur_cost': 61810.0}, {'tour': array([16, 60, 50, 43, 52, 12, 36,  4,  9, 37, 23, 15, 35, 22, 24, 14, 42,
       17, 39, 57, 56, 40,  6, 10, 31, 30,  0, 29, 27, 34, 47, 49, 20, 51,
       59,  8,  5,  7, 38, 21, 44, 19, 53,  1, 55, 32, 48, 46, 61, 62, 63,
       26, 28,  3, 13, 64, 25,  2, 18, 58, 45, 65, 41, 33, 11, 54],
      dtype=int64), 'cur_cost': 99220.0}, {'tour': [0, 11, 21, 10, 8, 2, 6, 4, 5, 9, 3, 7, 1, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12910.0}, {'tour': array([24, 56,  0, 29, 42, 39, 25, 57, 40, 28, 21, 61, 23,  1, 20, 58, 47,
       65, 59, 36, 38, 27, 19, 53,  7, 46, 64, 44, 14,  3, 30, 32,  4, 63,
       49, 51,  9, 33, 26,  8, 54, 48, 11, 55, 13, 12, 18,  2, 62, 16, 10,
       60,  6, 43, 50, 45, 37, 41, 17, 35, 22, 15, 52, 31,  5, 34],
      dtype=int64), 'cur_cost': 106698.0}, {'tour': [64, 7, 17, 1, 16, 11, 2, 20, 6, 10, 58, 54, 23, 28, 32, 26, 14, 22, 18, 40, 21, 49, 13, 36, 0, 15, 31, 9, 19, 29, 24, 25, 4, 3, 37, 33, 43, 50, 39, 27, 12, 30, 38, 48, 44, 51, 42, 47, 46, 55, 61, 53, 60, 56, 63, 62, 59, 8, 65, 57, 52, 5, 34, 35, 45, 41], 'cur_cost': 56266.0}, {'tour': array([62, 24, 45, 49, 52, 56, 21, 44,  1, 14, 43, 51, 38, 54, 26,  6, 31,
       18, 17, 36, 59,  2, 41, 58,  3, 25, 22, 27, 53, 20, 16, 11, 29, 50,
       28, 19, 23, 46, 33,  5, 30, 48, 12,  4, 15, 13, 37, 57, 32, 10, 64,
        7, 63, 60, 35, 55, 39, 65, 47, 40, 34,  8,  9, 42,  0, 61],
      dtype=int64), 'cur_cost': 107665.0}, {'tour': [62, 64, 3, 54, 52, 58, 0, 4, 8, 22, 37, 33, 1, 7, 55, 15, 24, 25, 6, 60, 11, 61, 39, 51, 43, 23, 36, 20, 26, 14, 27, 2, 59, 65, 57, 53, 56, 21, 19, 29, 30, 28, 35, 13, 16, 12, 9, 10, 18, 49, 45, 46, 44, 38, 40, 47, 17, 31, 34, 32, 48, 50, 42, 41, 5, 63], 'cur_cost': 48285.0}, {'tour': array([59,  7, 55, 12, 31, 10, 27, 60, 38, 33, 22, 50, 23, 64, 30, 41, 24,
       36, 29, 56, 43, 37, 46, 63, 14, 57, 35, 39, 49, 51,  5, 44,  0, 58,
       32, 62, 42, 52, 13, 15, 28,  2,  4, 47, 21, 40, 20, 16, 45, 61, 11,
       34, 18, 17,  6,  9, 48,  1, 26, 54,  8, 19,  3, 25, 65, 53],
      dtype=int64), 'cur_cost': 112870.0}]
2025-07-30 20:11:56,899 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒
2025-07-30 20:11:56,899 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 11, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 11, 'cache_hits': 0, 'similarity_calculations': 55, 'cache_hit_rate': 0.0, 'cache_size': 55}}
2025-07-30 20:11:56,899 - __main__ - WARNING - 个体 1 的利用路径生成失败，保留原个体
2025-07-30 20:11:56,899 - __main__ - INFO - 为个体 2 生成探索路径
2025-07-30 20:11:56,899 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-07-30 20:11:56,899 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-07-30 20:11:56,899 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-07-30 20:11:56,899 - ExplorationExpert - INFO - 计算路径成本
2025-07-30 20:11:56,899 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12430.0, 路径长度: 66
2025-07-30 20:11:56,899 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 5, 3, 13, 20, 21, 19, 16, 18, 12, 22, 23, 15, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 8, 2, 6, 4, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12430.0}
2025-07-30 20:11:56,899 - __main__ - INFO - 为个体 3 生成利用路径
2025-07-30 20:11:56,899 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-30 20:11:56,899 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-30 20:11:56,899 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 94630.0
2025-07-30 20:11:56,985 - ExploitationExpert - INFO - res_population_num: 20
2025-07-30 20:11:56,985 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9526, 9538, 9544, 9570.0, 9521, 9521, 9521, 9521]
2025-07-30 20:11:56,985 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 47, 49, 40, 43, 48, 46, 45, 44, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 16, 18, 17, 12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 47, 49, 40, 43, 48, 46, 45, 44, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       18, 17, 12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-07-30 20:11:56,985 - ExploitationExpert - INFO - populations_num: 10
2025-07-30 20:11:56,985 - ExploitationExpert - INFO - populations: [{'tour': [0, 22, 20, 6, 2, 8, 5, 4, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 12, 17, 18, 16, 23, 13, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12812.0}, {'tour': array([55, 27,  5, 30, 24, 46, 31, 38, 37, 48, 57, 18, 40, 26, 20, 51, 16,
        1, 33,  2, 62, 23, 61, 50, 36,  3,  7, 19, 39,  8, 22, 63, 15, 42,
       29,  0, 13, 34, 12, 21, 49, 45, 56, 28, 32, 11, 52,  4, 60, 53, 64,
       65, 54, 10, 43, 25, 44,  6, 17, 41, 14,  9, 35, 59, 47, 58],
      dtype=int64), 'cur_cost': 105058.0}, {'tour': [0, 5, 3, 13, 20, 21, 19, 16, 18, 12, 22, 23, 15, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 8, 2, 6, 4, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12430.0}, {'tour': array([ 1, 55, 27, 41, 40, 39, 57, 33, 31, 44, 19, 35, 16, 58,  5, 48, 43,
        8, 24, 29, 20, 13, 22, 30, 36, 26, 10, 52, 15,  4,  7, 46, 45, 42,
       47,  2, 11,  3, 32, 56, 59, 17, 61,  9, 37, 23, 21, 38, 65, 14, 54,
       49, 25, 18,  0, 62, 34, 64, 50, 53, 51, 28, 63, 60,  6, 12],
      dtype=int64), 'cur_cost': 94630.0}, {'tour': [0, 11, 21, 10, 8, 2, 6, 4, 5, 9, 3, 7, 1, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12910.0}, {'tour': array([24, 56,  0, 29, 42, 39, 25, 57, 40, 28, 21, 61, 23,  1, 20, 58, 47,
       65, 59, 36, 38, 27, 19, 53,  7, 46, 64, 44, 14,  3, 30, 32,  4, 63,
       49, 51,  9, 33, 26,  8, 54, 48, 11, 55, 13, 12, 18,  2, 62, 16, 10,
       60,  6, 43, 50, 45, 37, 41, 17, 35, 22, 15, 52, 31,  5, 34],
      dtype=int64), 'cur_cost': 106698.0}, {'tour': [64, 7, 17, 1, 16, 11, 2, 20, 6, 10, 58, 54, 23, 28, 32, 26, 14, 22, 18, 40, 21, 49, 13, 36, 0, 15, 31, 9, 19, 29, 24, 25, 4, 3, 37, 33, 43, 50, 39, 27, 12, 30, 38, 48, 44, 51, 42, 47, 46, 55, 61, 53, 60, 56, 63, 62, 59, 8, 65, 57, 52, 5, 34, 35, 45, 41], 'cur_cost': 56266.0}, {'tour': array([62, 24, 45, 49, 52, 56, 21, 44,  1, 14, 43, 51, 38, 54, 26,  6, 31,
       18, 17, 36, 59,  2, 41, 58,  3, 25, 22, 27, 53, 20, 16, 11, 29, 50,
       28, 19, 23, 46, 33,  5, 30, 48, 12,  4, 15, 13, 37, 57, 32, 10, 64,
        7, 63, 60, 35, 55, 39, 65, 47, 40, 34,  8,  9, 42,  0, 61],
      dtype=int64), 'cur_cost': 107665.0}, {'tour': [62, 64, 3, 54, 52, 58, 0, 4, 8, 22, 37, 33, 1, 7, 55, 15, 24, 25, 6, 60, 11, 61, 39, 51, 43, 23, 36, 20, 26, 14, 27, 2, 59, 65, 57, 53, 56, 21, 19, 29, 30, 28, 35, 13, 16, 12, 9, 10, 18, 49, 45, 46, 44, 38, 40, 47, 17, 31, 34, 32, 48, 50, 42, 41, 5, 63], 'cur_cost': 48285.0}, {'tour': array([59,  7, 55, 12, 31, 10, 27, 60, 38, 33, 22, 50, 23, 64, 30, 41, 24,
       36, 29, 56, 43, 37, 46, 63, 14, 57, 35, 39, 49, 51,  5, 44,  0, 58,
       32, 62, 42, 52, 13, 15, 28,  2,  4, 47, 21, 40, 20, 16, 45, 61, 11,
       34, 18, 17,  6,  9, 48,  1, 26, 54,  8, 19,  3, 25, 65, 53],
      dtype=int64), 'cur_cost': 112870.0}]
2025-07-30 20:11:56,985 - ExploitationExpert - INFO - 局部搜索耗时: 0.09秒
2025-07-30 20:11:56,985 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 12, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 12, 'cache_hits': 0, 'similarity_calculations': 66, 'cache_hit_rate': 0.0, 'cache_size': 66}}
2025-07-30 20:11:56,985 - __main__ - WARNING - 个体 3 的利用路径生成失败，保留原个体
2025-07-30 20:11:56,985 - __main__ - INFO - 为个体 4 生成探索路径
2025-07-30 20:11:56,985 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-07-30 20:11:56,985 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-07-30 20:11:56,999 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-07-30 20:11:56,999 - ExplorationExpert - INFO - 计算路径成本
2025-07-30 20:11:56,999 - ExplorationExpert - INFO - 探索路径生成完成，成本: 99000.0, 路径长度: 66
2025-07-30 20:11:56,999 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [5, 20, 13, 8, 9, 12, 3, 10, 61, 27, 53, 62, 35, 60, 64, 34, 54, 65, 52, 7, 50, 55, 46, 29, 37, 44, 58, 51, 41, 42, 48, 26, 36, 30, 33, 19, 11, 21, 57, 28, 24, 49, 22, 14, 56, 45, 59, 38, 0, 18, 39, 4, 40, 32, 6, 16, 17, 31, 15, 43, 23, 25, 63, 1, 2, 47], 'cur_cost': 99000.0}
2025-07-30 20:11:57,000 - __main__ - INFO - 为个体 5 生成利用路径
2025-07-30 20:11:57,000 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-30 20:11:57,000 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-30 20:11:57,000 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 116186.0
2025-07-30 20:11:57,700 - ExploitationExpert - INFO - res_population_num: 21
2025-07-30 20:11:57,700 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9526, 9538, 9544, 9570.0, 9521, 9521, 9521, 9521, 9521]
2025-07-30 20:11:57,700 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 47, 49, 40, 43, 48, 46, 45, 44, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 16, 18, 17, 12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 47, 49, 40, 43, 48, 46, 45, 44, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       18, 17, 12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-07-30 20:11:57,700 - ExploitationExpert - INFO - populations_num: 10
2025-07-30 20:11:57,700 - ExploitationExpert - INFO - populations: [{'tour': [0, 22, 20, 6, 2, 8, 5, 4, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 12, 17, 18, 16, 23, 13, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12812.0}, {'tour': array([55, 27,  5, 30, 24, 46, 31, 38, 37, 48, 57, 18, 40, 26, 20, 51, 16,
        1, 33,  2, 62, 23, 61, 50, 36,  3,  7, 19, 39,  8, 22, 63, 15, 42,
       29,  0, 13, 34, 12, 21, 49, 45, 56, 28, 32, 11, 52,  4, 60, 53, 64,
       65, 54, 10, 43, 25, 44,  6, 17, 41, 14,  9, 35, 59, 47, 58],
      dtype=int64), 'cur_cost': 105058.0}, {'tour': [0, 5, 3, 13, 20, 21, 19, 16, 18, 12, 22, 23, 15, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 8, 2, 6, 4, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12430.0}, {'tour': array([ 1, 55, 27, 41, 40, 39, 57, 33, 31, 44, 19, 35, 16, 58,  5, 48, 43,
        8, 24, 29, 20, 13, 22, 30, 36, 26, 10, 52, 15,  4,  7, 46, 45, 42,
       47,  2, 11,  3, 32, 56, 59, 17, 61,  9, 37, 23, 21, 38, 65, 14, 54,
       49, 25, 18,  0, 62, 34, 64, 50, 53, 51, 28, 63, 60,  6, 12],
      dtype=int64), 'cur_cost': 94630.0}, {'tour': [5, 20, 13, 8, 9, 12, 3, 10, 61, 27, 53, 62, 35, 60, 64, 34, 54, 65, 52, 7, 50, 55, 46, 29, 37, 44, 58, 51, 41, 42, 48, 26, 36, 30, 33, 19, 11, 21, 57, 28, 24, 49, 22, 14, 56, 45, 59, 38, 0, 18, 39, 4, 40, 32, 6, 16, 17, 31, 15, 43, 23, 25, 63, 1, 2, 47], 'cur_cost': 99000.0}, {'tour': array([28, 23,  8, 64, 27, 52, 16, 18, 61, 40, 43, 11, 63,  7, 46, 21, 15,
       24, 44, 30, 58, 39, 25, 60, 33, 29, 54, 65, 51, 36, 50, 59, 19, 41,
       53,  6, 34, 13,  1,  2, 56,  9, 37, 47,  4, 20, 12, 55, 57, 42,  0,
       49, 62, 26, 10, 22, 14,  3, 32,  5, 31, 48, 35, 38, 17, 45],
      dtype=int64), 'cur_cost': 116186.0}, {'tour': [64, 7, 17, 1, 16, 11, 2, 20, 6, 10, 58, 54, 23, 28, 32, 26, 14, 22, 18, 40, 21, 49, 13, 36, 0, 15, 31, 9, 19, 29, 24, 25, 4, 3, 37, 33, 43, 50, 39, 27, 12, 30, 38, 48, 44, 51, 42, 47, 46, 55, 61, 53, 60, 56, 63, 62, 59, 8, 65, 57, 52, 5, 34, 35, 45, 41], 'cur_cost': 56266.0}, {'tour': array([62, 24, 45, 49, 52, 56, 21, 44,  1, 14, 43, 51, 38, 54, 26,  6, 31,
       18, 17, 36, 59,  2, 41, 58,  3, 25, 22, 27, 53, 20, 16, 11, 29, 50,
       28, 19, 23, 46, 33,  5, 30, 48, 12,  4, 15, 13, 37, 57, 32, 10, 64,
        7, 63, 60, 35, 55, 39, 65, 47, 40, 34,  8,  9, 42,  0, 61],
      dtype=int64), 'cur_cost': 107665.0}, {'tour': [62, 64, 3, 54, 52, 58, 0, 4, 8, 22, 37, 33, 1, 7, 55, 15, 24, 25, 6, 60, 11, 61, 39, 51, 43, 23, 36, 20, 26, 14, 27, 2, 59, 65, 57, 53, 56, 21, 19, 29, 30, 28, 35, 13, 16, 12, 9, 10, 18, 49, 45, 46, 44, 38, 40, 47, 17, 31, 34, 32, 48, 50, 42, 41, 5, 63], 'cur_cost': 48285.0}, {'tour': array([59,  7, 55, 12, 31, 10, 27, 60, 38, 33, 22, 50, 23, 64, 30, 41, 24,
       36, 29, 56, 43, 37, 46, 63, 14, 57, 35, 39, 49, 51,  5, 44,  0, 58,
       32, 62, 42, 52, 13, 15, 28,  2,  4, 47, 21, 40, 20, 16, 45, 61, 11,
       34, 18, 17,  6,  9, 48,  1, 26, 54,  8, 19,  3, 25, 65, 53],
      dtype=int64), 'cur_cost': 112870.0}]
2025-07-30 20:11:57,700 - ExploitationExpert - INFO - 局部搜索耗时: 0.70秒
2025-07-30 20:11:57,700 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 13, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 13, 'cache_hits': 0, 'similarity_calculations': 78, 'cache_hit_rate': 0.0, 'cache_size': 78}}
2025-07-30 20:11:57,700 - __main__ - WARNING - 个体 5 的利用路径生成失败，保留原个体
2025-07-30 20:11:57,700 - __main__ - INFO - 为个体 6 生成探索路径
2025-07-30 20:11:57,700 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-07-30 20:11:57,700 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-07-30 20:11:57,700 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-07-30 20:11:57,700 - ExplorationExpert - INFO - 计算路径成本
2025-07-30 20:11:57,700 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12391.0, 路径长度: 66
2025-07-30 20:11:57,700 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 11, 14, 16, 18, 12, 22, 23, 13, 20, 21, 19, 17, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12391.0}
2025-07-30 20:11:57,700 - __main__ - INFO - 为个体 7 生成利用路径
2025-07-30 20:11:57,700 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-30 20:11:57,700 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-30 20:11:57,714 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 104859.0
2025-07-30 20:11:57,798 - ExploitationExpert - INFO - res_population_num: 21
2025-07-30 20:11:57,798 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9526, 9538, 9544, 9570.0, 9521, 9521, 9521, 9521, 9521]
2025-07-30 20:11:57,798 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 47, 49, 40, 43, 48, 46, 45, 44, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 16, 18, 17, 12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 47, 49, 40, 43, 48, 46, 45, 44, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       18, 17, 12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-07-30 20:11:57,798 - ExploitationExpert - INFO - populations_num: 10
2025-07-30 20:11:57,798 - ExploitationExpert - INFO - populations: [{'tour': [0, 22, 20, 6, 2, 8, 5, 4, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 12, 17, 18, 16, 23, 13, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12812.0}, {'tour': array([55, 27,  5, 30, 24, 46, 31, 38, 37, 48, 57, 18, 40, 26, 20, 51, 16,
        1, 33,  2, 62, 23, 61, 50, 36,  3,  7, 19, 39,  8, 22, 63, 15, 42,
       29,  0, 13, 34, 12, 21, 49, 45, 56, 28, 32, 11, 52,  4, 60, 53, 64,
       65, 54, 10, 43, 25, 44,  6, 17, 41, 14,  9, 35, 59, 47, 58],
      dtype=int64), 'cur_cost': 105058.0}, {'tour': [0, 5, 3, 13, 20, 21, 19, 16, 18, 12, 22, 23, 15, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 8, 2, 6, 4, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12430.0}, {'tour': array([ 1, 55, 27, 41, 40, 39, 57, 33, 31, 44, 19, 35, 16, 58,  5, 48, 43,
        8, 24, 29, 20, 13, 22, 30, 36, 26, 10, 52, 15,  4,  7, 46, 45, 42,
       47,  2, 11,  3, 32, 56, 59, 17, 61,  9, 37, 23, 21, 38, 65, 14, 54,
       49, 25, 18,  0, 62, 34, 64, 50, 53, 51, 28, 63, 60,  6, 12],
      dtype=int64), 'cur_cost': 94630.0}, {'tour': [5, 20, 13, 8, 9, 12, 3, 10, 61, 27, 53, 62, 35, 60, 64, 34, 54, 65, 52, 7, 50, 55, 46, 29, 37, 44, 58, 51, 41, 42, 48, 26, 36, 30, 33, 19, 11, 21, 57, 28, 24, 49, 22, 14, 56, 45, 59, 38, 0, 18, 39, 4, 40, 32, 6, 16, 17, 31, 15, 43, 23, 25, 63, 1, 2, 47], 'cur_cost': 99000.0}, {'tour': array([28, 23,  8, 64, 27, 52, 16, 18, 61, 40, 43, 11, 63,  7, 46, 21, 15,
       24, 44, 30, 58, 39, 25, 60, 33, 29, 54, 65, 51, 36, 50, 59, 19, 41,
       53,  6, 34, 13,  1,  2, 56,  9, 37, 47,  4, 20, 12, 55, 57, 42,  0,
       49, 62, 26, 10, 22, 14,  3, 32,  5, 31, 48, 35, 38, 17, 45],
      dtype=int64), 'cur_cost': 116186.0}, {'tour': [0, 11, 14, 16, 18, 12, 22, 23, 13, 20, 21, 19, 17, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12391.0}, {'tour': array([36, 59, 63, 51, 30, 58,  7, 38, 21, 10, 48, 54, 57,  0, 39, 45,  1,
       27, 37, 16,  6, 26, 12, 29, 33, 46, 11, 49, 55, 53, 60, 64, 35,  3,
       22, 65, 42, 52, 20, 14, 44,  5, 61, 32, 19, 34, 41, 40, 50,  4, 25,
       43, 17, 62, 28, 15, 56, 13, 31, 24, 23,  2, 18, 47,  8,  9],
      dtype=int64), 'cur_cost': 104859.0}, {'tour': [62, 64, 3, 54, 52, 58, 0, 4, 8, 22, 37, 33, 1, 7, 55, 15, 24, 25, 6, 60, 11, 61, 39, 51, 43, 23, 36, 20, 26, 14, 27, 2, 59, 65, 57, 53, 56, 21, 19, 29, 30, 28, 35, 13, 16, 12, 9, 10, 18, 49, 45, 46, 44, 38, 40, 47, 17, 31, 34, 32, 48, 50, 42, 41, 5, 63], 'cur_cost': 48285.0}, {'tour': array([59,  7, 55, 12, 31, 10, 27, 60, 38, 33, 22, 50, 23, 64, 30, 41, 24,
       36, 29, 56, 43, 37, 46, 63, 14, 57, 35, 39, 49, 51,  5, 44,  0, 58,
       32, 62, 42, 52, 13, 15, 28,  2,  4, 47, 21, 40, 20, 16, 45, 61, 11,
       34, 18, 17,  6,  9, 48,  1, 26, 54,  8, 19,  3, 25, 65, 53],
      dtype=int64), 'cur_cost': 112870.0}]
2025-07-30 20:11:57,798 - ExploitationExpert - INFO - 局部搜索耗时: 0.08秒
2025-07-30 20:11:57,798 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 14, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 14, 'cache_hits': 0, 'similarity_calculations': 91, 'cache_hit_rate': 0.0, 'cache_size': 91}}
2025-07-30 20:11:57,798 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-07-30 20:11:57,798 - __main__ - INFO - 为个体 8 生成探索路径
2025-07-30 20:11:57,798 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-07-30 20:11:57,798 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-07-30 20:11:57,798 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-07-30 20:11:57,798 - ExplorationExpert - INFO - 计算路径成本
2025-07-30 20:11:57,798 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12285.0, 路径长度: 66
2025-07-30 20:11:57,798 - __main__ - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 9, 14, 20, 21, 13, 23, 16, 18, 12, 22, 15, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12285.0}
2025-07-30 20:11:57,798 - __main__ - INFO - 为个体 9 生成利用路径
2025-07-30 20:11:57,798 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-30 20:11:57,798 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-30 20:11:57,813 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 109793.0
2025-07-30 20:11:57,897 - ExploitationExpert - INFO - res_population_num: 23
2025-07-30 20:11:57,897 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9526, 9538, 9544, 9570.0, 9521, 9521, 9521, 9521, 9521, 9521, 9521]
2025-07-30 20:11:57,897 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 47, 49, 40, 43, 48, 46, 45, 44, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 16, 18, 17, 12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 47, 49, 40, 43, 48, 46, 45, 44, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       18, 17, 12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64)]
2025-07-30 20:11:57,897 - ExploitationExpert - INFO - populations_num: 10
2025-07-30 20:11:57,897 - ExploitationExpert - INFO - populations: [{'tour': [0, 22, 20, 6, 2, 8, 5, 4, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 12, 17, 18, 16, 23, 13, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12812.0}, {'tour': array([55, 27,  5, 30, 24, 46, 31, 38, 37, 48, 57, 18, 40, 26, 20, 51, 16,
        1, 33,  2, 62, 23, 61, 50, 36,  3,  7, 19, 39,  8, 22, 63, 15, 42,
       29,  0, 13, 34, 12, 21, 49, 45, 56, 28, 32, 11, 52,  4, 60, 53, 64,
       65, 54, 10, 43, 25, 44,  6, 17, 41, 14,  9, 35, 59, 47, 58],
      dtype=int64), 'cur_cost': 105058.0}, {'tour': [0, 5, 3, 13, 20, 21, 19, 16, 18, 12, 22, 23, 15, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 8, 2, 6, 4, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12430.0}, {'tour': array([ 1, 55, 27, 41, 40, 39, 57, 33, 31, 44, 19, 35, 16, 58,  5, 48, 43,
        8, 24, 29, 20, 13, 22, 30, 36, 26, 10, 52, 15,  4,  7, 46, 45, 42,
       47,  2, 11,  3, 32, 56, 59, 17, 61,  9, 37, 23, 21, 38, 65, 14, 54,
       49, 25, 18,  0, 62, 34, 64, 50, 53, 51, 28, 63, 60,  6, 12],
      dtype=int64), 'cur_cost': 94630.0}, {'tour': [5, 20, 13, 8, 9, 12, 3, 10, 61, 27, 53, 62, 35, 60, 64, 34, 54, 65, 52, 7, 50, 55, 46, 29, 37, 44, 58, 51, 41, 42, 48, 26, 36, 30, 33, 19, 11, 21, 57, 28, 24, 49, 22, 14, 56, 45, 59, 38, 0, 18, 39, 4, 40, 32, 6, 16, 17, 31, 15, 43, 23, 25, 63, 1, 2, 47], 'cur_cost': 99000.0}, {'tour': array([28, 23,  8, 64, 27, 52, 16, 18, 61, 40, 43, 11, 63,  7, 46, 21, 15,
       24, 44, 30, 58, 39, 25, 60, 33, 29, 54, 65, 51, 36, 50, 59, 19, 41,
       53,  6, 34, 13,  1,  2, 56,  9, 37, 47,  4, 20, 12, 55, 57, 42,  0,
       49, 62, 26, 10, 22, 14,  3, 32,  5, 31, 48, 35, 38, 17, 45],
      dtype=int64), 'cur_cost': 116186.0}, {'tour': [0, 11, 14, 16, 18, 12, 22, 23, 13, 20, 21, 19, 17, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12391.0}, {'tour': array([36, 59, 63, 51, 30, 58,  7, 38, 21, 10, 48, 54, 57,  0, 39, 45,  1,
       27, 37, 16,  6, 26, 12, 29, 33, 46, 11, 49, 55, 53, 60, 64, 35,  3,
       22, 65, 42, 52, 20, 14, 44,  5, 61, 32, 19, 34, 41, 40, 50,  4, 25,
       43, 17, 62, 28, 15, 56, 13, 31, 24, 23,  2, 18, 47,  8,  9],
      dtype=int64), 'cur_cost': 104859.0}, {'tour': [0, 9, 14, 20, 21, 13, 23, 16, 18, 12, 22, 15, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12285.0}, {'tour': array([ 7, 11,  6, 33, 30,  4, 48, 59, 49, 31, 13, 47, 12, 55, 29, 17,  1,
       41, 65, 18, 25, 23, 50, 24, 14, 35, 16, 26, 60, 38, 58, 28, 52, 64,
        3, 22,  8, 40, 10, 21, 27, 15, 45, 56, 34,  9, 63,  2, 19, 61, 42,
       62, 57, 54,  5, 44, 51, 43,  0, 36, 32, 20, 53, 37, 39, 46],
      dtype=int64), 'cur_cost': 109793.0}]
2025-07-30 20:11:57,897 - ExploitationExpert - INFO - 局部搜索耗时: 0.08秒
2025-07-30 20:11:57,897 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 15, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 15, 'cache_hits': 0, 'similarity_calculations': 105, 'cache_hit_rate': 0.0, 'cache_size': 105}}
2025-07-30 20:11:57,897 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-07-30 20:11:57,897 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 22, 20, 6, 2, 8, 5, 4, 9, 11, 7, 3, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 12, 17, 18, 16, 23, 13, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12812.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 3, 13, 20, 21, 19, 16, 18, 12, 22, 23, 15, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 8, 2, 6, 4, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12430.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [5, 20, 13, 8, 9, 12, 3, 10, 61, 27, 53, 62, 35, 60, 64, 34, 54, 65, 52, 7, 50, 55, 46, 29, 37, 44, 58, 51, 41, 42, 48, 26, 36, 30, 33, 19, 11, 21, 57, 28, 24, 49, 22, 14, 56, 45, 59, 38, 0, 18, 39, 4, 40, 32, 6, 16, 17, 31, 15, 43, 23, 25, 63, 1, 2, 47], 'cur_cost': 99000.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 11, 14, 16, 18, 12, 22, 23, 13, 20, 21, 19, 17, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12391.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 9, 14, 20, 21, 13, 23, 16, 18, 12, 22, 15, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12285.0}}]
2025-07-30 20:11:57,897 - __main__ - INFO - 进化阶段完成
2025-07-30 20:11:57,897 - StatsExpert - INFO - 开始统计分析
2025-07-30 20:11:57,928 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 12285.0, 'max': 116186.0, 'mean': 67944.4, 'std': 45607.91052701275}, 'diversity': 0.8525252525252526, 'clusters': {'clusters': 8, 'cluster_sizes': [2, 1, 2, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-30 20:11:57,928 - __main__ - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-07-30 20:11:57,928 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment ---
2025-07-30 20:11:57,930 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-07-30 20:11:57,930 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 2/5

#### Population metrics
- Min cost: 0 → 0 (Δ 0)
- Mean cost: 0 → 0
- Diversity: 0.000 → 0.000 (Δ 0)

#### Elite metrics
- Elite count: 23 → 23
- Best elite cost: 9521 → 9521 (Δ 0)
- Elite diversity: 0.075 → 0.075 (Δ 0.0)

#### Strategy performance
- Explore: 0 inds, success 0% , avg Δ 0
- Exploit: 0 inds, success 0% , avg Δ 0

#### Other indicators
- No-change individuals: 0
- Regression individuals: 0
- Historical trends: N/A

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

### Output Example
```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.5, "exploit": 0.5},
  "balance_state": "balanced",
  "recommendations": ["maintain_current_ratio"]
}
```

2025-07-30 20:11:57,930 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-30 20:11:59,514 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 20:12:01,516 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 2/3)
2025-07-30 20:12:03,108 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 20:12:05,110 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 3/3)
2025-07-30 20:12:06,731 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 20:12:06,732 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: API请求失败: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 20:12:06,732 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-07-30 20:12:06,732 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-07-30 20:12:06,732 - __main__ - INFO - composite13_66 开始进化第 4 代
2025-07-30 20:12:06,732 - __main__ - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-07-30 20:12:06,732 - StatsExpert - INFO - 开始统计分析
2025-07-30 20:12:06,754 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 12285.0, 'max': 116186.0, 'mean': 67944.4, 'std': 45607.91052701275}, 'diversity': 0.8525252525252526, 'clusters': {'clusters': 8, 'cluster_sizes': [2, 1, 2, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-30 20:12:06,754 - PathExpert - INFO - 开始路径结构分析
2025-07-30 20:12:06,756 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-07-30 20:12:06,757 - PathExpert - INFO - 路径结构分析完成
2025-07-30 20:12:06,757 - EliteExpert - INFO - 开始精英解分析
2025-07-30 20:12:06,769 - EliteExpert - INFO - 精英解分析完成
2025-07-30 20:12:06,769 - LandscapeExpert - INFO - 开始景观分析
2025-07-30 20:12:06,769 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-30 20:12:06,769 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 3/5)
- population_size: 10
- cost_stats: min 12285.0, mean 67944.4, max 116186.0, std 45607.91052701275
- diversity: 0.8525252525252526
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Not available (Δ Not available, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {'common_edges': {}, 'common_edge_ratio': 0.0}
- elite_diversity: {'diversity_score': 0.19978440531800212}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

### Output Example
```json
{
  "search_space_features": {"ruggedness": 0.5, "modality": "multi", "deceptiveness": "unknown"},
  "population_state": {"diversity": 0.5, "convergence": 0.4, "clustering": "weak"},
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "unknown",
  "evolution_direction": {"recommended_focus": "explore", "operators": []}
}
```

### Formatting Guidelines (read carefully, DO NOT output this section)
1. Do NOT copy or repeat any *_sample values above; at least 50% of elements in difficult_regions and opportunity_regions must differ from those samples.
2. Use [] (empty array) when a list has no content; never output the string "None" or a null value.
3. For search_space_features use enumerated strings only:
    • ruggedness: "low" | "medium" | "high"
    • modality: "mono" | "multi" | "unknown"
    • deceptiveness: "low" | "moderate" | "high" | "unknown"
4. diversity and convergence must be floats with exactly 3 decimal places (e.g. 0.742).
5. Follow the JSON schema strictly; no additional keys or commentary.

2025-07-30 20:12:06,769 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-30 20:12:08,375 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 20:12:10,377 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 2/3)
2025-07-30 20:12:11,982 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 20:12:13,983 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 3/3)
2025-07-30 20:12:15,590 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 20:12:15,592 - LandscapeExpert - INFO - LLM返回的分析结果: API请求失败: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 20:12:15,592 - LandscapeExpert - WARNING - 解析景观分析结果失败: Unable to extract valid JSON data from the response
2025-07-30 20:12:15,592 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-30 20:12:15,592 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.5, 'modality': 'unknown', 'deceptiveness': 'unknown'}, 'population_state': {'diversity': 0.5, 'convergence': 0.5, 'clustering': 'unknown'}, 'difficult_regions': [], 'opportunity_regions': [], 'evolution_phase': 'unknown', 'evolution_direction': {'recommended_focus': 'balance', 'operators': []}, 'raw_text': 'API请求失败: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ'}
2025-07-30 20:12:15,592 - __main__ - INFO - 开始策略分配阶段
2025-07-30 20:12:15,592 - StrategyExpert - INFO - 开始策略分配分析
2025-07-30 20:12:15,592 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 3
- Population: 10 individuals
  • diversity: 0.5
  • best_cost: 12285.0
  • mean_cost: 67944.4
- Search-space: ruggedness 0.5, modality unknown, deceptiveness unknown
- Evolution phase: unknown
- Landscape focus suggestion: balance
- Spatial density summary: None
- Elite diversity: N/A
- Difficult regions (# 0): None
- Opportunity regions (# 0): None
- Last-iteration feedback: API请求失败: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Formatting Guidelines (DO NOT output this section)
1. global_explore_ratio must be rounded to exactly 2 decimal places (e.g., 0.80).
2. The individual_assignments mapping MUST include every index from 0 to (population_size - 1) without omission.
3. Valid values are "explore" or "exploit" (lowercase).
4. rationale ≤ 40 words.

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-30 20:12:15,592 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-30 20:12:15,592 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-30 20:12:17,166 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 20:12:19,170 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 2/3)
2025-07-30 20:12:20,774 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 20:12:22,774 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 3/3)
2025-07-30 20:12:24,380 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 20:12:24,381 - StrategyExpert - INFO - LLM返回的策略分配结果: API请求失败: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 20:12:24,381 - StrategyExpert - WARNING - 解析策略分配结果时出错: Unable to extract valid JSON data from the response
2025-07-30 20:12:24,381 - StrategyExpert - INFO - 策略分配完成: ['explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-07-30 20:12:24,381 - __main__ - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-07-30 20:12:24,381 - __main__ - INFO - 策略分配完整报告: API请求失败: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 20:12:24,381 - __main__ - INFO - 策略分配阶段完成
2025-07-30 20:12:24,381 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-07-30 20:12:24,381 - __main__ - INFO - 策略分配完整报告: API请求失败: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 20:12:24,381 - __main__ - INFO - 开始进化阶段
2025-07-30 20:12:24,381 - __main__ - INFO - 为个体 0 生成探索路径
2025-07-30 20:12:24,381 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-07-30 20:12:24,381 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-07-30 20:12:24,381 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-07-30 20:12:24,381 - ExplorationExpert - INFO - 计算路径成本
2025-07-30 20:12:24,381 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14846.0, 路径长度: 66
2025-07-30 20:12:24,381 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 16, 5, 15, 22, 12, 17, 18, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14846.0}
2025-07-30 20:12:24,381 - __main__ - INFO - 为个体 1 生成利用路径
2025-07-30 20:12:24,381 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-30 20:12:24,381 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-30 20:12:24,381 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 96293.0
2025-07-30 20:12:24,465 - ExploitationExpert - INFO - res_population_num: 24
2025-07-30 20:12:24,465 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9526, 9538, 9544, 9570.0, 9521]
2025-07-30 20:12:24,465 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 47, 49, 40, 43, 48, 46, 45, 44, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 16, 18, 17, 12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 47, 49, 40, 43, 48, 46, 45, 44, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       18, 17, 12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-07-30 20:12:24,465 - ExploitationExpert - INFO - populations_num: 10
2025-07-30 20:12:24,465 - ExploitationExpert - INFO - populations: [{'tour': [0, 16, 5, 15, 22, 12, 17, 18, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14846.0}, {'tour': array([ 4, 24, 19, 40, 52, 20, 48, 14, 34, 32, 37, 63, 13, 64, 11,  5, 46,
       57, 53, 41, 44, 36, 49, 38, 28, 21, 58,  0, 60,  6,  2, 54, 59, 51,
       31, 17, 35, 61, 33, 30, 43, 27, 50, 18, 23, 39, 26, 15, 56, 42, 62,
       65, 22,  1,  3, 47,  7, 12, 16,  8,  9, 55, 29, 25, 45, 10],
      dtype=int64), 'cur_cost': 96293.0}, {'tour': [0, 5, 3, 13, 20, 21, 19, 16, 18, 12, 22, 23, 15, 14, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 9, 11, 7, 1, 8, 2, 6, 4, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12430.0}, {'tour': array([ 1, 55, 27, 41, 40, 39, 57, 33, 31, 44, 19, 35, 16, 58,  5, 48, 43,
        8, 24, 29, 20, 13, 22, 30, 36, 26, 10, 52, 15,  4,  7, 46, 45, 42,
       47,  2, 11,  3, 32, 56, 59, 17, 61,  9, 37, 23, 21, 38, 65, 14, 54,
       49, 25, 18,  0, 62, 34, 64, 50, 53, 51, 28, 63, 60,  6, 12],
      dtype=int64), 'cur_cost': 94630.0}, {'tour': [5, 20, 13, 8, 9, 12, 3, 10, 61, 27, 53, 62, 35, 60, 64, 34, 54, 65, 52, 7, 50, 55, 46, 29, 37, 44, 58, 51, 41, 42, 48, 26, 36, 30, 33, 19, 11, 21, 57, 28, 24, 49, 22, 14, 56, 45, 59, 38, 0, 18, 39, 4, 40, 32, 6, 16, 17, 31, 15, 43, 23, 25, 63, 1, 2, 47], 'cur_cost': 99000.0}, {'tour': array([28, 23,  8, 64, 27, 52, 16, 18, 61, 40, 43, 11, 63,  7, 46, 21, 15,
       24, 44, 30, 58, 39, 25, 60, 33, 29, 54, 65, 51, 36, 50, 59, 19, 41,
       53,  6, 34, 13,  1,  2, 56,  9, 37, 47,  4, 20, 12, 55, 57, 42,  0,
       49, 62, 26, 10, 22, 14,  3, 32,  5, 31, 48, 35, 38, 17, 45],
      dtype=int64), 'cur_cost': 116186.0}, {'tour': [0, 11, 14, 16, 18, 12, 22, 23, 13, 20, 21, 19, 17, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12391.0}, {'tour': array([36, 59, 63, 51, 30, 58,  7, 38, 21, 10, 48, 54, 57,  0, 39, 45,  1,
       27, 37, 16,  6, 26, 12, 29, 33, 46, 11, 49, 55, 53, 60, 64, 35,  3,
       22, 65, 42, 52, 20, 14, 44,  5, 61, 32, 19, 34, 41, 40, 50,  4, 25,
       43, 17, 62, 28, 15, 56, 13, 31, 24, 23,  2, 18, 47,  8,  9],
      dtype=int64), 'cur_cost': 104859.0}, {'tour': [0, 9, 14, 20, 21, 13, 23, 16, 18, 12, 22, 15, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12285.0}, {'tour': array([ 7, 11,  6, 33, 30,  4, 48, 59, 49, 31, 13, 47, 12, 55, 29, 17,  1,
       41, 65, 18, 25, 23, 50, 24, 14, 35, 16, 26, 60, 38, 58, 28, 52, 64,
        3, 22,  8, 40, 10, 21, 27, 15, 45, 56, 34,  9, 63,  2, 19, 61, 42,
       62, 57, 54,  5, 44, 51, 43,  0, 36, 32, 20, 53, 37, 39, 46],
      dtype=int64), 'cur_cost': 109793.0}]
2025-07-30 20:12:24,465 - ExploitationExpert - INFO - 局部搜索耗时: 0.08秒
2025-07-30 20:12:24,465 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 16, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 16, 'cache_hits': 0, 'similarity_calculations': 120, 'cache_hit_rate': 0.0, 'cache_size': 120}}
2025-07-30 20:12:24,465 - __main__ - WARNING - 个体 1 的利用路径生成失败，保留原个体
2025-07-30 20:12:24,465 - __main__ - INFO - 为个体 2 生成探索路径
2025-07-30 20:12:24,465 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-07-30 20:12:24,465 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-07-30 20:12:24,481 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-07-30 20:12:24,481 - ExplorationExpert - INFO - 计算路径成本
2025-07-30 20:12:24,481 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12388.0, 路径长度: 66
2025-07-30 20:12:24,481 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 11, 20, 19, 16, 18, 12, 22, 23, 13, 21, 14, 15, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12388.0}
2025-07-30 20:12:24,482 - __main__ - INFO - 为个体 3 生成利用路径
2025-07-30 20:12:24,482 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-30 20:12:24,482 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-30 20:12:24,482 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 116333.0
2025-07-30 20:12:24,582 - ExploitationExpert - INFO - res_population_num: 24
2025-07-30 20:12:24,582 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9526, 9538, 9544, 9570.0, 9521]
2025-07-30 20:12:24,582 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 47, 49, 40, 43, 48, 46, 45, 44, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 16, 18, 17, 12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 47, 49, 40, 43, 48, 46, 45, 44, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       18, 17, 12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-07-30 20:12:24,598 - ExploitationExpert - INFO - populations_num: 10
2025-07-30 20:12:24,598 - ExploitationExpert - INFO - populations: [{'tour': [0, 16, 5, 15, 22, 12, 17, 18, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14846.0}, {'tour': array([ 4, 24, 19, 40, 52, 20, 48, 14, 34, 32, 37, 63, 13, 64, 11,  5, 46,
       57, 53, 41, 44, 36, 49, 38, 28, 21, 58,  0, 60,  6,  2, 54, 59, 51,
       31, 17, 35, 61, 33, 30, 43, 27, 50, 18, 23, 39, 26, 15, 56, 42, 62,
       65, 22,  1,  3, 47,  7, 12, 16,  8,  9, 55, 29, 25, 45, 10],
      dtype=int64), 'cur_cost': 96293.0}, {'tour': [0, 11, 20, 19, 16, 18, 12, 22, 23, 13, 21, 14, 15, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12388.0}, {'tour': array([47, 10, 26, 25,  7, 53, 17, 33,  0, 61, 13, 18, 19, 59, 34,  1, 55,
       12,  3, 38,  9, 37, 45, 64, 46, 15, 52, 39, 44, 30, 28, 32, 11, 56,
       48,  2, 40, 23, 62, 43, 42,  4, 35, 20, 29, 57,  8, 14, 31, 49, 60,
       27, 63, 54, 36, 24, 41, 65, 51,  5, 22, 50,  6, 16, 58, 21],
      dtype=int64), 'cur_cost': 116333.0}, {'tour': [5, 20, 13, 8, 9, 12, 3, 10, 61, 27, 53, 62, 35, 60, 64, 34, 54, 65, 52, 7, 50, 55, 46, 29, 37, 44, 58, 51, 41, 42, 48, 26, 36, 30, 33, 19, 11, 21, 57, 28, 24, 49, 22, 14, 56, 45, 59, 38, 0, 18, 39, 4, 40, 32, 6, 16, 17, 31, 15, 43, 23, 25, 63, 1, 2, 47], 'cur_cost': 99000.0}, {'tour': array([28, 23,  8, 64, 27, 52, 16, 18, 61, 40, 43, 11, 63,  7, 46, 21, 15,
       24, 44, 30, 58, 39, 25, 60, 33, 29, 54, 65, 51, 36, 50, 59, 19, 41,
       53,  6, 34, 13,  1,  2, 56,  9, 37, 47,  4, 20, 12, 55, 57, 42,  0,
       49, 62, 26, 10, 22, 14,  3, 32,  5, 31, 48, 35, 38, 17, 45],
      dtype=int64), 'cur_cost': 116186.0}, {'tour': [0, 11, 14, 16, 18, 12, 22, 23, 13, 20, 21, 19, 17, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12391.0}, {'tour': array([36, 59, 63, 51, 30, 58,  7, 38, 21, 10, 48, 54, 57,  0, 39, 45,  1,
       27, 37, 16,  6, 26, 12, 29, 33, 46, 11, 49, 55, 53, 60, 64, 35,  3,
       22, 65, 42, 52, 20, 14, 44,  5, 61, 32, 19, 34, 41, 40, 50,  4, 25,
       43, 17, 62, 28, 15, 56, 13, 31, 24, 23,  2, 18, 47,  8,  9],
      dtype=int64), 'cur_cost': 104859.0}, {'tour': [0, 9, 14, 20, 21, 13, 23, 16, 18, 12, 22, 15, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12285.0}, {'tour': array([ 7, 11,  6, 33, 30,  4, 48, 59, 49, 31, 13, 47, 12, 55, 29, 17,  1,
       41, 65, 18, 25, 23, 50, 24, 14, 35, 16, 26, 60, 38, 58, 28, 52, 64,
        3, 22,  8, 40, 10, 21, 27, 15, 45, 56, 34,  9, 63,  2, 19, 61, 42,
       62, 57, 54,  5, 44, 51, 43,  0, 36, 32, 20, 53, 37, 39, 46],
      dtype=int64), 'cur_cost': 109793.0}]
2025-07-30 20:12:24,598 - ExploitationExpert - INFO - 局部搜索耗时: 0.12秒
2025-07-30 20:12:24,598 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 17, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 17, 'cache_hits': 0, 'similarity_calculations': 136, 'cache_hit_rate': 0.0, 'cache_size': 136}}
2025-07-30 20:12:24,598 - __main__ - WARNING - 个体 3 的利用路径生成失败，保留原个体
2025-07-30 20:12:24,598 - __main__ - INFO - 为个体 4 生成探索路径
2025-07-30 20:12:24,598 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-07-30 20:12:24,598 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-07-30 20:12:24,598 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-07-30 20:12:24,598 - ExplorationExpert - INFO - 计算路径成本
2025-07-30 20:12:24,598 - ExplorationExpert - INFO - 探索路径生成完成，成本: 110116.0, 路径长度: 66
2025-07-30 20:12:24,598 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [16, 20, 22, 12, 21, 17, 9, 19, 45, 38, 24, 8, 46, 59, 25, 60, 48, 34, 61, 51, 23, 27, 2, 64, 63, 41, 62, 28, 18, 37, 65, 52, 4, 58, 44, 43, 29, 47, 13, 35, 1, 33, 42, 55, 56, 3, 53, 32, 10, 50, 7, 15, 36, 39, 26, 0, 54, 6, 57, 40, 31, 14, 30, 11, 49, 5], 'cur_cost': 110116.0}
2025-07-30 20:12:24,598 - __main__ - INFO - 为个体 5 生成利用路径
2025-07-30 20:12:24,598 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-30 20:12:24,598 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-30 20:12:24,598 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 107633.0
2025-07-30 20:12:24,681 - ExploitationExpert - INFO - res_population_num: 24
2025-07-30 20:12:24,681 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9526, 9538, 9544, 9570.0, 9521]
2025-07-30 20:12:24,681 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 47, 49, 40, 43, 48, 46, 45, 44, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 16, 18, 17, 12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 47, 49, 40, 43, 48, 46, 45, 44, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       18, 17, 12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-07-30 20:12:24,681 - ExploitationExpert - INFO - populations_num: 10
2025-07-30 20:12:24,697 - ExploitationExpert - INFO - populations: [{'tour': [0, 16, 5, 15, 22, 12, 17, 18, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14846.0}, {'tour': array([ 4, 24, 19, 40, 52, 20, 48, 14, 34, 32, 37, 63, 13, 64, 11,  5, 46,
       57, 53, 41, 44, 36, 49, 38, 28, 21, 58,  0, 60,  6,  2, 54, 59, 51,
       31, 17, 35, 61, 33, 30, 43, 27, 50, 18, 23, 39, 26, 15, 56, 42, 62,
       65, 22,  1,  3, 47,  7, 12, 16,  8,  9, 55, 29, 25, 45, 10],
      dtype=int64), 'cur_cost': 96293.0}, {'tour': [0, 11, 20, 19, 16, 18, 12, 22, 23, 13, 21, 14, 15, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12388.0}, {'tour': array([47, 10, 26, 25,  7, 53, 17, 33,  0, 61, 13, 18, 19, 59, 34,  1, 55,
       12,  3, 38,  9, 37, 45, 64, 46, 15, 52, 39, 44, 30, 28, 32, 11, 56,
       48,  2, 40, 23, 62, 43, 42,  4, 35, 20, 29, 57,  8, 14, 31, 49, 60,
       27, 63, 54, 36, 24, 41, 65, 51,  5, 22, 50,  6, 16, 58, 21],
      dtype=int64), 'cur_cost': 116333.0}, {'tour': [16, 20, 22, 12, 21, 17, 9, 19, 45, 38, 24, 8, 46, 59, 25, 60, 48, 34, 61, 51, 23, 27, 2, 64, 63, 41, 62, 28, 18, 37, 65, 52, 4, 58, 44, 43, 29, 47, 13, 35, 1, 33, 42, 55, 56, 3, 53, 32, 10, 50, 7, 15, 36, 39, 26, 0, 54, 6, 57, 40, 31, 14, 30, 11, 49, 5], 'cur_cost': 110116.0}, {'tour': array([63, 39, 38, 24, 12, 45, 62,  9, 40, 44, 26,  1, 59, 43,  6, 55, 15,
        0, 18, 33,  5, 13, 35, 34, 50, 56, 20, 28, 52, 42, 25, 47, 65, 61,
       14, 49, 29, 60, 53, 21, 17, 31, 16, 51, 37, 10, 32, 36,  2, 54, 64,
       46,  8, 27, 41, 57, 48,  7, 11, 19, 30, 58,  3,  4, 22, 23],
      dtype=int64), 'cur_cost': 107633.0}, {'tour': [0, 11, 14, 16, 18, 12, 22, 23, 13, 20, 21, 19, 17, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12391.0}, {'tour': array([36, 59, 63, 51, 30, 58,  7, 38, 21, 10, 48, 54, 57,  0, 39, 45,  1,
       27, 37, 16,  6, 26, 12, 29, 33, 46, 11, 49, 55, 53, 60, 64, 35,  3,
       22, 65, 42, 52, 20, 14, 44,  5, 61, 32, 19, 34, 41, 40, 50,  4, 25,
       43, 17, 62, 28, 15, 56, 13, 31, 24, 23,  2, 18, 47,  8,  9],
      dtype=int64), 'cur_cost': 104859.0}, {'tour': [0, 9, 14, 20, 21, 13, 23, 16, 18, 12, 22, 15, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12285.0}, {'tour': array([ 7, 11,  6, 33, 30,  4, 48, 59, 49, 31, 13, 47, 12, 55, 29, 17,  1,
       41, 65, 18, 25, 23, 50, 24, 14, 35, 16, 26, 60, 38, 58, 28, 52, 64,
        3, 22,  8, 40, 10, 21, 27, 15, 45, 56, 34,  9, 63,  2, 19, 61, 42,
       62, 57, 54,  5, 44, 51, 43,  0, 36, 32, 20, 53, 37, 39, 46],
      dtype=int64), 'cur_cost': 109793.0}]
2025-07-30 20:12:24,697 - ExploitationExpert - INFO - 局部搜索耗时: 0.10秒
2025-07-30 20:12:24,697 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 18, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 18, 'cache_hits': 0, 'similarity_calculations': 153, 'cache_hit_rate': 0.0, 'cache_size': 153}}
2025-07-30 20:12:24,697 - __main__ - WARNING - 个体 5 的利用路径生成失败，保留原个体
2025-07-30 20:12:24,697 - __main__ - INFO - 为个体 6 生成探索路径
2025-07-30 20:12:24,697 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-07-30 20:12:24,697 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-07-30 20:12:24,697 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-07-30 20:12:24,697 - ExplorationExpert - INFO - 计算路径成本
2025-07-30 20:12:24,697 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12346.0, 路径长度: 66
2025-07-30 20:12:24,697 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 9, 12, 21, 20, 13, 23, 16, 18, 17, 22, 15, 14, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12346.0}
2025-07-30 20:12:24,697 - __main__ - INFO - 为个体 7 生成利用路径
2025-07-30 20:12:24,697 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-30 20:12:24,697 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-30 20:12:24,703 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 102559.0
2025-07-30 20:12:24,797 - ExploitationExpert - INFO - res_population_num: 24
2025-07-30 20:12:24,797 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9526, 9538, 9544, 9570.0, 9521]
2025-07-30 20:12:24,797 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 47, 49, 40, 43, 48, 46, 45, 44, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 16, 18, 17, 12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 47, 49, 40, 43, 48, 46, 45, 44, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       18, 17, 12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-07-30 20:12:24,797 - ExploitationExpert - INFO - populations_num: 10
2025-07-30 20:12:24,797 - ExploitationExpert - INFO - populations: [{'tour': [0, 16, 5, 15, 22, 12, 17, 18, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14846.0}, {'tour': array([ 4, 24, 19, 40, 52, 20, 48, 14, 34, 32, 37, 63, 13, 64, 11,  5, 46,
       57, 53, 41, 44, 36, 49, 38, 28, 21, 58,  0, 60,  6,  2, 54, 59, 51,
       31, 17, 35, 61, 33, 30, 43, 27, 50, 18, 23, 39, 26, 15, 56, 42, 62,
       65, 22,  1,  3, 47,  7, 12, 16,  8,  9, 55, 29, 25, 45, 10],
      dtype=int64), 'cur_cost': 96293.0}, {'tour': [0, 11, 20, 19, 16, 18, 12, 22, 23, 13, 21, 14, 15, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12388.0}, {'tour': array([47, 10, 26, 25,  7, 53, 17, 33,  0, 61, 13, 18, 19, 59, 34,  1, 55,
       12,  3, 38,  9, 37, 45, 64, 46, 15, 52, 39, 44, 30, 28, 32, 11, 56,
       48,  2, 40, 23, 62, 43, 42,  4, 35, 20, 29, 57,  8, 14, 31, 49, 60,
       27, 63, 54, 36, 24, 41, 65, 51,  5, 22, 50,  6, 16, 58, 21],
      dtype=int64), 'cur_cost': 116333.0}, {'tour': [16, 20, 22, 12, 21, 17, 9, 19, 45, 38, 24, 8, 46, 59, 25, 60, 48, 34, 61, 51, 23, 27, 2, 64, 63, 41, 62, 28, 18, 37, 65, 52, 4, 58, 44, 43, 29, 47, 13, 35, 1, 33, 42, 55, 56, 3, 53, 32, 10, 50, 7, 15, 36, 39, 26, 0, 54, 6, 57, 40, 31, 14, 30, 11, 49, 5], 'cur_cost': 110116.0}, {'tour': array([63, 39, 38, 24, 12, 45, 62,  9, 40, 44, 26,  1, 59, 43,  6, 55, 15,
        0, 18, 33,  5, 13, 35, 34, 50, 56, 20, 28, 52, 42, 25, 47, 65, 61,
       14, 49, 29, 60, 53, 21, 17, 31, 16, 51, 37, 10, 32, 36,  2, 54, 64,
       46,  8, 27, 41, 57, 48,  7, 11, 19, 30, 58,  3,  4, 22, 23],
      dtype=int64), 'cur_cost': 107633.0}, {'tour': [0, 9, 12, 21, 20, 13, 23, 16, 18, 17, 22, 15, 14, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12346.0}, {'tour': array([27, 43, 11,  7, 26, 23,  4, 57,  6, 28,  1, 35, 20, 22, 37, 21, 30,
       34, 61, 52, 51, 58, 12, 16, 33, 54, 60, 32, 49, 44, 17, 56, 15,  5,
        9, 36, 42, 55, 47, 53, 59, 65,  0, 64, 24, 40, 41, 39,  3, 10, 45,
       31, 63, 13, 18,  2, 48, 25, 14, 38, 19, 62, 50,  8, 46, 29],
      dtype=int64), 'cur_cost': 102559.0}, {'tour': [0, 9, 14, 20, 21, 13, 23, 16, 18, 12, 22, 15, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12285.0}, {'tour': array([ 7, 11,  6, 33, 30,  4, 48, 59, 49, 31, 13, 47, 12, 55, 29, 17,  1,
       41, 65, 18, 25, 23, 50, 24, 14, 35, 16, 26, 60, 38, 58, 28, 52, 64,
        3, 22,  8, 40, 10, 21, 27, 15, 45, 56, 34,  9, 63,  2, 19, 61, 42,
       62, 57, 54,  5, 44, 51, 43,  0, 36, 32, 20, 53, 37, 39, 46],
      dtype=int64), 'cur_cost': 109793.0}]
2025-07-30 20:12:24,797 - ExploitationExpert - INFO - 局部搜索耗时: 0.10秒
2025-07-30 20:12:24,797 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 19, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 19, 'cache_hits': 0, 'similarity_calculations': 171, 'cache_hit_rate': 0.0, 'cache_size': 171}}
2025-07-30 20:12:24,797 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-07-30 20:12:24,797 - __main__ - INFO - 为个体 8 生成探索路径
2025-07-30 20:12:24,797 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-07-30 20:12:24,797 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-07-30 20:12:24,797 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-07-30 20:12:24,797 - ExplorationExpert - INFO - 计算路径成本
2025-07-30 20:12:24,797 - ExplorationExpert - INFO - 探索路径生成完成，成本: 112324.0, 路径长度: 66
2025-07-30 20:12:24,797 - __main__ - INFO - 个体 8 探索路径生成报告: {'new_tour': [16, 5, 15, 22, 12, 9, 21, 41, 18, 32, 10, 48, 40, 63, 45, 60, 4, 64, 36, 49, 3, 20, 28, 30, 52, 42, 11, 1, 14, 23, 47, 62, 24, 7, 53, 29, 25, 46, 37, 54, 35, 56, 33, 58, 61, 57, 6, 8, 65, 43, 0, 31, 19, 26, 39, 13, 51, 59, 17, 38, 27, 2, 34, 55, 44, 50], 'cur_cost': 112324.0}
2025-07-30 20:12:24,797 - __main__ - INFO - 为个体 9 生成利用路径
2025-07-30 20:12:24,797 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-30 20:12:24,797 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-30 20:12:24,812 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 107955.0
2025-07-30 20:12:24,897 - ExploitationExpert - INFO - res_population_num: 24
2025-07-30 20:12:24,897 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9526, 9538, 9544, 9570.0, 9521]
2025-07-30 20:12:24,897 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 47, 49, 40, 43, 48, 46, 45, 44, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 16, 18, 17, 12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 47, 49, 40, 43, 48, 46, 45, 44, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       18, 17, 12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-07-30 20:12:24,897 - ExploitationExpert - INFO - populations_num: 10
2025-07-30 20:12:24,897 - ExploitationExpert - INFO - populations: [{'tour': [0, 16, 5, 15, 22, 12, 17, 18, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14846.0}, {'tour': array([ 4, 24, 19, 40, 52, 20, 48, 14, 34, 32, 37, 63, 13, 64, 11,  5, 46,
       57, 53, 41, 44, 36, 49, 38, 28, 21, 58,  0, 60,  6,  2, 54, 59, 51,
       31, 17, 35, 61, 33, 30, 43, 27, 50, 18, 23, 39, 26, 15, 56, 42, 62,
       65, 22,  1,  3, 47,  7, 12, 16,  8,  9, 55, 29, 25, 45, 10],
      dtype=int64), 'cur_cost': 96293.0}, {'tour': [0, 11, 20, 19, 16, 18, 12, 22, 23, 13, 21, 14, 15, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12388.0}, {'tour': array([47, 10, 26, 25,  7, 53, 17, 33,  0, 61, 13, 18, 19, 59, 34,  1, 55,
       12,  3, 38,  9, 37, 45, 64, 46, 15, 52, 39, 44, 30, 28, 32, 11, 56,
       48,  2, 40, 23, 62, 43, 42,  4, 35, 20, 29, 57,  8, 14, 31, 49, 60,
       27, 63, 54, 36, 24, 41, 65, 51,  5, 22, 50,  6, 16, 58, 21],
      dtype=int64), 'cur_cost': 116333.0}, {'tour': [16, 20, 22, 12, 21, 17, 9, 19, 45, 38, 24, 8, 46, 59, 25, 60, 48, 34, 61, 51, 23, 27, 2, 64, 63, 41, 62, 28, 18, 37, 65, 52, 4, 58, 44, 43, 29, 47, 13, 35, 1, 33, 42, 55, 56, 3, 53, 32, 10, 50, 7, 15, 36, 39, 26, 0, 54, 6, 57, 40, 31, 14, 30, 11, 49, 5], 'cur_cost': 110116.0}, {'tour': array([63, 39, 38, 24, 12, 45, 62,  9, 40, 44, 26,  1, 59, 43,  6, 55, 15,
        0, 18, 33,  5, 13, 35, 34, 50, 56, 20, 28, 52, 42, 25, 47, 65, 61,
       14, 49, 29, 60, 53, 21, 17, 31, 16, 51, 37, 10, 32, 36,  2, 54, 64,
       46,  8, 27, 41, 57, 48,  7, 11, 19, 30, 58,  3,  4, 22, 23],
      dtype=int64), 'cur_cost': 107633.0}, {'tour': [0, 9, 12, 21, 20, 13, 23, 16, 18, 17, 22, 15, 14, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12346.0}, {'tour': array([27, 43, 11,  7, 26, 23,  4, 57,  6, 28,  1, 35, 20, 22, 37, 21, 30,
       34, 61, 52, 51, 58, 12, 16, 33, 54, 60, 32, 49, 44, 17, 56, 15,  5,
        9, 36, 42, 55, 47, 53, 59, 65,  0, 64, 24, 40, 41, 39,  3, 10, 45,
       31, 63, 13, 18,  2, 48, 25, 14, 38, 19, 62, 50,  8, 46, 29],
      dtype=int64), 'cur_cost': 102559.0}, {'tour': [16, 5, 15, 22, 12, 9, 21, 41, 18, 32, 10, 48, 40, 63, 45, 60, 4, 64, 36, 49, 3, 20, 28, 30, 52, 42, 11, 1, 14, 23, 47, 62, 24, 7, 53, 29, 25, 46, 37, 54, 35, 56, 33, 58, 61, 57, 6, 8, 65, 43, 0, 31, 19, 26, 39, 13, 51, 59, 17, 38, 27, 2, 34, 55, 44, 50], 'cur_cost': 112324.0}, {'tour': array([11, 64, 54, 34, 20, 35, 27,  1, 17, 48, 15, 62,  9, 46,  7, 37, 29,
       43, 16, 65, 39, 61, 38, 18, 23, 14, 55, 32, 31, 26, 52, 22, 21, 47,
       57, 19, 42,  5, 50, 60, 10, 49,  6,  4, 63, 25,  8, 30, 44, 40, 36,
        2, 41, 13, 28,  0, 53, 59,  3, 45, 12, 33, 56, 58, 51, 24],
      dtype=int64), 'cur_cost': 107955.0}]
2025-07-30 20:12:24,897 - ExploitationExpert - INFO - 局部搜索耗时: 0.08秒
2025-07-30 20:12:24,897 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 20, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 20, 'cache_hits': 0, 'similarity_calculations': 190, 'cache_hit_rate': 0.0, 'cache_size': 190}}
2025-07-30 20:12:24,897 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-07-30 20:12:24,897 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 16, 5, 15, 22, 12, 17, 18, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 14846.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 11, 20, 19, 16, 18, 12, 22, 23, 13, 21, 14, 15, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12388.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [16, 20, 22, 12, 21, 17, 9, 19, 45, 38, 24, 8, 46, 59, 25, 60, 48, 34, 61, 51, 23, 27, 2, 64, 63, 41, 62, 28, 18, 37, 65, 52, 4, 58, 44, 43, 29, 47, 13, 35, 1, 33, 42, 55, 56, 3, 53, 32, 10, 50, 7, 15, 36, 39, 26, 0, 54, 6, 57, 40, 31, 14, 30, 11, 49, 5], 'cur_cost': 110116.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 9, 12, 21, 20, 13, 23, 16, 18, 17, 22, 15, 14, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12346.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [16, 5, 15, 22, 12, 9, 21, 41, 18, 32, 10, 48, 40, 63, 45, 60, 4, 64, 36, 49, 3, 20, 28, 30, 52, 42, 11, 1, 14, 23, 47, 62, 24, 7, 53, 29, 25, 46, 37, 54, 35, 56, 33, 58, 61, 57, 6, 8, 65, 43, 0, 31, 19, 26, 39, 13, 51, 59, 17, 38, 27, 2, 34, 55, 44, 50], 'cur_cost': 112324.0}}]
2025-07-30 20:12:24,897 - __main__ - INFO - 进化阶段完成
2025-07-30 20:12:24,897 - StatsExpert - INFO - 开始统计分析
2025-07-30 20:12:24,931 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 12346.0, 'max': 116333.0, 'mean': 79279.3, 'std': 43565.619766164236}, 'diversity': 0.9094276094276094, 'clusters': {'clusters': 8, 'cluster_sizes': [3, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-30 20:12:24,931 - __main__ - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-07-30 20:12:24,931 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment ---
2025-07-30 20:12:24,933 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-07-30 20:12:24,933 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 3/5

#### Population metrics
- Min cost: 0 → 0 (Δ 0)
- Mean cost: 0 → 0
- Diversity: 0.000 → 0.000 (Δ 0)

#### Elite metrics
- Elite count: 24 → 24
- Best elite cost: 9521 → 9521 (Δ 0)
- Elite diversity: 0.072 → 0.072 (Δ 0.0)

#### Strategy performance
- Explore: 0 inds, success 0% , avg Δ 0
- Exploit: 0 inds, success 0% , avg Δ 0

#### Other indicators
- No-change individuals: 0
- Regression individuals: 0
- Historical trends: N/A

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

### Output Example
```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.5, "exploit": 0.5},
  "balance_state": "balanced",
  "recommendations": ["maintain_current_ratio"]
}
```

2025-07-30 20:12:24,934 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-30 20:12:26,503 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 20:12:28,506 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 2/3)
2025-07-30 20:12:30,062 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 20:12:32,063 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 3/3)
2025-07-30 20:12:33,645 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 20:12:33,647 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: API请求失败: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 20:12:33,647 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-07-30 20:12:33,647 - __main__ - INFO - --- Finished Evolution Iteration 4 ---
2025-07-30 20:12:33,647 - __main__ - INFO - composite13_66 开始进化第 5 代
2025-07-30 20:12:33,647 - __main__ - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-07-30 20:12:33,647 - StatsExpert - INFO - 开始统计分析
2025-07-30 20:12:33,669 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 12346.0, 'max': 116333.0, 'mean': 79279.3, 'std': 43565.619766164236}, 'diversity': 0.9094276094276094, 'clusters': {'clusters': 8, 'cluster_sizes': [3, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-30 20:12:33,669 - PathExpert - INFO - 开始路径结构分析
2025-07-30 20:12:33,671 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-07-30 20:12:33,671 - PathExpert - INFO - 路径结构分析完成
2025-07-30 20:12:33,671 - EliteExpert - INFO - 开始精英解分析
2025-07-30 20:12:33,683 - EliteExpert - INFO - 精英解分析完成
2025-07-30 20:12:33,683 - LandscapeExpert - INFO - 开始景观分析
2025-07-30 20:12:33,683 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-30 20:12:33,683 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 4/5)
- population_size: 10
- cost_stats: min 12346.0, mean 79279.3, max 116333.0, std 43565.619766164236
- diversity: 0.9094276094276094
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Not available (Δ Not available, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {'common_edges': {}, 'common_edge_ratio': 0.0}
- elite_diversity: {'diversity_score': 0.2016359244620114}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

### Output Example
```json
{
  "search_space_features": {"ruggedness": 0.5, "modality": "multi", "deceptiveness": "unknown"},
  "population_state": {"diversity": 0.5, "convergence": 0.4, "clustering": "weak"},
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "unknown",
  "evolution_direction": {"recommended_focus": "explore", "operators": []}
}
```

### Formatting Guidelines (read carefully, DO NOT output this section)
1. Do NOT copy or repeat any *_sample values above; at least 50% of elements in difficult_regions and opportunity_regions must differ from those samples.
2. Use [] (empty array) when a list has no content; never output the string "None" or a null value.
3. For search_space_features use enumerated strings only:
    • ruggedness: "low" | "medium" | "high"
    • modality: "mono" | "multi" | "unknown"
    • deceptiveness: "low" | "moderate" | "high" | "unknown"
4. diversity and convergence must be floats with exactly 3 decimal places (e.g. 0.742).
5. Follow the JSON schema strictly; no additional keys or commentary.

2025-07-30 20:12:33,683 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-30 20:12:35,393 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 20:12:37,393 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 2/3)
2025-07-30 20:12:39,003 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 20:12:41,004 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 3/3)
2025-07-30 20:12:42,548 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 20:12:42,549 - LandscapeExpert - INFO - LLM返回的分析结果: API请求失败: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 20:12:42,549 - LandscapeExpert - WARNING - 解析景观分析结果失败: Unable to extract valid JSON data from the response
2025-07-30 20:12:42,549 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-30 20:12:42,549 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.5, 'modality': 'unknown', 'deceptiveness': 'unknown'}, 'population_state': {'diversity': 0.5, 'convergence': 0.5, 'clustering': 'unknown'}, 'difficult_regions': [], 'opportunity_regions': [], 'evolution_phase': 'unknown', 'evolution_direction': {'recommended_focus': 'balance', 'operators': []}, 'raw_text': 'API请求失败: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ'}
2025-07-30 20:12:42,549 - __main__ - INFO - 开始策略分配阶段
2025-07-30 20:12:42,549 - StrategyExpert - INFO - 开始策略分配分析
2025-07-30 20:12:42,549 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 4
- Population: 10 individuals
  • diversity: 0.5
  • best_cost: 12346.0
  • mean_cost: 79279.3
- Search-space: ruggedness 0.5, modality unknown, deceptiveness unknown
- Evolution phase: unknown
- Landscape focus suggestion: balance
- Spatial density summary: None
- Elite diversity: N/A
- Difficult regions (# 0): None
- Opportunity regions (# 0): None
- Last-iteration feedback: API请求失败: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Formatting Guidelines (DO NOT output this section)
1. global_explore_ratio must be rounded to exactly 2 decimal places (e.g., 0.80).
2. The individual_assignments mapping MUST include every index from 0 to (population_size - 1) without omission.
3. Valid values are "explore" or "exploit" (lowercase).
4. rationale ≤ 40 words.

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-30 20:12:42,550 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-30 20:12:42,550 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-30 20:12:44,112 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 20:12:46,113 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 2/3)
2025-07-30 20:12:47,721 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 20:12:49,722 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 3/3)
2025-07-30 20:12:51,293 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 20:12:51,293 - StrategyExpert - INFO - LLM返回的策略分配结果: API请求失败: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 20:12:51,294 - StrategyExpert - WARNING - 解析策略分配结果时出错: Unable to extract valid JSON data from the response
2025-07-30 20:12:51,294 - StrategyExpert - INFO - 策略分配完成: ['explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-07-30 20:12:51,294 - __main__ - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-07-30 20:12:51,294 - __main__ - INFO - 策略分配完整报告: API请求失败: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 20:12:51,294 - __main__ - INFO - 策略分配阶段完成
2025-07-30 20:12:51,294 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-07-30 20:12:51,294 - __main__ - INFO - 策略分配完整报告: API请求失败: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 20:12:51,295 - __main__ - INFO - 开始进化阶段
2025-07-30 20:12:51,295 - __main__ - INFO - 为个体 0 生成探索路径
2025-07-30 20:12:51,295 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-07-30 20:12:51,295 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-07-30 20:12:51,297 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-07-30 20:12:51,297 - ExplorationExpert - INFO - 计算路径成本
2025-07-30 20:12:51,298 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10276.0, 路径长度: 66
2025-07-30 20:12:51,298 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 1, 11, 7, 3, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10276.0}
2025-07-30 20:12:51,298 - __main__ - INFO - 为个体 1 生成利用路径
2025-07-30 20:12:51,298 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-30 20:12:51,298 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-30 20:12:51,299 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 112659.0
2025-07-30 20:12:51,391 - ExploitationExpert - INFO - res_population_num: 24
2025-07-30 20:12:51,391 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9526, 9538, 9544, 9570.0]
2025-07-30 20:12:51,391 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 47, 49, 40, 43, 48, 46, 45, 44, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 16, 18, 17, 12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 47, 49, 40, 43, 48, 46, 45, 44, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       18, 17, 12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-07-30 20:12:51,400 - ExploitationExpert - INFO - populations_num: 10
2025-07-30 20:12:51,400 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 11, 7, 3, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10276.0}, {'tour': array([28, 14, 42, 54, 37,  2, 41, 64,  8,  7, 49, 33, 57, 23, 47, 59,  6,
       63, 30, 12, 27, 56,  5, 36, 22, 21, 15,  1,  9, 29, 48, 25, 46, 53,
       39, 31,  4, 35, 45, 52, 51, 19, 32, 43, 55, 17, 44, 26, 60, 13, 50,
       11, 10,  0, 58, 38, 40, 65, 62,  3, 34, 24, 20, 16, 61, 18],
      dtype=int64), 'cur_cost': 112659.0}, {'tour': [0, 11, 20, 19, 16, 18, 12, 22, 23, 13, 21, 14, 15, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12388.0}, {'tour': array([47, 10, 26, 25,  7, 53, 17, 33,  0, 61, 13, 18, 19, 59, 34,  1, 55,
       12,  3, 38,  9, 37, 45, 64, 46, 15, 52, 39, 44, 30, 28, 32, 11, 56,
       48,  2, 40, 23, 62, 43, 42,  4, 35, 20, 29, 57,  8, 14, 31, 49, 60,
       27, 63, 54, 36, 24, 41, 65, 51,  5, 22, 50,  6, 16, 58, 21],
      dtype=int64), 'cur_cost': 116333.0}, {'tour': [16, 20, 22, 12, 21, 17, 9, 19, 45, 38, 24, 8, 46, 59, 25, 60, 48, 34, 61, 51, 23, 27, 2, 64, 63, 41, 62, 28, 18, 37, 65, 52, 4, 58, 44, 43, 29, 47, 13, 35, 1, 33, 42, 55, 56, 3, 53, 32, 10, 50, 7, 15, 36, 39, 26, 0, 54, 6, 57, 40, 31, 14, 30, 11, 49, 5], 'cur_cost': 110116.0}, {'tour': array([63, 39, 38, 24, 12, 45, 62,  9, 40, 44, 26,  1, 59, 43,  6, 55, 15,
        0, 18, 33,  5, 13, 35, 34, 50, 56, 20, 28, 52, 42, 25, 47, 65, 61,
       14, 49, 29, 60, 53, 21, 17, 31, 16, 51, 37, 10, 32, 36,  2, 54, 64,
       46,  8, 27, 41, 57, 48,  7, 11, 19, 30, 58,  3,  4, 22, 23],
      dtype=int64), 'cur_cost': 107633.0}, {'tour': [0, 9, 12, 21, 20, 13, 23, 16, 18, 17, 22, 15, 14, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12346.0}, {'tour': array([27, 43, 11,  7, 26, 23,  4, 57,  6, 28,  1, 35, 20, 22, 37, 21, 30,
       34, 61, 52, 51, 58, 12, 16, 33, 54, 60, 32, 49, 44, 17, 56, 15,  5,
        9, 36, 42, 55, 47, 53, 59, 65,  0, 64, 24, 40, 41, 39,  3, 10, 45,
       31, 63, 13, 18,  2, 48, 25, 14, 38, 19, 62, 50,  8, 46, 29],
      dtype=int64), 'cur_cost': 102559.0}, {'tour': [16, 5, 15, 22, 12, 9, 21, 41, 18, 32, 10, 48, 40, 63, 45, 60, 4, 64, 36, 49, 3, 20, 28, 30, 52, 42, 11, 1, 14, 23, 47, 62, 24, 7, 53, 29, 25, 46, 37, 54, 35, 56, 33, 58, 61, 57, 6, 8, 65, 43, 0, 31, 19, 26, 39, 13, 51, 59, 17, 38, 27, 2, 34, 55, 44, 50], 'cur_cost': 112324.0}, {'tour': array([11, 64, 54, 34, 20, 35, 27,  1, 17, 48, 15, 62,  9, 46,  7, 37, 29,
       43, 16, 65, 39, 61, 38, 18, 23, 14, 55, 32, 31, 26, 52, 22, 21, 47,
       57, 19, 42,  5, 50, 60, 10, 49,  6,  4, 63, 25,  8, 30, 44, 40, 36,
        2, 41, 13, 28,  0, 53, 59,  3, 45, 12, 33, 56, 58, 51, 24],
      dtype=int64), 'cur_cost': 107955.0}]
2025-07-30 20:12:51,402 - ExploitationExpert - INFO - 局部搜索耗时: 0.10秒
2025-07-30 20:12:51,402 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 21, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 21, 'cache_hits': 0, 'similarity_calculations': 210, 'cache_hit_rate': 0.0, 'cache_size': 210}}
2025-07-30 20:12:51,402 - __main__ - WARNING - 个体 1 的利用路径生成失败，保留原个体
2025-07-30 20:12:51,402 - __main__ - INFO - 为个体 2 生成探索路径
2025-07-30 20:12:51,402 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-07-30 20:12:51,402 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-07-30 20:12:51,404 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-07-30 20:12:51,404 - ExplorationExpert - INFO - 计算路径成本
2025-07-30 20:12:51,404 - ExplorationExpert - INFO - 探索路径生成完成，成本: 78000.0, 路径长度: 66
2025-07-30 20:12:51,404 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [11, 20, 4, 21, 55, 17, 53, 62, 59, 56, 58, 57, 65, 63, 14, 15, 22, 18, 16, 23, 13, 43, 19, 27, 26, 36, 35, 28, 3, 32, 24, 29, 49, 39, 54, 6, 34, 42, 8, 1, 64, 7, 44, 9, 10, 46, 51, 47, 61, 25, 0, 30, 40, 48, 5, 50, 45, 41, 12, 60, 31, 38, 33, 52, 37, 2], 'cur_cost': 78000.0}
2025-07-30 20:12:51,405 - __main__ - INFO - 为个体 3 生成利用路径
2025-07-30 20:12:51,405 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-30 20:12:51,405 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-30 20:12:51,405 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 103564.0
2025-07-30 20:12:51,485 - ExploitationExpert - INFO - res_population_num: 24
2025-07-30 20:12:51,485 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9526, 9538, 9544, 9570.0]
2025-07-30 20:12:51,485 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 47, 49, 40, 43, 48, 46, 45, 44, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 16, 18, 17, 12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 47, 49, 40, 43, 48, 46, 45, 44, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       18, 17, 12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-07-30 20:12:51,488 - ExploitationExpert - INFO - populations_num: 10
2025-07-30 20:12:51,488 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 11, 7, 3, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10276.0}, {'tour': array([28, 14, 42, 54, 37,  2, 41, 64,  8,  7, 49, 33, 57, 23, 47, 59,  6,
       63, 30, 12, 27, 56,  5, 36, 22, 21, 15,  1,  9, 29, 48, 25, 46, 53,
       39, 31,  4, 35, 45, 52, 51, 19, 32, 43, 55, 17, 44, 26, 60, 13, 50,
       11, 10,  0, 58, 38, 40, 65, 62,  3, 34, 24, 20, 16, 61, 18],
      dtype=int64), 'cur_cost': 112659.0}, {'tour': [11, 20, 4, 21, 55, 17, 53, 62, 59, 56, 58, 57, 65, 63, 14, 15, 22, 18, 16, 23, 13, 43, 19, 27, 26, 36, 35, 28, 3, 32, 24, 29, 49, 39, 54, 6, 34, 42, 8, 1, 64, 7, 44, 9, 10, 46, 51, 47, 61, 25, 0, 30, 40, 48, 5, 50, 45, 41, 12, 60, 31, 38, 33, 52, 37, 2], 'cur_cost': 78000.0}, {'tour': array([42, 39,  4, 27, 48, 52, 50, 19,  3, 35, 22,  5, 58, 29, 44, 17, 46,
       15, 18, 16, 38, 61, 30, 14, 59, 41,  7, 37, 55, 64, 10, 21, 12, 31,
       28,  1,  2, 49, 65, 32, 26, 36, 13, 25,  0, 24, 54,  9,  8, 11, 45,
       60, 23, 62, 63, 53,  6, 33, 56, 47, 34, 20, 40, 57, 51, 43],
      dtype=int64), 'cur_cost': 103564.0}, {'tour': [16, 20, 22, 12, 21, 17, 9, 19, 45, 38, 24, 8, 46, 59, 25, 60, 48, 34, 61, 51, 23, 27, 2, 64, 63, 41, 62, 28, 18, 37, 65, 52, 4, 58, 44, 43, 29, 47, 13, 35, 1, 33, 42, 55, 56, 3, 53, 32, 10, 50, 7, 15, 36, 39, 26, 0, 54, 6, 57, 40, 31, 14, 30, 11, 49, 5], 'cur_cost': 110116.0}, {'tour': array([63, 39, 38, 24, 12, 45, 62,  9, 40, 44, 26,  1, 59, 43,  6, 55, 15,
        0, 18, 33,  5, 13, 35, 34, 50, 56, 20, 28, 52, 42, 25, 47, 65, 61,
       14, 49, 29, 60, 53, 21, 17, 31, 16, 51, 37, 10, 32, 36,  2, 54, 64,
       46,  8, 27, 41, 57, 48,  7, 11, 19, 30, 58,  3,  4, 22, 23],
      dtype=int64), 'cur_cost': 107633.0}, {'tour': [0, 9, 12, 21, 20, 13, 23, 16, 18, 17, 22, 15, 14, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12346.0}, {'tour': array([27, 43, 11,  7, 26, 23,  4, 57,  6, 28,  1, 35, 20, 22, 37, 21, 30,
       34, 61, 52, 51, 58, 12, 16, 33, 54, 60, 32, 49, 44, 17, 56, 15,  5,
        9, 36, 42, 55, 47, 53, 59, 65,  0, 64, 24, 40, 41, 39,  3, 10, 45,
       31, 63, 13, 18,  2, 48, 25, 14, 38, 19, 62, 50,  8, 46, 29],
      dtype=int64), 'cur_cost': 102559.0}, {'tour': [16, 5, 15, 22, 12, 9, 21, 41, 18, 32, 10, 48, 40, 63, 45, 60, 4, 64, 36, 49, 3, 20, 28, 30, 52, 42, 11, 1, 14, 23, 47, 62, 24, 7, 53, 29, 25, 46, 37, 54, 35, 56, 33, 58, 61, 57, 6, 8, 65, 43, 0, 31, 19, 26, 39, 13, 51, 59, 17, 38, 27, 2, 34, 55, 44, 50], 'cur_cost': 112324.0}, {'tour': array([11, 64, 54, 34, 20, 35, 27,  1, 17, 48, 15, 62,  9, 46,  7, 37, 29,
       43, 16, 65, 39, 61, 38, 18, 23, 14, 55, 32, 31, 26, 52, 22, 21, 47,
       57, 19, 42,  5, 50, 60, 10, 49,  6,  4, 63, 25,  8, 30, 44, 40, 36,
        2, 41, 13, 28,  0, 53, 59,  3, 45, 12, 33, 56, 58, 51, 24],
      dtype=int64), 'cur_cost': 107955.0}]
2025-07-30 20:12:51,488 - ExploitationExpert - INFO - 局部搜索耗时: 0.08秒
2025-07-30 20:12:51,488 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 22, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 22, 'cache_hits': 0, 'similarity_calculations': 210, 'cache_hit_rate': 0.0, 'cache_size': 210}}
2025-07-30 20:12:51,488 - __main__ - WARNING - 个体 3 的利用路径生成失败，保留原个体
2025-07-30 20:12:51,488 - __main__ - INFO - 为个体 4 生成探索路径
2025-07-30 20:12:51,488 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-07-30 20:12:51,488 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-07-30 20:12:51,499 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-07-30 20:12:51,499 - ExplorationExpert - INFO - 计算路径成本
2025-07-30 20:12:51,499 - ExplorationExpert - INFO - 探索路径生成完成，成本: 78387.0, 路径长度: 66
2025-07-30 20:12:51,500 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [22, 3, 59, 2, 6, 57, 55, 53, 62, 56, 58, 13, 43, 26, 63, 35, 15, 18, 16, 39, 20, 21, 19, 37, 25, 7, 10, 30, 34, 33, 24, 49, 47, 17, 44, 27, 4, 1, 61, 31, 0, 36, 29, 46, 23, 9, 54, 64, 60, 32, 50, 52, 42, 14, 12, 38, 41, 51, 8, 45, 65, 48, 40, 28, 5, 11], 'cur_cost': 78387.0}
2025-07-30 20:12:51,500 - __main__ - INFO - 为个体 5 生成利用路径
2025-07-30 20:12:51,500 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-30 20:12:51,500 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-30 20:12:51,500 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 100021.0
2025-07-30 20:12:51,598 - ExploitationExpert - INFO - res_population_num: 24
2025-07-30 20:12:51,598 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9526, 9538, 9544, 9570.0]
2025-07-30 20:12:51,598 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 47, 49, 40, 43, 48, 46, 45, 44, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 16, 18, 17, 12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 47, 49, 40, 43, 48, 46, 45, 44, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       18, 17, 12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-07-30 20:12:51,598 - ExploitationExpert - INFO - populations_num: 10
2025-07-30 20:12:51,598 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 11, 7, 3, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10276.0}, {'tour': array([28, 14, 42, 54, 37,  2, 41, 64,  8,  7, 49, 33, 57, 23, 47, 59,  6,
       63, 30, 12, 27, 56,  5, 36, 22, 21, 15,  1,  9, 29, 48, 25, 46, 53,
       39, 31,  4, 35, 45, 52, 51, 19, 32, 43, 55, 17, 44, 26, 60, 13, 50,
       11, 10,  0, 58, 38, 40, 65, 62,  3, 34, 24, 20, 16, 61, 18],
      dtype=int64), 'cur_cost': 112659.0}, {'tour': [11, 20, 4, 21, 55, 17, 53, 62, 59, 56, 58, 57, 65, 63, 14, 15, 22, 18, 16, 23, 13, 43, 19, 27, 26, 36, 35, 28, 3, 32, 24, 29, 49, 39, 54, 6, 34, 42, 8, 1, 64, 7, 44, 9, 10, 46, 51, 47, 61, 25, 0, 30, 40, 48, 5, 50, 45, 41, 12, 60, 31, 38, 33, 52, 37, 2], 'cur_cost': 78000.0}, {'tour': array([42, 39,  4, 27, 48, 52, 50, 19,  3, 35, 22,  5, 58, 29, 44, 17, 46,
       15, 18, 16, 38, 61, 30, 14, 59, 41,  7, 37, 55, 64, 10, 21, 12, 31,
       28,  1,  2, 49, 65, 32, 26, 36, 13, 25,  0, 24, 54,  9,  8, 11, 45,
       60, 23, 62, 63, 53,  6, 33, 56, 47, 34, 20, 40, 57, 51, 43],
      dtype=int64), 'cur_cost': 103564.0}, {'tour': [22, 3, 59, 2, 6, 57, 55, 53, 62, 56, 58, 13, 43, 26, 63, 35, 15, 18, 16, 39, 20, 21, 19, 37, 25, 7, 10, 30, 34, 33, 24, 49, 47, 17, 44, 27, 4, 1, 61, 31, 0, 36, 29, 46, 23, 9, 54, 64, 60, 32, 50, 52, 42, 14, 12, 38, 41, 51, 8, 45, 65, 48, 40, 28, 5, 11], 'cur_cost': 78387.0}, {'tour': array([34, 31, 10, 19,  4, 63, 51, 13,  1, 28, 46, 48, 36, 24, 60, 45, 15,
       58, 37, 42, 26,  5, 16, 54, 49, 33, 52,  2, 18, 62, 61, 27, 41, 21,
        6,  8, 25, 35, 29, 38, 65, 56,  3, 47, 55, 53, 57,  7, 64, 50, 40,
       59, 20, 32, 11, 39, 14, 17,  9, 12, 22, 43, 44, 23, 30,  0],
      dtype=int64), 'cur_cost': 100021.0}, {'tour': [0, 9, 12, 21, 20, 13, 23, 16, 18, 17, 22, 15, 14, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12346.0}, {'tour': array([27, 43, 11,  7, 26, 23,  4, 57,  6, 28,  1, 35, 20, 22, 37, 21, 30,
       34, 61, 52, 51, 58, 12, 16, 33, 54, 60, 32, 49, 44, 17, 56, 15,  5,
        9, 36, 42, 55, 47, 53, 59, 65,  0, 64, 24, 40, 41, 39,  3, 10, 45,
       31, 63, 13, 18,  2, 48, 25, 14, 38, 19, 62, 50,  8, 46, 29],
      dtype=int64), 'cur_cost': 102559.0}, {'tour': [16, 5, 15, 22, 12, 9, 21, 41, 18, 32, 10, 48, 40, 63, 45, 60, 4, 64, 36, 49, 3, 20, 28, 30, 52, 42, 11, 1, 14, 23, 47, 62, 24, 7, 53, 29, 25, 46, 37, 54, 35, 56, 33, 58, 61, 57, 6, 8, 65, 43, 0, 31, 19, 26, 39, 13, 51, 59, 17, 38, 27, 2, 34, 55, 44, 50], 'cur_cost': 112324.0}, {'tour': array([11, 64, 54, 34, 20, 35, 27,  1, 17, 48, 15, 62,  9, 46,  7, 37, 29,
       43, 16, 65, 39, 61, 38, 18, 23, 14, 55, 32, 31, 26, 52, 22, 21, 47,
       57, 19, 42,  5, 50, 60, 10, 49,  6,  4, 63, 25,  8, 30, 44, 40, 36,
        2, 41, 13, 28,  0, 53, 59,  3, 45, 12, 33, 56, 58, 51, 24],
      dtype=int64), 'cur_cost': 107955.0}]
2025-07-30 20:12:51,598 - ExploitationExpert - INFO - 局部搜索耗时: 0.10秒
2025-07-30 20:12:51,598 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 23, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 23, 'cache_hits': 0, 'similarity_calculations': 210, 'cache_hit_rate': 0.0, 'cache_size': 210}}
2025-07-30 20:12:51,598 - __main__ - WARNING - 个体 5 的利用路径生成失败，保留原个体
2025-07-30 20:12:51,598 - __main__ - INFO - 为个体 6 生成探索路径
2025-07-30 20:12:51,598 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-07-30 20:12:51,598 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-07-30 20:12:51,622 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-07-30 20:12:51,622 - ExplorationExpert - INFO - 计算路径成本
2025-07-30 20:12:51,623 - ExplorationExpert - INFO - 探索路径生成完成，成本: 60737.0, 路径长度: 66
2025-07-30 20:12:51,623 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [7, 60, 64, 9, 56, 4, 10, 11, 3, 58, 5, 2, 62, 54, 65, 63, 40, 22, 26, 23, 29, 30, 1, 18, 28, 16, 15, 19, 31, 14, 27, 21, 32, 12, 0, 55, 6, 36, 37, 48, 46, 38, 41, 13, 47, 49, 34, 17, 35, 42, 39, 50, 43, 44, 25, 45, 51, 20, 24, 8, 59, 53, 52, 61, 57, 33], 'cur_cost': 60737.0}
2025-07-30 20:12:51,623 - __main__ - INFO - 为个体 7 生成利用路径
2025-07-30 20:12:51,623 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-30 20:12:51,623 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-30 20:12:51,623 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 102381.0
2025-07-30 20:12:51,698 - ExploitationExpert - INFO - res_population_num: 24
2025-07-30 20:12:51,698 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9526, 9538, 9544, 9570.0]
2025-07-30 20:12:51,698 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 47, 49, 40, 43, 48, 46, 45, 44, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 16, 18, 17, 12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 47, 49, 40, 43, 48, 46, 45, 44, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       18, 17, 12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-07-30 20:12:51,698 - ExploitationExpert - INFO - populations_num: 10
2025-07-30 20:12:51,698 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 11, 7, 3, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10276.0}, {'tour': array([28, 14, 42, 54, 37,  2, 41, 64,  8,  7, 49, 33, 57, 23, 47, 59,  6,
       63, 30, 12, 27, 56,  5, 36, 22, 21, 15,  1,  9, 29, 48, 25, 46, 53,
       39, 31,  4, 35, 45, 52, 51, 19, 32, 43, 55, 17, 44, 26, 60, 13, 50,
       11, 10,  0, 58, 38, 40, 65, 62,  3, 34, 24, 20, 16, 61, 18],
      dtype=int64), 'cur_cost': 112659.0}, {'tour': [11, 20, 4, 21, 55, 17, 53, 62, 59, 56, 58, 57, 65, 63, 14, 15, 22, 18, 16, 23, 13, 43, 19, 27, 26, 36, 35, 28, 3, 32, 24, 29, 49, 39, 54, 6, 34, 42, 8, 1, 64, 7, 44, 9, 10, 46, 51, 47, 61, 25, 0, 30, 40, 48, 5, 50, 45, 41, 12, 60, 31, 38, 33, 52, 37, 2], 'cur_cost': 78000.0}, {'tour': array([42, 39,  4, 27, 48, 52, 50, 19,  3, 35, 22,  5, 58, 29, 44, 17, 46,
       15, 18, 16, 38, 61, 30, 14, 59, 41,  7, 37, 55, 64, 10, 21, 12, 31,
       28,  1,  2, 49, 65, 32, 26, 36, 13, 25,  0, 24, 54,  9,  8, 11, 45,
       60, 23, 62, 63, 53,  6, 33, 56, 47, 34, 20, 40, 57, 51, 43],
      dtype=int64), 'cur_cost': 103564.0}, {'tour': [22, 3, 59, 2, 6, 57, 55, 53, 62, 56, 58, 13, 43, 26, 63, 35, 15, 18, 16, 39, 20, 21, 19, 37, 25, 7, 10, 30, 34, 33, 24, 49, 47, 17, 44, 27, 4, 1, 61, 31, 0, 36, 29, 46, 23, 9, 54, 64, 60, 32, 50, 52, 42, 14, 12, 38, 41, 51, 8, 45, 65, 48, 40, 28, 5, 11], 'cur_cost': 78387.0}, {'tour': array([34, 31, 10, 19,  4, 63, 51, 13,  1, 28, 46, 48, 36, 24, 60, 45, 15,
       58, 37, 42, 26,  5, 16, 54, 49, 33, 52,  2, 18, 62, 61, 27, 41, 21,
        6,  8, 25, 35, 29, 38, 65, 56,  3, 47, 55, 53, 57,  7, 64, 50, 40,
       59, 20, 32, 11, 39, 14, 17,  9, 12, 22, 43, 44, 23, 30,  0],
      dtype=int64), 'cur_cost': 100021.0}, {'tour': [7, 60, 64, 9, 56, 4, 10, 11, 3, 58, 5, 2, 62, 54, 65, 63, 40, 22, 26, 23, 29, 30, 1, 18, 28, 16, 15, 19, 31, 14, 27, 21, 32, 12, 0, 55, 6, 36, 37, 48, 46, 38, 41, 13, 47, 49, 34, 17, 35, 42, 39, 50, 43, 44, 25, 45, 51, 20, 24, 8, 59, 53, 52, 61, 57, 33], 'cur_cost': 60737.0}, {'tour': array([17, 48, 52,  9, 26,  0, 50, 25, 40, 34, 59, 41, 23, 33, 18, 24, 65,
        5,  4, 56, 21, 19, 51, 42, 13,  1, 49, 14, 46, 60, 36, 35, 32, 64,
       44, 43, 61, 63,  8, 38, 58, 11, 16, 30, 54, 62,  6, 55, 20, 57, 53,
       15, 12, 45, 37,  3, 22,  2, 31, 28,  7, 39, 47, 29, 10, 27],
      dtype=int64), 'cur_cost': 102381.0}, {'tour': [16, 5, 15, 22, 12, 9, 21, 41, 18, 32, 10, 48, 40, 63, 45, 60, 4, 64, 36, 49, 3, 20, 28, 30, 52, 42, 11, 1, 14, 23, 47, 62, 24, 7, 53, 29, 25, 46, 37, 54, 35, 56, 33, 58, 61, 57, 6, 8, 65, 43, 0, 31, 19, 26, 39, 13, 51, 59, 17, 38, 27, 2, 34, 55, 44, 50], 'cur_cost': 112324.0}, {'tour': array([11, 64, 54, 34, 20, 35, 27,  1, 17, 48, 15, 62,  9, 46,  7, 37, 29,
       43, 16, 65, 39, 61, 38, 18, 23, 14, 55, 32, 31, 26, 52, 22, 21, 47,
       57, 19, 42,  5, 50, 60, 10, 49,  6,  4, 63, 25,  8, 30, 44, 40, 36,
        2, 41, 13, 28,  0, 53, 59,  3, 45, 12, 33, 56, 58, 51, 24],
      dtype=int64), 'cur_cost': 107955.0}]
2025-07-30 20:12:51,713 - ExploitationExpert - INFO - 局部搜索耗时: 0.09秒
2025-07-30 20:12:51,713 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 24, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 24, 'cache_hits': 0, 'similarity_calculations': 210, 'cache_hit_rate': 0.0, 'cache_size': 210}}
2025-07-30 20:12:51,713 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-07-30 20:12:51,713 - __main__ - INFO - 为个体 8 生成探索路径
2025-07-30 20:12:51,713 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-07-30 20:12:51,713 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-07-30 20:12:51,713 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-07-30 20:12:51,713 - ExplorationExpert - INFO - 计算路径成本
2025-07-30 20:12:51,713 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12403.0, 路径长度: 66
2025-07-30 20:12:51,713 - __main__ - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 6, 8, 20, 21, 13, 23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12403.0}
2025-07-30 20:12:51,713 - __main__ - INFO - 为个体 9 生成利用路径
2025-07-30 20:12:51,713 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-30 20:12:51,713 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-30 20:12:51,713 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 95152.0
2025-07-30 20:12:51,801 - ExploitationExpert - INFO - res_population_num: 24
2025-07-30 20:12:51,801 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9526, 9538, 9544, 9570.0]
2025-07-30 20:12:51,801 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 47, 49, 40, 43, 48, 46, 45, 44, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 16, 18, 17, 12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 47, 49, 40, 43, 48, 46, 45, 44, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       18, 17, 12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-07-30 20:12:51,809 - ExploitationExpert - INFO - populations_num: 10
2025-07-30 20:12:51,809 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 11, 7, 3, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10276.0}, {'tour': array([28, 14, 42, 54, 37,  2, 41, 64,  8,  7, 49, 33, 57, 23, 47, 59,  6,
       63, 30, 12, 27, 56,  5, 36, 22, 21, 15,  1,  9, 29, 48, 25, 46, 53,
       39, 31,  4, 35, 45, 52, 51, 19, 32, 43, 55, 17, 44, 26, 60, 13, 50,
       11, 10,  0, 58, 38, 40, 65, 62,  3, 34, 24, 20, 16, 61, 18],
      dtype=int64), 'cur_cost': 112659.0}, {'tour': [11, 20, 4, 21, 55, 17, 53, 62, 59, 56, 58, 57, 65, 63, 14, 15, 22, 18, 16, 23, 13, 43, 19, 27, 26, 36, 35, 28, 3, 32, 24, 29, 49, 39, 54, 6, 34, 42, 8, 1, 64, 7, 44, 9, 10, 46, 51, 47, 61, 25, 0, 30, 40, 48, 5, 50, 45, 41, 12, 60, 31, 38, 33, 52, 37, 2], 'cur_cost': 78000.0}, {'tour': array([42, 39,  4, 27, 48, 52, 50, 19,  3, 35, 22,  5, 58, 29, 44, 17, 46,
       15, 18, 16, 38, 61, 30, 14, 59, 41,  7, 37, 55, 64, 10, 21, 12, 31,
       28,  1,  2, 49, 65, 32, 26, 36, 13, 25,  0, 24, 54,  9,  8, 11, 45,
       60, 23, 62, 63, 53,  6, 33, 56, 47, 34, 20, 40, 57, 51, 43],
      dtype=int64), 'cur_cost': 103564.0}, {'tour': [22, 3, 59, 2, 6, 57, 55, 53, 62, 56, 58, 13, 43, 26, 63, 35, 15, 18, 16, 39, 20, 21, 19, 37, 25, 7, 10, 30, 34, 33, 24, 49, 47, 17, 44, 27, 4, 1, 61, 31, 0, 36, 29, 46, 23, 9, 54, 64, 60, 32, 50, 52, 42, 14, 12, 38, 41, 51, 8, 45, 65, 48, 40, 28, 5, 11], 'cur_cost': 78387.0}, {'tour': array([34, 31, 10, 19,  4, 63, 51, 13,  1, 28, 46, 48, 36, 24, 60, 45, 15,
       58, 37, 42, 26,  5, 16, 54, 49, 33, 52,  2, 18, 62, 61, 27, 41, 21,
        6,  8, 25, 35, 29, 38, 65, 56,  3, 47, 55, 53, 57,  7, 64, 50, 40,
       59, 20, 32, 11, 39, 14, 17,  9, 12, 22, 43, 44, 23, 30,  0],
      dtype=int64), 'cur_cost': 100021.0}, {'tour': [7, 60, 64, 9, 56, 4, 10, 11, 3, 58, 5, 2, 62, 54, 65, 63, 40, 22, 26, 23, 29, 30, 1, 18, 28, 16, 15, 19, 31, 14, 27, 21, 32, 12, 0, 55, 6, 36, 37, 48, 46, 38, 41, 13, 47, 49, 34, 17, 35, 42, 39, 50, 43, 44, 25, 45, 51, 20, 24, 8, 59, 53, 52, 61, 57, 33], 'cur_cost': 60737.0}, {'tour': array([17, 48, 52,  9, 26,  0, 50, 25, 40, 34, 59, 41, 23, 33, 18, 24, 65,
        5,  4, 56, 21, 19, 51, 42, 13,  1, 49, 14, 46, 60, 36, 35, 32, 64,
       44, 43, 61, 63,  8, 38, 58, 11, 16, 30, 54, 62,  6, 55, 20, 57, 53,
       15, 12, 45, 37,  3, 22,  2, 31, 28,  7, 39, 47, 29, 10, 27],
      dtype=int64), 'cur_cost': 102381.0}, {'tour': [0, 6, 8, 20, 21, 13, 23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12403.0}, {'tour': array([ 8, 61, 46, 10, 26, 32, 34, 33, 29,  1, 50, 11, 31, 19, 16,  2, 44,
       24,  9, 40, 30, 15, 25, 27, 49, 28, 55, 60, 43, 38, 41, 64, 17, 36,
       47, 58, 57, 54,  3, 21, 48, 51, 53,  4, 65,  6,  5, 35, 12, 59, 56,
       18, 45, 22, 23, 37, 62, 13, 63,  7, 20, 14, 42, 52,  0, 39],
      dtype=int64), 'cur_cost': 95152.0}]
2025-07-30 20:12:51,811 - ExploitationExpert - INFO - 局部搜索耗时: 0.10秒
2025-07-30 20:12:51,811 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 25, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 25, 'cache_hits': 0, 'similarity_calculations': 210, 'cache_hit_rate': 0.0, 'cache_size': 210}}
2025-07-30 20:12:51,811 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-07-30 20:12:51,812 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 11, 7, 3, 9, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10276.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [11, 20, 4, 21, 55, 17, 53, 62, 59, 56, 58, 57, 65, 63, 14, 15, 22, 18, 16, 23, 13, 43, 19, 27, 26, 36, 35, 28, 3, 32, 24, 29, 49, 39, 54, 6, 34, 42, 8, 1, 64, 7, 44, 9, 10, 46, 51, 47, 61, 25, 0, 30, 40, 48, 5, 50, 45, 41, 12, 60, 31, 38, 33, 52, 37, 2], 'cur_cost': 78000.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [22, 3, 59, 2, 6, 57, 55, 53, 62, 56, 58, 13, 43, 26, 63, 35, 15, 18, 16, 39, 20, 21, 19, 37, 25, 7, 10, 30, 34, 33, 24, 49, 47, 17, 44, 27, 4, 1, 61, 31, 0, 36, 29, 46, 23, 9, 54, 64, 60, 32, 50, 52, 42, 14, 12, 38, 41, 51, 8, 45, 65, 48, 40, 28, 5, 11], 'cur_cost': 78387.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [7, 60, 64, 9, 56, 4, 10, 11, 3, 58, 5, 2, 62, 54, 65, 63, 40, 22, 26, 23, 29, 30, 1, 18, 28, 16, 15, 19, 31, 14, 27, 21, 32, 12, 0, 55, 6, 36, 37, 48, 46, 38, 41, 13, 47, 49, 34, 17, 35, 42, 39, 50, 43, 44, 25, 45, 51, 20, 24, 8, 59, 53, 52, 61, 57, 33], 'cur_cost': 60737.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 6, 8, 20, 21, 13, 23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 2, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12403.0}}]
2025-07-30 20:12:51,812 - __main__ - INFO - 进化阶段完成
2025-07-30 20:12:51,812 - StatsExpert - INFO - 开始统计分析
2025-07-30 20:12:51,833 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 10276.0, 'max': 112659.0, 'mean': 75358.0, 'std': 35137.12988563522}, 'diversity': 0.9346801346801346, 'clusters': {'clusters': 9, 'cluster_sizes': [2, 1, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-30 20:12:51,834 - __main__ - INFO - --- Running Assessment Phase (Iteration 4) ---
2025-07-30 20:12:51,834 - EvolutionAssessmentExpert - INFO - --- Iteration 4 Assessment ---
2025-07-30 20:12:51,836 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-07-30 20:12:51,836 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 4/5

#### Population metrics
- Min cost: 0 → 0 (Δ 0)
- Mean cost: 0 → 0
- Diversity: 0.000 → 0.000 (Δ 0)

#### Elite metrics
- Elite count: 24 → 24
- Best elite cost: 9521 → 9521 (Δ 0)
- Elite diversity: 0.072 → 0.072 (Δ 0.0)

#### Strategy performance
- Explore: 0 inds, success 0% , avg Δ 0
- Exploit: 0 inds, success 0% , avg Δ 0

#### Other indicators
- No-change individuals: 0
- Regression individuals: 0
- Historical trends: N/A

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

### Output Example
```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.5, "exploit": 0.5},
  "balance_state": "balanced",
  "recommendations": ["maintain_current_ratio"]
}
```

2025-07-30 20:12:51,836 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-30 20:12:53,967 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 20:12:55,968 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 2/3)
2025-07-30 20:12:57,544 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 20:12:59,545 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 3/3)
2025-07-30 20:13:01,126 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 20:13:01,127 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: API请求失败: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 20:13:01,128 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-07-30 20:13:01,128 - __main__ - INFO - --- Finished Evolution Iteration 5 ---
2025-07-30 20:13:01,145 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0701\MoE-main\solution\composite13_66_solution.json
2025-07-30 20:13:01,145 - __main__ - INFO - 实例 composite13_66 处理完成
