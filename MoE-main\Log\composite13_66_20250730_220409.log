2025-07-30 22:04:09,156 - __main__ - INFO - composite13_66 开始进化第 1 代
2025-07-30 22:04:09,156 - collaboration_manager - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-07-30 22:04:09,156 - StatsExpert - INFO - 开始统计分析
2025-07-30 22:04:09,182 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=9972.0, 多样性=0.934
2025-07-30 22:04:09,183 - PathExpert - INFO - 开始路径结构分析
2025-07-30 22:04:09,188 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.025
2025-07-30 22:04:09,189 - EliteExpert - INFO - 开始精英解分析
2025-07-30 22:04:09,890 - LandscapeExpert - INFO - 开始景观分析
2025-07-30 22:04:09,890 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-30 22:04:09,890 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 0/5)
- population_size: 10
- cost_stats: min 9972.0, mean 76995.0, max 114265.0, std 46432.83958100727
- diversity: 0.9343434343434343
- convergence: 0
- clustering: No clustering information
- recent_best_costs: Not available (Δ Not available, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: []
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}
- elite_diversity: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

### Output Example
```json
{
  "search_space_features": {"ruggedness": 0.5, "modality": "multi", "deceptiveness": "unknown"},
  "population_state": {"diversity": 0.5, "convergence": 0.4, "clustering": "weak"},
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "unknown",
  "evolution_direction": {"recommended_focus": "explore", "operators": []}
}
```

### Formatting Guidelines (read carefully, DO NOT output this section)
1. Do NOT copy or repeat any *_sample values above; at least 50% of elements in difficult_regions and opportunity_regions must differ from those samples.
2. Use [] (empty array) when a list has no content; never output the string "None" or a null value.
3. For search_space_features use enumerated strings only:
    • ruggedness: "low" | "medium" | "high"
    • modality: "mono" | "multi" | "unknown"
    • deceptiveness: "low" | "moderate" | "high" | "unknown"
4. diversity and convergence must be floats with exactly 3 decimal places (e.g. 0.742).
5. Follow the JSON schema strictly; no additional keys or commentary.

2025-07-30 22:04:09,890 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-30 22:04:11,805 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 22:04:13,806 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 2/3)
2025-07-30 22:04:17,023 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 22:04:19,024 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 3/3)
2025-07-30 22:04:21,326 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 22:04:21,326 - LandscapeExpert - INFO - LLM返回的分析结果: API请求失败: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 22:04:21,326 - LandscapeExpert - WARNING - 解析景观分析结果失败: Unable to extract valid JSON data from the response
2025-07-30 22:04:21,326 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-30 22:04:21,326 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.5, 'modality': 'unknown', 'deceptiveness': 'unknown'}, 'population_state': {'diversity': 0.5, 'convergence': 0.5, 'clustering': 'unknown'}, 'difficult_regions': [], 'opportunity_regions': [], 'evolution_phase': 'unknown', 'evolution_direction': {'recommended_focus': 'balance', 'operators': []}, 'raw_text': 'API请求失败: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ'}
2025-07-30 22:04:21,326 - collaboration_manager - INFO - 开始策略分配阶段
2025-07-30 22:04:21,326 - StrategyExpert - INFO - 开始策略分配分析
2025-07-30 22:04:21,326 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 0
- Population: 10 individuals
  • diversity: 0.5
  • best_cost: 9972.0
  • mean_cost: 76995.0
- Search-space: ruggedness 0.5, modality unknown, deceptiveness unknown
- Evolution phase: unknown
- Landscape focus suggestion: balance
- Spatial density summary: None
- Elite diversity: N/A
- Difficult regions (# 0): None
- Opportunity regions (# 0): None
- Last-iteration feedback: None

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Formatting Guidelines (DO NOT output this section)
1. global_explore_ratio must be rounded to exactly 2 decimal places (e.g., 0.80).
2. The individual_assignments mapping MUST include every index from 0 to (population_size - 1) without omission.
3. Valid values are "explore" or "exploit" (lowercase).
4. rationale ≤ 40 words.

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-30 22:04:21,326 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-30 22:04:21,326 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-30 22:04:22,937 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 22:04:24,938 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 2/3)
2025-07-30 22:04:26,515 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 22:04:28,529 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 3/3)
2025-07-30 22:04:30,918 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 22:04:30,918 - StrategyExpert - INFO - LLM返回的策略分配结果: API请求失败: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 22:04:30,918 - StrategyExpert - WARNING - 解析策略分配结果时出错: Unable to extract valid JSON data from the response
2025-07-30 22:04:30,918 - StrategyExpert - INFO - 策略分配完成: ['explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-07-30 22:04:30,918 - collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-07-30 22:04:30,918 - collaboration_manager - INFO - 策略分配完整报告: API请求失败: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 22:04:30,918 - collaboration_manager - INFO - 策略分配阶段完成
2025-07-30 22:04:30,918 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-07-30 22:04:30,918 - __main__ - INFO - 策略分配完整报告: API请求失败: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 22:04:30,919 - collaboration_manager - INFO - 开始进化阶段
2025-07-30 22:04:30,919 - collaboration_manager - INFO - 为个体 0 生成探索路径
2025-07-30 22:04:30,920 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-07-30 22:04:30,920 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-07-30 22:04:30,920 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-07-30 22:04:30,920 - ExplorationExpert - INFO - 计算路径成本
2025-07-30 22:04:31,075 - ExplorationExpert - INFO - 探索路径生成完成，成本: 53027.0, 路径长度: 66
2025-07-30 22:04:31,076 - collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [12, 28, 24, 0, 15, 22, 26, 37, 36, 27, 35, 21, 20, 33, 9, 17, 23, 7, 58, 8, 18, 25, 31, 3, 59, 54, 64, 2, 10, 16, 6, 5, 56, 11, 57, 53, 52, 40, 42, 51, 46, 13, 43, 19, 49, 48, 45, 30, 14, 44, 38, 47, 41, 32, 34, 4, 63, 61, 62, 65, 55, 60, 1, 29, 39, 50], 'cur_cost': 53027.0}
2025-07-30 22:04:31,076 - collaboration_manager - INFO - 为个体 1 生成利用路径
2025-07-30 22:04:31,076 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-30 22:04:31,079 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-30 22:04:31,079 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 113745.0
2025-07-30 22:04:32,375 - ExploitationExpert - INFO - res_population_num: 1
2025-07-30 22:04:32,375 - ExploitationExpert - INFO - res_population_costs: [9546.0]
2025-07-30 22:04:32,375 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 43, 40,
       49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 54, 57, 64,
       53, 62, 59, 56, 55, 61, 65, 52, 63,  2,  6,  4,  5,  8, 10],
      dtype=int64)]
2025-07-30 22:04:32,376 - ExploitationExpert - INFO - populations_num: 10
2025-07-30 22:04:32,376 - ExploitationExpert - INFO - populations: [{'tour': [12, 28, 24, 0, 15, 22, 26, 37, 36, 27, 35, 21, 20, 33, 9, 17, 23, 7, 58, 8, 18, 25, 31, 3, 59, 54, 64, 2, 10, 16, 6, 5, 56, 11, 57, 53, 52, 40, 42, 51, 46, 13, 43, 19, 49, 48, 45, 30, 14, 44, 38, 47, 41, 32, 34, 4, 63, 61, 62, 65, 55, 60, 1, 29, 39, 50], 'cur_cost': 53027.0}, {'tour': array([ 4, 65, 18, 36, 38, 53, 14,  1, 39, 22, 40, 33, 12, 10, 63, 46, 43,
       56, 13,  6, 15, 60, 21, 26, 57, 20, 31, 58, 28,  9, 52, 47,  8,  0,
       35, 19, 51, 64, 30, 62, 16, 42, 48, 41,  3, 11, 54, 23, 34,  2, 37,
       44, 50, 25, 17, 45, 49,  7, 32, 55,  5, 61, 27, 29, 59, 24],
      dtype=int64), 'cur_cost': 113745.0}, {'tour': array([22, 12, 17, 15, 14, 23, 16, 18, 19, 13, 20, 21, 27, 37, 25, 26, 36,
       35, 28, 30, 34, 33, 31, 24, 29, 32,  3,  7,  1, 11,  9,  5,  4,  8,
        2,  6, 10,  0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52,
       63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42],
      dtype=int64), 'cur_cost': 10030.0}, {'tour': array([36,  4, 52, 41, 16,  8, 12, 10, 51, 57, 53, 48, 21, 37,  7, 61, 14,
       19, 34, 35, 20, 26,  3, 18, 65, 17, 46, 15, 58, 32, 54, 47, 56, 45,
        6, 49, 59, 63, 24,  0, 28, 43, 33, 50, 42, 23, 44, 62,  9, 39, 22,
       30, 27, 25, 64, 55, 38, 60,  5, 40, 29,  2, 11, 31, 13,  1],
      dtype=int64), 'cur_cost': 112495.0}, {'tour': array([47, 44, 51, 28, 53, 12, 18, 38, 42, 29,  1, 62, 45, 57,  8,  7,  5,
       16, 13, 48, 40, 34, 14, 43, 52, 35,  6, 61, 59, 17, 19, 39, 54, 37,
       26, 64, 63,  3, 50, 15, 46, 21, 25,  2, 22, 60, 10, 65, 31, 55, 24,
       20, 58, 56, 30,  4, 33, 11, 23,  9, 36, 49, 27,  0, 32, 41],
      dtype=int64), 'cur_cost': 100907.0}, {'tour': array([ 0, 61, 59, 41, 44, 23, 49, 55,  3, 53, 48, 24, 62, 39, 56, 17, 50,
       14, 46, 60, 65, 21, 20,  7, 19, 47, 51,  1,  9, 13, 22, 27, 42, 37,
       15, 28, 64, 36, 40, 38, 29,  6, 26,  8, 30,  5, 35, 10, 25, 34, 43,
       45, 31, 57,  4, 63, 54, 33, 12, 11, 58, 16,  2, 18, 32, 52],
      dtype=int64), 'cur_cost': 103789.0}, {'tour': array([39, 49, 36, 11, 20, 60,  5, 55, 29, 17,  9,  2, 65, 28, 59, 38, 23,
       21, 48, 61,  4, 45, 42, 12, 31, 14,  8, 13, 24, 27, 52, 26,  0, 18,
       56, 58, 50, 64, 34, 30, 16, 41, 44, 53,  1,  7, 54, 15, 35, 46, 10,
       19, 63,  3, 22, 62, 51, 47, 32, 33, 40, 57, 37, 25,  6, 43],
      dtype=int64), 'cur_cost': 101548.0}, {'tour': array([45,  0, 54, 30, 63, 57, 14, 26, 16, 13, 29, 52, 23,  5, 53, 44, 62,
        6, 27, 20, 31, 15, 64, 58, 49, 35, 46, 28, 25, 55, 65, 39, 59,  1,
       47, 38, 18, 24,  2, 33, 32, 22, 37,  3, 19, 40, 51, 12, 21, 42, 48,
       36, 10, 34, 11,  8, 17,  4,  9, 41, 56, 61, 50, 60, 43,  7],
      dtype=int64), 'cur_cost': 104179.0}, {'tour': array([43, 53, 49, 56, 15,  1,  6, 59, 33, 22, 55, 62, 13,  7, 19, 37, 61,
        5, 29, 10, 25, 41, 39, 48,  8, 42, 35, 12, 16, 44, 20, 60,  2,  0,
       45, 64, 58, 63, 28, 23, 38, 18, 40, 26, 32, 17, 27, 34,  3, 46, 36,
       50, 51, 57, 24, 14, 54, 52, 21, 65,  9, 11, 31, 47,  4, 30],
      dtype=int64), 'cur_cost': 102735.0}, {'tour': array([21, 22, 48, 29, 12, 34, 49, 44, 55, 35, 30, 37, 16, 53, 19, 10,  3,
        0, 18,  6, 33, 59, 50, 54, 31,  7, 61, 24, 41, 64, 13, 57, 15, 42,
        2, 51, 65,  1, 36,  4,  9, 47,  5, 38, 32, 28, 52, 25, 40, 39, 56,
       26, 45, 60, 11, 14, 58, 20, 62, 63,  8, 43, 17, 27, 23, 46],
      dtype=int64), 'cur_cost': 114265.0}]
2025-07-30 22:04:32,379 - ExploitationExpert - INFO - 局部搜索耗时: 1.30秒
2025-07-30 22:04:32,380 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-07-30 22:04:32,380 - collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([ 4, 65, 18, 36, 38, 53, 14,  1, 39, 22, 40, 33, 12, 10, 63, 46, 43,
       56, 13,  6, 15, 60, 21, 26, 57, 20, 31, 58, 28,  9, 52, 47,  8,  0,
       35, 19, 51, 64, 30, 62, 16, 42, 48, 41,  3, 11, 54, 23, 34,  2, 37,
       44, 50, 25, 17, 45, 49,  7, 32, 55,  5, 61, 27, 29, 59, 24],
      dtype=int64), 'cur_cost': 113745.0}
2025-07-30 22:04:32,380 - collaboration_manager - INFO - 为个体 2 生成探索路径
2025-07-30 22:04:32,380 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-07-30 22:04:32,380 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-07-30 22:04:32,384 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-07-30 22:04:32,384 - ExplorationExpert - INFO - 计算路径成本
2025-07-30 22:04:32,384 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12326.0, 路径长度: 66
2025-07-30 22:04:32,384 - collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 7, 14, 12, 22, 23, 16, 18, 17, 15, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12326.0}
2025-07-30 22:04:32,385 - collaboration_manager - INFO - 为个体 3 生成利用路径
2025-07-30 22:04:32,385 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-30 22:04:32,385 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-30 22:04:32,385 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 113275.0
2025-07-30 22:04:33,713 - ExploitationExpert - INFO - res_population_num: 1
2025-07-30 22:04:33,713 - ExploitationExpert - INFO - res_population_costs: [9546.0]
2025-07-30 22:04:33,713 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 43, 40,
       49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 54, 57, 64,
       53, 62, 59, 56, 55, 61, 65, 52, 63,  2,  6,  4,  5,  8, 10],
      dtype=int64)]
2025-07-30 22:04:33,714 - ExploitationExpert - INFO - populations_num: 10
2025-07-30 22:04:33,714 - ExploitationExpert - INFO - populations: [{'tour': [12, 28, 24, 0, 15, 22, 26, 37, 36, 27, 35, 21, 20, 33, 9, 17, 23, 7, 58, 8, 18, 25, 31, 3, 59, 54, 64, 2, 10, 16, 6, 5, 56, 11, 57, 53, 52, 40, 42, 51, 46, 13, 43, 19, 49, 48, 45, 30, 14, 44, 38, 47, 41, 32, 34, 4, 63, 61, 62, 65, 55, 60, 1, 29, 39, 50], 'cur_cost': 53027.0}, {'tour': array([ 4, 65, 18, 36, 38, 53, 14,  1, 39, 22, 40, 33, 12, 10, 63, 46, 43,
       56, 13,  6, 15, 60, 21, 26, 57, 20, 31, 58, 28,  9, 52, 47,  8,  0,
       35, 19, 51, 64, 30, 62, 16, 42, 48, 41,  3, 11, 54, 23, 34,  2, 37,
       44, 50, 25, 17, 45, 49,  7, 32, 55,  5, 61, 27, 29, 59, 24],
      dtype=int64), 'cur_cost': 113745.0}, {'tour': [0, 7, 14, 12, 22, 23, 16, 18, 17, 15, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12326.0}, {'tour': array([27, 54,  9, 33, 22, 40, 52, 55, 45, 65,  5,  6,  7, 48,  2, 47, 29,
       57, 16, 28, 63, 35, 39, 11,  4, 10, 32,  3, 23, 64, 43, 60, 56, 19,
       42, 62, 25, 14, 30,  0, 38,  8, 13, 18, 61, 21, 59, 44, 58,  1, 51,
       41, 12, 36, 26, 20, 46, 34, 49, 15, 24, 37, 31, 53, 50, 17],
      dtype=int64), 'cur_cost': 113275.0}, {'tour': array([47, 44, 51, 28, 53, 12, 18, 38, 42, 29,  1, 62, 45, 57,  8,  7,  5,
       16, 13, 48, 40, 34, 14, 43, 52, 35,  6, 61, 59, 17, 19, 39, 54, 37,
       26, 64, 63,  3, 50, 15, 46, 21, 25,  2, 22, 60, 10, 65, 31, 55, 24,
       20, 58, 56, 30,  4, 33, 11, 23,  9, 36, 49, 27,  0, 32, 41],
      dtype=int64), 'cur_cost': 100907.0}, {'tour': array([ 0, 61, 59, 41, 44, 23, 49, 55,  3, 53, 48, 24, 62, 39, 56, 17, 50,
       14, 46, 60, 65, 21, 20,  7, 19, 47, 51,  1,  9, 13, 22, 27, 42, 37,
       15, 28, 64, 36, 40, 38, 29,  6, 26,  8, 30,  5, 35, 10, 25, 34, 43,
       45, 31, 57,  4, 63, 54, 33, 12, 11, 58, 16,  2, 18, 32, 52],
      dtype=int64), 'cur_cost': 103789.0}, {'tour': array([39, 49, 36, 11, 20, 60,  5, 55, 29, 17,  9,  2, 65, 28, 59, 38, 23,
       21, 48, 61,  4, 45, 42, 12, 31, 14,  8, 13, 24, 27, 52, 26,  0, 18,
       56, 58, 50, 64, 34, 30, 16, 41, 44, 53,  1,  7, 54, 15, 35, 46, 10,
       19, 63,  3, 22, 62, 51, 47, 32, 33, 40, 57, 37, 25,  6, 43],
      dtype=int64), 'cur_cost': 101548.0}, {'tour': array([45,  0, 54, 30, 63, 57, 14, 26, 16, 13, 29, 52, 23,  5, 53, 44, 62,
        6, 27, 20, 31, 15, 64, 58, 49, 35, 46, 28, 25, 55, 65, 39, 59,  1,
       47, 38, 18, 24,  2, 33, 32, 22, 37,  3, 19, 40, 51, 12, 21, 42, 48,
       36, 10, 34, 11,  8, 17,  4,  9, 41, 56, 61, 50, 60, 43,  7],
      dtype=int64), 'cur_cost': 104179.0}, {'tour': array([43, 53, 49, 56, 15,  1,  6, 59, 33, 22, 55, 62, 13,  7, 19, 37, 61,
        5, 29, 10, 25, 41, 39, 48,  8, 42, 35, 12, 16, 44, 20, 60,  2,  0,
       45, 64, 58, 63, 28, 23, 38, 18, 40, 26, 32, 17, 27, 34,  3, 46, 36,
       50, 51, 57, 24, 14, 54, 52, 21, 65,  9, 11, 31, 47,  4, 30],
      dtype=int64), 'cur_cost': 102735.0}, {'tour': array([21, 22, 48, 29, 12, 34, 49, 44, 55, 35, 30, 37, 16, 53, 19, 10,  3,
        0, 18,  6, 33, 59, 50, 54, 31,  7, 61, 24, 41, 64, 13, 57, 15, 42,
        2, 51, 65,  1, 36,  4,  9, 47,  5, 38, 32, 28, 52, 25, 40, 39, 56,
       26, 45, 60, 11, 14, 58, 20, 62, 63,  8, 43, 17, 27, 23, 46],
      dtype=int64), 'cur_cost': 114265.0}]
2025-07-30 22:04:33,717 - ExploitationExpert - INFO - 局部搜索耗时: 1.33秒
2025-07-30 22:04:33,717 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-07-30 22:04:33,717 - collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([27, 54,  9, 33, 22, 40, 52, 55, 45, 65,  5,  6,  7, 48,  2, 47, 29,
       57, 16, 28, 63, 35, 39, 11,  4, 10, 32,  3, 23, 64, 43, 60, 56, 19,
       42, 62, 25, 14, 30,  0, 38,  8, 13, 18, 61, 21, 59, 44, 58,  1, 51,
       41, 12, 36, 26, 20, 46, 34, 49, 15, 24, 37, 31, 53, 50, 17],
      dtype=int64), 'cur_cost': 113275.0}
2025-07-30 22:04:33,717 - collaboration_manager - INFO - 为个体 4 生成探索路径
2025-07-30 22:04:33,717 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-07-30 22:04:33,717 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-07-30 22:04:33,720 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-07-30 22:04:33,720 - ExplorationExpert - INFO - 计算路径成本
2025-07-30 22:04:33,721 - ExplorationExpert - INFO - 探索路径生成完成，成本: 97509.0, 路径长度: 66
2025-07-30 22:04:33,721 - collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [12, 14, 0, 22, 16, 37, 36, 15, 13, 21, 20, 9, 7, 58, 35, 28, 31, 3, 54, 64, 2, 6, 4, 53, 52, 40, 55, 51, 62, 43, 19, 49, 48, 45, 30, 44, 63, 41, 33, 18, 61, 27, 50, 1, 56, 39, 59, 17, 29, 42, 57, 65, 38, 23, 47, 60, 8, 24, 26, 46, 10, 32, 11, 25, 5, 34], 'cur_cost': 97509.0}
2025-07-30 22:04:33,722 - collaboration_manager - INFO - 为个体 5 生成利用路径
2025-07-30 22:04:33,722 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-30 22:04:33,722 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-30 22:04:33,723 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 106266.0
2025-07-30 22:04:34,214 - ExploitationExpert - INFO - res_population_num: 2
2025-07-30 22:04:34,214 - ExploitationExpert - INFO - res_population_costs: [9546.0, 9542]
2025-07-30 22:04:34,214 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 43, 40,
       49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 54, 57, 64,
       53, 62, 59, 56, 55, 61, 65, 52, 63,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 14, 15, 17, 12, 22, 23, 16, 18, 19, 27, 37,
       36, 26, 25, 31, 33, 35, 34, 30, 28, 32, 29, 24,  3,  7,  1],
      dtype=int64)]
2025-07-30 22:04:34,214 - ExploitationExpert - INFO - populations_num: 10
2025-07-30 22:04:34,214 - ExploitationExpert - INFO - populations: [{'tour': [12, 28, 24, 0, 15, 22, 26, 37, 36, 27, 35, 21, 20, 33, 9, 17, 23, 7, 58, 8, 18, 25, 31, 3, 59, 54, 64, 2, 10, 16, 6, 5, 56, 11, 57, 53, 52, 40, 42, 51, 46, 13, 43, 19, 49, 48, 45, 30, 14, 44, 38, 47, 41, 32, 34, 4, 63, 61, 62, 65, 55, 60, 1, 29, 39, 50], 'cur_cost': 53027.0}, {'tour': array([ 4, 65, 18, 36, 38, 53, 14,  1, 39, 22, 40, 33, 12, 10, 63, 46, 43,
       56, 13,  6, 15, 60, 21, 26, 57, 20, 31, 58, 28,  9, 52, 47,  8,  0,
       35, 19, 51, 64, 30, 62, 16, 42, 48, 41,  3, 11, 54, 23, 34,  2, 37,
       44, 50, 25, 17, 45, 49,  7, 32, 55,  5, 61, 27, 29, 59, 24],
      dtype=int64), 'cur_cost': 113745.0}, {'tour': [0, 7, 14, 12, 22, 23, 16, 18, 17, 15, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12326.0}, {'tour': array([27, 54,  9, 33, 22, 40, 52, 55, 45, 65,  5,  6,  7, 48,  2, 47, 29,
       57, 16, 28, 63, 35, 39, 11,  4, 10, 32,  3, 23, 64, 43, 60, 56, 19,
       42, 62, 25, 14, 30,  0, 38,  8, 13, 18, 61, 21, 59, 44, 58,  1, 51,
       41, 12, 36, 26, 20, 46, 34, 49, 15, 24, 37, 31, 53, 50, 17],
      dtype=int64), 'cur_cost': 113275.0}, {'tour': [12, 14, 0, 22, 16, 37, 36, 15, 13, 21, 20, 9, 7, 58, 35, 28, 31, 3, 54, 64, 2, 6, 4, 53, 52, 40, 55, 51, 62, 43, 19, 49, 48, 45, 30, 44, 63, 41, 33, 18, 61, 27, 50, 1, 56, 39, 59, 17, 29, 42, 57, 65, 38, 23, 47, 60, 8, 24, 26, 46, 10, 32, 11, 25, 5, 34], 'cur_cost': 97509.0}, {'tour': array([48, 22, 38, 58, 63, 14, 17, 52,  4, 37, 30, 65, 35, 56, 20, 60, 16,
       26, 42, 24, 33,  9, 34, 40, 47, 15,  8,  5, 31, 59, 28, 41, 55, 29,
       23, 49, 19,  2, 51, 46,  3, 62,  0, 53, 54, 12, 45,  6, 11, 10, 43,
       13,  7, 27, 39, 36, 64,  1, 32, 57, 25, 18, 21, 61, 44, 50],
      dtype=int64), 'cur_cost': 106266.0}, {'tour': array([39, 49, 36, 11, 20, 60,  5, 55, 29, 17,  9,  2, 65, 28, 59, 38, 23,
       21, 48, 61,  4, 45, 42, 12, 31, 14,  8, 13, 24, 27, 52, 26,  0, 18,
       56, 58, 50, 64, 34, 30, 16, 41, 44, 53,  1,  7, 54, 15, 35, 46, 10,
       19, 63,  3, 22, 62, 51, 47, 32, 33, 40, 57, 37, 25,  6, 43],
      dtype=int64), 'cur_cost': 101548.0}, {'tour': array([45,  0, 54, 30, 63, 57, 14, 26, 16, 13, 29, 52, 23,  5, 53, 44, 62,
        6, 27, 20, 31, 15, 64, 58, 49, 35, 46, 28, 25, 55, 65, 39, 59,  1,
       47, 38, 18, 24,  2, 33, 32, 22, 37,  3, 19, 40, 51, 12, 21, 42, 48,
       36, 10, 34, 11,  8, 17,  4,  9, 41, 56, 61, 50, 60, 43,  7],
      dtype=int64), 'cur_cost': 104179.0}, {'tour': array([43, 53, 49, 56, 15,  1,  6, 59, 33, 22, 55, 62, 13,  7, 19, 37, 61,
        5, 29, 10, 25, 41, 39, 48,  8, 42, 35, 12, 16, 44, 20, 60,  2,  0,
       45, 64, 58, 63, 28, 23, 38, 18, 40, 26, 32, 17, 27, 34,  3, 46, 36,
       50, 51, 57, 24, 14, 54, 52, 21, 65,  9, 11, 31, 47,  4, 30],
      dtype=int64), 'cur_cost': 102735.0}, {'tour': array([21, 22, 48, 29, 12, 34, 49, 44, 55, 35, 30, 37, 16, 53, 19, 10,  3,
        0, 18,  6, 33, 59, 50, 54, 31,  7, 61, 24, 41, 64, 13, 57, 15, 42,
        2, 51, 65,  1, 36,  4,  9, 47,  5, 38, 32, 28, 52, 25, 40, 39, 56,
       26, 45, 60, 11, 14, 58, 20, 62, 63,  8, 43, 17, 27, 23, 46],
      dtype=int64), 'cur_cost': 114265.0}]
2025-07-30 22:04:34,219 - ExploitationExpert - INFO - 局部搜索耗时: 0.50秒
2025-07-30 22:04:34,219 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-07-30 22:04:34,220 - collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([48, 22, 38, 58, 63, 14, 17, 52,  4, 37, 30, 65, 35, 56, 20, 60, 16,
       26, 42, 24, 33,  9, 34, 40, 47, 15,  8,  5, 31, 59, 28, 41, 55, 29,
       23, 49, 19,  2, 51, 46,  3, 62,  0, 53, 54, 12, 45,  6, 11, 10, 43,
       13,  7, 27, 39, 36, 64,  1, 32, 57, 25, 18, 21, 61, 44, 50],
      dtype=int64), 'cur_cost': 106266.0}
2025-07-30 22:04:34,220 - collaboration_manager - INFO - 为个体 6 生成探索路径
2025-07-30 22:04:34,220 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-07-30 22:04:34,220 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-07-30 22:04:34,227 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-07-30 22:04:34,227 - ExplorationExpert - INFO - 计算路径成本
2025-07-30 22:04:34,227 - ExplorationExpert - INFO - 探索路径生成完成，成本: 68436.0, 路径长度: 66
2025-07-30 22:04:34,227 - collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [42, 47, 22, 37, 27, 14, 12, 9, 53, 10, 63, 64, 13, 19, 31, 21, 29, 1, 62, 55, 11, 59, 18, 28, 30, 23, 36, 15, 8, 0, 52, 65, 3, 61, 4, 54, 39, 17, 2, 20, 26, 24, 7, 58, 49, 40, 38, 50, 48, 44, 16, 5, 35, 25, 34, 46, 41, 43, 32, 45, 6, 60, 56, 57, 51, 33], 'cur_cost': 68436.0}
2025-07-30 22:04:34,227 - collaboration_manager - INFO - 为个体 7 生成利用路径
2025-07-30 22:04:34,227 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-30 22:04:34,227 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-30 22:04:34,227 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 108741.0
2025-07-30 22:04:34,300 - ExploitationExpert - INFO - res_population_num: 6
2025-07-30 22:04:34,300 - ExploitationExpert - INFO - res_population_costs: [9546.0, 9542, 9540, 9529, 9526, 9521]
2025-07-30 22:04:34,300 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 43, 40,
       49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 54, 57, 64,
       53, 62, 59, 56, 55, 61, 65, 52, 63,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 14, 15, 17, 12, 22, 23, 16, 18, 19, 27, 37,
       36, 26, 25, 31, 33, 35, 34, 30, 28, 32, 29, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 14, 15, 17, 12, 22, 23, 16, 18, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 43, 40,
       49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7,  3, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27,
       37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-07-30 22:04:34,300 - ExploitationExpert - INFO - populations_num: 10
2025-07-30 22:04:34,300 - ExploitationExpert - INFO - populations: [{'tour': [12, 28, 24, 0, 15, 22, 26, 37, 36, 27, 35, 21, 20, 33, 9, 17, 23, 7, 58, 8, 18, 25, 31, 3, 59, 54, 64, 2, 10, 16, 6, 5, 56, 11, 57, 53, 52, 40, 42, 51, 46, 13, 43, 19, 49, 48, 45, 30, 14, 44, 38, 47, 41, 32, 34, 4, 63, 61, 62, 65, 55, 60, 1, 29, 39, 50], 'cur_cost': 53027.0}, {'tour': array([ 4, 65, 18, 36, 38, 53, 14,  1, 39, 22, 40, 33, 12, 10, 63, 46, 43,
       56, 13,  6, 15, 60, 21, 26, 57, 20, 31, 58, 28,  9, 52, 47,  8,  0,
       35, 19, 51, 64, 30, 62, 16, 42, 48, 41,  3, 11, 54, 23, 34,  2, 37,
       44, 50, 25, 17, 45, 49,  7, 32, 55,  5, 61, 27, 29, 59, 24],
      dtype=int64), 'cur_cost': 113745.0}, {'tour': [0, 7, 14, 12, 22, 23, 16, 18, 17, 15, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12326.0}, {'tour': array([27, 54,  9, 33, 22, 40, 52, 55, 45, 65,  5,  6,  7, 48,  2, 47, 29,
       57, 16, 28, 63, 35, 39, 11,  4, 10, 32,  3, 23, 64, 43, 60, 56, 19,
       42, 62, 25, 14, 30,  0, 38,  8, 13, 18, 61, 21, 59, 44, 58,  1, 51,
       41, 12, 36, 26, 20, 46, 34, 49, 15, 24, 37, 31, 53, 50, 17],
      dtype=int64), 'cur_cost': 113275.0}, {'tour': [12, 14, 0, 22, 16, 37, 36, 15, 13, 21, 20, 9, 7, 58, 35, 28, 31, 3, 54, 64, 2, 6, 4, 53, 52, 40, 55, 51, 62, 43, 19, 49, 48, 45, 30, 44, 63, 41, 33, 18, 61, 27, 50, 1, 56, 39, 59, 17, 29, 42, 57, 65, 38, 23, 47, 60, 8, 24, 26, 46, 10, 32, 11, 25, 5, 34], 'cur_cost': 97509.0}, {'tour': array([48, 22, 38, 58, 63, 14, 17, 52,  4, 37, 30, 65, 35, 56, 20, 60, 16,
       26, 42, 24, 33,  9, 34, 40, 47, 15,  8,  5, 31, 59, 28, 41, 55, 29,
       23, 49, 19,  2, 51, 46,  3, 62,  0, 53, 54, 12, 45,  6, 11, 10, 43,
       13,  7, 27, 39, 36, 64,  1, 32, 57, 25, 18, 21, 61, 44, 50],
      dtype=int64), 'cur_cost': 106266.0}, {'tour': [42, 47, 22, 37, 27, 14, 12, 9, 53, 10, 63, 64, 13, 19, 31, 21, 29, 1, 62, 55, 11, 59, 18, 28, 30, 23, 36, 15, 8, 0, 52, 65, 3, 61, 4, 54, 39, 17, 2, 20, 26, 24, 7, 58, 49, 40, 38, 50, 48, 44, 16, 5, 35, 25, 34, 46, 41, 43, 32, 45, 6, 60, 56, 57, 51, 33], 'cur_cost': 68436.0}, {'tour': array([40, 65, 53, 28, 47, 31, 61, 26, 50, 15, 32, 54, 20, 24, 64, 60,  8,
        9, 37, 63, 58, 51, 49, 41, 16, 21, 10, 11, 22, 45, 12, 55, 25,  1,
        3, 57, 18,  5, 14, 36, 43, 62, 30, 19,  2,  0, 29, 44, 23, 39,  6,
       56, 17, 46, 42, 34, 48,  4, 13, 27, 38, 59,  7, 33, 52, 35],
      dtype=int64), 'cur_cost': 108741.0}, {'tour': array([43, 53, 49, 56, 15,  1,  6, 59, 33, 22, 55, 62, 13,  7, 19, 37, 61,
        5, 29, 10, 25, 41, 39, 48,  8, 42, 35, 12, 16, 44, 20, 60,  2,  0,
       45, 64, 58, 63, 28, 23, 38, 18, 40, 26, 32, 17, 27, 34,  3, 46, 36,
       50, 51, 57, 24, 14, 54, 52, 21, 65,  9, 11, 31, 47,  4, 30],
      dtype=int64), 'cur_cost': 102735.0}, {'tour': array([21, 22, 48, 29, 12, 34, 49, 44, 55, 35, 30, 37, 16, 53, 19, 10,  3,
        0, 18,  6, 33, 59, 50, 54, 31,  7, 61, 24, 41, 64, 13, 57, 15, 42,
        2, 51, 65,  1, 36,  4,  9, 47,  5, 38, 32, 28, 52, 25, 40, 39, 56,
       26, 45, 60, 11, 14, 58, 20, 62, 63,  8, 43, 17, 27, 23, 46],
      dtype=int64), 'cur_cost': 114265.0}]
2025-07-30 22:04:34,300 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒
2025-07-30 22:04:34,300 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-07-30 22:04:34,300 - collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([40, 65, 53, 28, 47, 31, 61, 26, 50, 15, 32, 54, 20, 24, 64, 60,  8,
        9, 37, 63, 58, 51, 49, 41, 16, 21, 10, 11, 22, 45, 12, 55, 25,  1,
        3, 57, 18,  5, 14, 36, 43, 62, 30, 19,  2,  0, 29, 44, 23, 39,  6,
       56, 17, 46, 42, 34, 48,  4, 13, 27, 38, 59,  7, 33, 52, 35],
      dtype=int64), 'cur_cost': 108741.0}
2025-07-30 22:04:34,300 - collaboration_manager - INFO - 为个体 8 生成探索路径
2025-07-30 22:04:34,300 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-07-30 22:04:34,300 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-07-30 22:04:34,300 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-07-30 22:04:34,300 - ExplorationExpert - INFO - 计算路径成本
2025-07-30 22:04:34,300 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12770.0, 路径长度: 66
2025-07-30 22:04:34,300 - collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 22, 10, 1, 7, 3, 9, 11, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12770.0}
2025-07-30 22:04:34,300 - collaboration_manager - INFO - 为个体 9 生成利用路径
2025-07-30 22:04:34,300 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-30 22:04:34,300 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-30 22:04:34,300 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 101808.0
2025-07-30 22:04:34,368 - ExploitationExpert - INFO - res_population_num: 6
2025-07-30 22:04:34,368 - ExploitationExpert - INFO - res_population_costs: [9546.0, 9542, 9540, 9529, 9526, 9521]
2025-07-30 22:04:34,368 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 43, 40,
       49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 54, 57, 64,
       53, 62, 59, 56, 55, 61, 65, 52, 63,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 14, 15, 17, 12, 22, 23, 16, 18, 19, 27, 37,
       36, 26, 25, 31, 33, 35, 34, 30, 28, 32, 29, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 14, 15, 17, 12, 22, 23, 16, 18, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 43, 40,
       49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7,  3, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27,
       37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-07-30 22:04:34,368 - ExploitationExpert - INFO - populations_num: 10
2025-07-30 22:04:34,368 - ExploitationExpert - INFO - populations: [{'tour': [12, 28, 24, 0, 15, 22, 26, 37, 36, 27, 35, 21, 20, 33, 9, 17, 23, 7, 58, 8, 18, 25, 31, 3, 59, 54, 64, 2, 10, 16, 6, 5, 56, 11, 57, 53, 52, 40, 42, 51, 46, 13, 43, 19, 49, 48, 45, 30, 14, 44, 38, 47, 41, 32, 34, 4, 63, 61, 62, 65, 55, 60, 1, 29, 39, 50], 'cur_cost': 53027.0}, {'tour': array([ 4, 65, 18, 36, 38, 53, 14,  1, 39, 22, 40, 33, 12, 10, 63, 46, 43,
       56, 13,  6, 15, 60, 21, 26, 57, 20, 31, 58, 28,  9, 52, 47,  8,  0,
       35, 19, 51, 64, 30, 62, 16, 42, 48, 41,  3, 11, 54, 23, 34,  2, 37,
       44, 50, 25, 17, 45, 49,  7, 32, 55,  5, 61, 27, 29, 59, 24],
      dtype=int64), 'cur_cost': 113745.0}, {'tour': [0, 7, 14, 12, 22, 23, 16, 18, 17, 15, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12326.0}, {'tour': array([27, 54,  9, 33, 22, 40, 52, 55, 45, 65,  5,  6,  7, 48,  2, 47, 29,
       57, 16, 28, 63, 35, 39, 11,  4, 10, 32,  3, 23, 64, 43, 60, 56, 19,
       42, 62, 25, 14, 30,  0, 38,  8, 13, 18, 61, 21, 59, 44, 58,  1, 51,
       41, 12, 36, 26, 20, 46, 34, 49, 15, 24, 37, 31, 53, 50, 17],
      dtype=int64), 'cur_cost': 113275.0}, {'tour': [12, 14, 0, 22, 16, 37, 36, 15, 13, 21, 20, 9, 7, 58, 35, 28, 31, 3, 54, 64, 2, 6, 4, 53, 52, 40, 55, 51, 62, 43, 19, 49, 48, 45, 30, 44, 63, 41, 33, 18, 61, 27, 50, 1, 56, 39, 59, 17, 29, 42, 57, 65, 38, 23, 47, 60, 8, 24, 26, 46, 10, 32, 11, 25, 5, 34], 'cur_cost': 97509.0}, {'tour': array([48, 22, 38, 58, 63, 14, 17, 52,  4, 37, 30, 65, 35, 56, 20, 60, 16,
       26, 42, 24, 33,  9, 34, 40, 47, 15,  8,  5, 31, 59, 28, 41, 55, 29,
       23, 49, 19,  2, 51, 46,  3, 62,  0, 53, 54, 12, 45,  6, 11, 10, 43,
       13,  7, 27, 39, 36, 64,  1, 32, 57, 25, 18, 21, 61, 44, 50],
      dtype=int64), 'cur_cost': 106266.0}, {'tour': [42, 47, 22, 37, 27, 14, 12, 9, 53, 10, 63, 64, 13, 19, 31, 21, 29, 1, 62, 55, 11, 59, 18, 28, 30, 23, 36, 15, 8, 0, 52, 65, 3, 61, 4, 54, 39, 17, 2, 20, 26, 24, 7, 58, 49, 40, 38, 50, 48, 44, 16, 5, 35, 25, 34, 46, 41, 43, 32, 45, 6, 60, 56, 57, 51, 33], 'cur_cost': 68436.0}, {'tour': array([40, 65, 53, 28, 47, 31, 61, 26, 50, 15, 32, 54, 20, 24, 64, 60,  8,
        9, 37, 63, 58, 51, 49, 41, 16, 21, 10, 11, 22, 45, 12, 55, 25,  1,
        3, 57, 18,  5, 14, 36, 43, 62, 30, 19,  2,  0, 29, 44, 23, 39,  6,
       56, 17, 46, 42, 34, 48,  4, 13, 27, 38, 59,  7, 33, 52, 35],
      dtype=int64), 'cur_cost': 108741.0}, {'tour': [0, 22, 10, 1, 7, 3, 9, 11, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12770.0}, {'tour': array([55, 13, 33, 38, 63, 51, 29, 36, 48, 22, 18, 37, 39, 24, 19,  6,  7,
       34, 25, 11, 21, 64, 45, 47, 44,  4, 31, 20, 59, 60,  9, 56, 26, 15,
        8,  2, 65, 40, 14, 46, 54, 41, 32, 58, 16, 28, 23, 57, 53, 10, 50,
       35, 42, 27,  0, 43, 49, 61, 62,  1, 17, 30,  3, 12, 52,  5],
      dtype=int64), 'cur_cost': 101808.0}]
2025-07-30 22:04:34,368 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒
2025-07-30 22:04:34,368 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-07-30 22:04:34,368 - collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([55, 13, 33, 38, 63, 51, 29, 36, 48, 22, 18, 37, 39, 24, 19,  6,  7,
       34, 25, 11, 21, 64, 45, 47, 44,  4, 31, 20, 59, 60,  9, 56, 26, 15,
        8,  2, 65, 40, 14, 46, 54, 41, 32, 58, 16, 28, 23, 57, 53, 10, 50,
       35, 42, 27,  0, 43, 49, 61, 62,  1, 17, 30,  3, 12, 52,  5],
      dtype=int64), 'cur_cost': 101808.0}
2025-07-30 22:04:34,368 - collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [12, 28, 24, 0, 15, 22, 26, 37, 36, 27, 35, 21, 20, 33, 9, 17, 23, 7, 58, 8, 18, 25, 31, 3, 59, 54, 64, 2, 10, 16, 6, 5, 56, 11, 57, 53, 52, 40, 42, 51, 46, 13, 43, 19, 49, 48, 45, 30, 14, 44, 38, 47, 41, 32, 34, 4, 63, 61, 62, 65, 55, 60, 1, 29, 39, 50], 'cur_cost': 53027.0}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 4, 65, 18, 36, 38, 53, 14,  1, 39, 22, 40, 33, 12, 10, 63, 46, 43,
       56, 13,  6, 15, 60, 21, 26, 57, 20, 31, 58, 28,  9, 52, 47,  8,  0,
       35, 19, 51, 64, 30, 62, 16, 42, 48, 41,  3, 11, 54, 23, 34,  2, 37,
       44, 50, 25, 17, 45, 49,  7, 32, 55,  5, 61, 27, 29, 59, 24],
      dtype=int64), 'cur_cost': 113745.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 7, 14, 12, 22, 23, 16, 18, 17, 15, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12326.0}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([27, 54,  9, 33, 22, 40, 52, 55, 45, 65,  5,  6,  7, 48,  2, 47, 29,
       57, 16, 28, 63, 35, 39, 11,  4, 10, 32,  3, 23, 64, 43, 60, 56, 19,
       42, 62, 25, 14, 30,  0, 38,  8, 13, 18, 61, 21, 59, 44, 58,  1, 51,
       41, 12, 36, 26, 20, 46, 34, 49, 15, 24, 37, 31, 53, 50, 17],
      dtype=int64), 'cur_cost': 113275.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [12, 14, 0, 22, 16, 37, 36, 15, 13, 21, 20, 9, 7, 58, 35, 28, 31, 3, 54, 64, 2, 6, 4, 53, 52, 40, 55, 51, 62, 43, 19, 49, 48, 45, 30, 44, 63, 41, 33, 18, 61, 27, 50, 1, 56, 39, 59, 17, 29, 42, 57, 65, 38, 23, 47, 60, 8, 24, 26, 46, 10, 32, 11, 25, 5, 34], 'cur_cost': 97509.0}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([48, 22, 38, 58, 63, 14, 17, 52,  4, 37, 30, 65, 35, 56, 20, 60, 16,
       26, 42, 24, 33,  9, 34, 40, 47, 15,  8,  5, 31, 59, 28, 41, 55, 29,
       23, 49, 19,  2, 51, 46,  3, 62,  0, 53, 54, 12, 45,  6, 11, 10, 43,
       13,  7, 27, 39, 36, 64,  1, 32, 57, 25, 18, 21, 61, 44, 50],
      dtype=int64), 'cur_cost': 106266.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [42, 47, 22, 37, 27, 14, 12, 9, 53, 10, 63, 64, 13, 19, 31, 21, 29, 1, 62, 55, 11, 59, 18, 28, 30, 23, 36, 15, 8, 0, 52, 65, 3, 61, 4, 54, 39, 17, 2, 20, 26, 24, 7, 58, 49, 40, 38, 50, 48, 44, 16, 5, 35, 25, 34, 46, 41, 43, 32, 45, 6, 60, 56, 57, 51, 33], 'cur_cost': 68436.0}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([40, 65, 53, 28, 47, 31, 61, 26, 50, 15, 32, 54, 20, 24, 64, 60,  8,
        9, 37, 63, 58, 51, 49, 41, 16, 21, 10, 11, 22, 45, 12, 55, 25,  1,
        3, 57, 18,  5, 14, 36, 43, 62, 30, 19,  2,  0, 29, 44, 23, 39,  6,
       56, 17, 46, 42, 34, 48,  4, 13, 27, 38, 59,  7, 33, 52, 35],
      dtype=int64), 'cur_cost': 108741.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 22, 10, 1, 7, 3, 9, 11, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12770.0}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([55, 13, 33, 38, 63, 51, 29, 36, 48, 22, 18, 37, 39, 24, 19,  6,  7,
       34, 25, 11, 21, 64, 45, 47, 44,  4, 31, 20, 59, 60,  9, 56, 26, 15,
        8,  2, 65, 40, 14, 46, 54, 41, 32, 58, 16, 28, 23, 57, 53, 10, 50,
       35, 42, 27,  0, 43, 49, 61, 62,  1, 17, 30,  3, 12, 52,  5],
      dtype=int64), 'cur_cost': 101808.0}}]
2025-07-30 22:04:34,368 - collaboration_manager - INFO - 进化阶段完成
2025-07-30 22:04:34,368 - StatsExpert - INFO - 开始统计分析
2025-07-30 22:04:34,385 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=12326.0, 多样性=0.951
2025-07-30 22:04:34,385 - collaboration_manager - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-07-30 22:04:34,385 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment (Algorithm-based) ---
2025-07-30 22:04:34,385 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-07-30 22:04:34,385 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 40, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvement_rate': -0.0849424147979664, 'best_improvement': -0.23606097071801044}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': 0.01729729729729729}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 6, 'new_count': 6, 'count_change': 0, 'old_best_cost': 9521, 'new_best_cost': 9521, 'quality_improvement': 0.0, 'old_diversity': 0.8282828282828283, 'new_diversity': 0.8282828282828283, 'diversity_change': 0.0}, 'suggestions': ['成本出现恶化，建议增加探索策略比例以跳出局部最优', '种群多样性较高，可适当增加利用策略进行局部优化']}
2025-07-30 22:04:34,385 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-07-30 22:04:34,385 - __main__ - INFO - composite13_66 开始进化第 2 代
2025-07-30 22:04:34,385 - collaboration_manager - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-07-30 22:04:34,385 - StatsExpert - INFO - 开始统计分析
2025-07-30 22:04:34,400 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=12326.0, 多样性=0.951
2025-07-30 22:04:34,400 - PathExpert - INFO - 开始路径结构分析
2025-07-30 22:04:34,400 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.020
2025-07-30 22:04:34,400 - EliteExpert - INFO - 开始精英解分析
2025-07-30 22:04:34,400 - EliteExpert - INFO - 精英解分析完成: 精英解数量=6, 多样性=0.828
2025-07-30 22:04:34,416 - LandscapeExpert - INFO - 开始景观分析
2025-07-30 22:04:34,416 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-30 22:04:34,416 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 1/5)
- population_size: 10
- cost_stats: min 12326.0, mean 78790.3, max 113745.0, std 40125.26315884628
- diversity: 0.9505050505050504
- convergence: 0
- clustering: No clustering information
- recent_best_costs: Not available (Δ Not available, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: []
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}
- elite_diversity: 4 key-value pairs, sample: diversity_score: 0.8282828282828283, pairwise_distances: [0.9696969696969697, 0.9848484848484849, 0.7575757575757576, 0.7575757575757576, 0.9848484848484849, 0.8787878787878788, 0.9848484848484849, 0.9848484848484849, 0.8636363636363636, 0.9848484848484849, 0.9848484848484849, 0.19696969696969696, 0.12121212121212122, 0.9848484848484849, 0.9848484848484849], min_distance: 0.12121212121212122

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

### Output Example
```json
{
  "search_space_features": {"ruggedness": 0.5, "modality": "multi", "deceptiveness": "unknown"},
  "population_state": {"diversity": 0.5, "convergence": 0.4, "clustering": "weak"},
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "unknown",
  "evolution_direction": {"recommended_focus": "explore", "operators": []}
}
```

### Formatting Guidelines (read carefully, DO NOT output this section)
1. Do NOT copy or repeat any *_sample values above; at least 50% of elements in difficult_regions and opportunity_regions must differ from those samples.
2. Use [] (empty array) when a list has no content; never output the string "None" or a null value.
3. For search_space_features use enumerated strings only:
    • ruggedness: "low" | "medium" | "high"
    • modality: "mono" | "multi" | "unknown"
    • deceptiveness: "low" | "moderate" | "high" | "unknown"
4. diversity and convergence must be floats with exactly 3 decimal places (e.g. 0.742).
5. Follow the JSON schema strictly; no additional keys or commentary.

2025-07-30 22:04:34,416 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-30 22:04:36,608 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 22:04:38,611 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 2/3)
2025-07-30 22:04:40,495 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 22:04:42,496 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 3/3)
2025-07-30 22:04:44,308 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 22:04:44,309 - LandscapeExpert - INFO - LLM返回的分析结果: API请求失败: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 22:04:44,309 - LandscapeExpert - WARNING - 解析景观分析结果失败: Unable to extract valid JSON data from the response
2025-07-30 22:04:44,309 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-30 22:04:44,309 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.5, 'modality': 'unknown', 'deceptiveness': 'unknown'}, 'population_state': {'diversity': 0.5, 'convergence': 0.5, 'clustering': 'unknown'}, 'difficult_regions': [], 'opportunity_regions': [], 'evolution_phase': 'unknown', 'evolution_direction': {'recommended_focus': 'balance', 'operators': []}, 'raw_text': 'API请求失败: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ'}
2025-07-30 22:04:44,309 - collaboration_manager - INFO - 开始策略分配阶段
2025-07-30 22:04:44,309 - StrategyExpert - INFO - 开始策略分配分析
2025-07-30 22:04:44,309 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 1
- Population: 10 individuals
  • diversity: 0.5
  • best_cost: 12326.0
  • mean_cost: 78790.3
- Search-space: ruggedness 0.5, modality unknown, deceptiveness unknown
- Evolution phase: unknown
- Landscape focus suggestion: balance
- Spatial density summary: None
- Elite diversity: N/A
- Difficult regions (# 0): None
- Opportunity regions (# 0): None
- Last-iteration feedback: {'overall_score': 40, 'iteration': 0, 'total_iterations': 5, 'cost_improvement': {'status': 'deterioration', 'improvemen...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Formatting Guidelines (DO NOT output this section)
1. global_explore_ratio must be rounded to exactly 2 decimal places (e.g., 0.80).
2. The individual_assignments mapping MUST include every index from 0 to (population_size - 1) without omission.
3. Valid values are "explore" or "exploit" (lowercase).
4. rationale ≤ 40 words.

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-30 22:04:44,309 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-30 22:04:44,309 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-30 22:04:46,683 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 22:04:48,690 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 2/3)
2025-07-30 22:04:50,327 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 22:04:52,328 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 3/3)
2025-07-30 22:04:54,180 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 22:04:54,180 - StrategyExpert - INFO - LLM返回的策略分配结果: API请求失败: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 22:04:54,180 - StrategyExpert - WARNING - 解析策略分配结果时出错: Unable to extract valid JSON data from the response
2025-07-30 22:04:54,180 - StrategyExpert - INFO - 策略分配完成: ['explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-07-30 22:04:54,180 - collaboration_manager - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-07-30 22:04:54,180 - collaboration_manager - INFO - 策略分配完整报告: API请求失败: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 22:04:54,180 - collaboration_manager - INFO - 策略分配阶段完成
2025-07-30 22:04:54,180 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-07-30 22:04:54,180 - __main__ - INFO - 策略分配完整报告: API请求失败: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 22:04:54,180 - collaboration_manager - INFO - 开始进化阶段
2025-07-30 22:04:54,180 - collaboration_manager - INFO - 为个体 0 生成探索路径
2025-07-30 22:04:54,180 - ExplorationExpert - INFO - 开始为个体 0 生成探索路径（算法实现）
2025-07-30 22:04:54,180 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-07-30 22:04:54,180 - ExplorationExpert - INFO - 使用策略 diversification 生成探索路径，长度: 66
2025-07-30 22:04:54,180 - ExplorationExpert - INFO - 计算路径成本
2025-07-30 22:04:54,180 - ExplorationExpert - INFO - 探索路径生成完成，成本: 98398.0, 路径长度: 66
2025-07-30 22:04:54,180 - collaboration_manager - INFO - 个体 0 探索路径生成报告: {'new_tour': [4, 7, 24, 0, 15, 23, 26, 37, 17, 27, 40, 20, 12, 33, 63, 46, 43, 57, 36, 8, 18, 30, 21, 11, 31, 54, 64, 58, 10, 16, 6, 5, 48, 19, 35, 53, 52, 14, 42, 51, 38, 13, 59, 41, 61, 60, 29, 50, 34, 2, 1, 47, 39, 32, 45, 65, 49, 25, 62, 55, 9, 56, 3, 44, 22, 28], 'cur_cost': 98398.0}
2025-07-30 22:04:54,180 - collaboration_manager - INFO - 为个体 1 生成利用路径
2025-07-30 22:04:54,180 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-30 22:04:54,180 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-30 22:04:54,180 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 111027.0
2025-07-30 22:04:54,238 - ExploitationExpert - INFO - res_population_num: 6
2025-07-30 22:04:54,238 - ExploitationExpert - INFO - res_population_costs: [9521, 9526, 9529, 9540, 9542, 9546.0]
2025-07-30 22:04:54,238 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27,
       37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 43, 40,
       49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 14, 15, 17, 12, 22, 23, 16, 18, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 14, 15, 17, 12, 22, 23, 16, 18, 19, 27, 37,
       36, 26, 25, 31, 33, 35, 34, 30, 28, 32, 29, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 43, 40,
       49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 54, 57, 64,
       53, 62, 59, 56, 55, 61, 65, 52, 63,  2,  6,  4,  5,  8, 10],
      dtype=int64)]
2025-07-30 22:04:54,238 - ExploitationExpert - INFO - populations_num: 10
2025-07-30 22:04:54,238 - ExploitationExpert - INFO - populations: [{'tour': [4, 7, 24, 0, 15, 23, 26, 37, 17, 27, 40, 20, 12, 33, 63, 46, 43, 57, 36, 8, 18, 30, 21, 11, 31, 54, 64, 58, 10, 16, 6, 5, 48, 19, 35, 53, 52, 14, 42, 51, 38, 13, 59, 41, 61, 60, 29, 50, 34, 2, 1, 47, 39, 32, 45, 65, 49, 25, 62, 55, 9, 56, 3, 44, 22, 28], 'cur_cost': 98398.0}, {'tour': array([36,  5, 37, 51,  8,  9, 53, 18, 40, 33, 26,  1, 58, 45, 62, 16, 49,
       54,  7, 21, 23, 39,  0,  6, 44, 59, 63,  3, 17, 42, 10, 25, 57,  4,
       60, 24, 61, 46, 52, 55, 22, 38, 35, 56, 15, 50, 30, 12, 20, 11, 13,
       32, 28, 14, 47, 19, 65, 43, 31, 27, 48, 41, 34,  2, 29, 64],
      dtype=int64), 'cur_cost': 111027.0}, {'tour': [0, 7, 14, 12, 22, 23, 16, 18, 17, 15, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 9, 11, 1, 5, 4, 8, 2, 6, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12326.0}, {'tour': [27, 54, 9, 33, 22, 40, 52, 55, 45, 65, 5, 6, 7, 48, 2, 47, 29, 57, 16, 28, 63, 35, 39, 11, 4, 10, 32, 3, 23, 64, 43, 60, 56, 19, 42, 62, 25, 14, 30, 0, 38, 8, 13, 18, 61, 21, 59, 44, 58, 1, 51, 41, 12, 36, 26, 20, 46, 34, 49, 15, 24, 37, 31, 53, 50, 17], 'cur_cost': 113275.0}, {'tour': [12, 14, 0, 22, 16, 37, 36, 15, 13, 21, 20, 9, 7, 58, 35, 28, 31, 3, 54, 64, 2, 6, 4, 53, 52, 40, 55, 51, 62, 43, 19, 49, 48, 45, 30, 44, 63, 41, 33, 18, 61, 27, 50, 1, 56, 39, 59, 17, 29, 42, 57, 65, 38, 23, 47, 60, 8, 24, 26, 46, 10, 32, 11, 25, 5, 34], 'cur_cost': 97509.0}, {'tour': [48, 22, 38, 58, 63, 14, 17, 52, 4, 37, 30, 65, 35, 56, 20, 60, 16, 26, 42, 24, 33, 9, 34, 40, 47, 15, 8, 5, 31, 59, 28, 41, 55, 29, 23, 49, 19, 2, 51, 46, 3, 62, 0, 53, 54, 12, 45, 6, 11, 10, 43, 13, 7, 27, 39, 36, 64, 1, 32, 57, 25, 18, 21, 61, 44, 50], 'cur_cost': 106266.0}, {'tour': [42, 47, 22, 37, 27, 14, 12, 9, 53, 10, 63, 64, 13, 19, 31, 21, 29, 1, 62, 55, 11, 59, 18, 28, 30, 23, 36, 15, 8, 0, 52, 65, 3, 61, 4, 54, 39, 17, 2, 20, 26, 24, 7, 58, 49, 40, 38, 50, 48, 44, 16, 5, 35, 25, 34, 46, 41, 43, 32, 45, 6, 60, 56, 57, 51, 33], 'cur_cost': 68436.0}, {'tour': [40, 65, 53, 28, 47, 31, 61, 26, 50, 15, 32, 54, 20, 24, 64, 60, 8, 9, 37, 63, 58, 51, 49, 41, 16, 21, 10, 11, 22, 45, 12, 55, 25, 1, 3, 57, 18, 5, 14, 36, 43, 62, 30, 19, 2, 0, 29, 44, 23, 39, 6, 56, 17, 46, 42, 34, 48, 4, 13, 27, 38, 59, 7, 33, 52, 35], 'cur_cost': 108741.0}, {'tour': [0, 22, 10, 1, 7, 3, 9, 11, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12770.0}, {'tour': [55, 13, 33, 38, 63, 51, 29, 36, 48, 22, 18, 37, 39, 24, 19, 6, 7, 34, 25, 11, 21, 64, 45, 47, 44, 4, 31, 20, 59, 60, 9, 56, 26, 15, 8, 2, 65, 40, 14, 46, 54, 41, 32, 58, 16, 28, 23, 57, 53, 10, 50, 35, 42, 27, 0, 43, 49, 61, 62, 1, 17, 30, 3, 12, 52, 5], 'cur_cost': 101808.0}]
2025-07-30 22:04:54,238 - ExploitationExpert - INFO - 局部搜索耗时: 0.06秒
2025-07-30 22:04:54,238 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 6, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 6, 'cache_hits': 0, 'similarity_calculations': 15, 'cache_hit_rate': 0.0, 'cache_size': 15}}
2025-07-30 22:04:54,238 - collaboration_manager - INFO - 个体 1 利用路径生成报告: {'new_tour': array([36,  5, 37, 51,  8,  9, 53, 18, 40, 33, 26,  1, 58, 45, 62, 16, 49,
       54,  7, 21, 23, 39,  0,  6, 44, 59, 63,  3, 17, 42, 10, 25, 57,  4,
       60, 24, 61, 46, 52, 55, 22, 38, 35, 56, 15, 50, 30, 12, 20, 11, 13,
       32, 28, 14, 47, 19, 65, 43, 31, 27, 48, 41, 34,  2, 29, 64],
      dtype=int64), 'cur_cost': 111027.0}
2025-07-30 22:04:54,238 - collaboration_manager - INFO - 为个体 2 生成探索路径
2025-07-30 22:04:54,238 - ExplorationExpert - INFO - 开始为个体 2 生成探索路径（算法实现）
2025-07-30 22:04:54,238 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-07-30 22:04:54,238 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-07-30 22:04:54,238 - ExplorationExpert - INFO - 计算路径成本
2025-07-30 22:04:54,238 - ExplorationExpert - INFO - 探索路径生成完成，成本: 63786.0, 路径长度: 66
2025-07-30 22:04:54,238 - collaboration_manager - INFO - 个体 2 探索路径生成报告: {'new_tour': [7, 1, 52, 6, 9, 22, 14, 24, 16, 8, 5, 20, 17, 25, 35, 34, 2, 3, 23, 12, 33, 31, 11, 64, 13, 19, 10, 62, 15, 40, 21, 39, 44, 36, 4, 60, 53, 59, 54, 48, 50, 49, 38, 27, 32, 26, 46, 45, 30, 47, 37, 29, 51, 42, 58, 61, 65, 55, 0, 56, 18, 43, 41, 28, 63, 57], 'cur_cost': 63786.0}
2025-07-30 22:04:54,238 - collaboration_manager - INFO - 为个体 3 生成利用路径
2025-07-30 22:04:54,238 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-30 22:04:54,238 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-30 22:04:54,238 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 93048.0
2025-07-30 22:04:54,309 - ExploitationExpert - INFO - res_population_num: 7
2025-07-30 22:04:54,309 - ExploitationExpert - INFO - res_population_costs: [9521, 9526, 9529, 9540, 9542, 9546.0, 9521]
2025-07-30 22:04:54,309 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27,
       37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 43, 40,
       49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 14, 15, 17, 12, 22, 23, 16, 18, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 14, 15, 17, 12, 22, 23, 16, 18, 19, 27, 37,
       36, 26, 25, 31, 33, 35, 34, 30, 28, 32, 29, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 43, 40,
       49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 54, 57, 64,
       53, 62, 59, 56, 55, 61, 65, 52, 63,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-07-30 22:04:54,309 - ExploitationExpert - INFO - populations_num: 10
2025-07-30 22:04:54,309 - ExploitationExpert - INFO - populations: [{'tour': [4, 7, 24, 0, 15, 23, 26, 37, 17, 27, 40, 20, 12, 33, 63, 46, 43, 57, 36, 8, 18, 30, 21, 11, 31, 54, 64, 58, 10, 16, 6, 5, 48, 19, 35, 53, 52, 14, 42, 51, 38, 13, 59, 41, 61, 60, 29, 50, 34, 2, 1, 47, 39, 32, 45, 65, 49, 25, 62, 55, 9, 56, 3, 44, 22, 28], 'cur_cost': 98398.0}, {'tour': array([36,  5, 37, 51,  8,  9, 53, 18, 40, 33, 26,  1, 58, 45, 62, 16, 49,
       54,  7, 21, 23, 39,  0,  6, 44, 59, 63,  3, 17, 42, 10, 25, 57,  4,
       60, 24, 61, 46, 52, 55, 22, 38, 35, 56, 15, 50, 30, 12, 20, 11, 13,
       32, 28, 14, 47, 19, 65, 43, 31, 27, 48, 41, 34,  2, 29, 64],
      dtype=int64), 'cur_cost': 111027.0}, {'tour': [7, 1, 52, 6, 9, 22, 14, 24, 16, 8, 5, 20, 17, 25, 35, 34, 2, 3, 23, 12, 33, 31, 11, 64, 13, 19, 10, 62, 15, 40, 21, 39, 44, 36, 4, 60, 53, 59, 54, 48, 50, 49, 38, 27, 32, 26, 46, 45, 30, 47, 37, 29, 51, 42, 58, 61, 65, 55, 0, 56, 18, 43, 41, 28, 63, 57], 'cur_cost': 63786.0}, {'tour': array([45, 44, 30, 61,  7, 57, 58, 52, 14, 33, 40, 65, 54, 59, 60, 18, 29,
       28, 36,  8,  6,  1,  5, 46, 32, 53, 27, 35, 15, 49, 56, 48, 25, 24,
       16, 23, 41,  0, 63, 22,  3, 12, 11, 55, 51, 39, 21,  9, 19, 62, 31,
        4, 34, 47, 13, 10, 26, 64, 38, 42, 37, 20, 17, 50,  2, 43],
      dtype=int64), 'cur_cost': 93048.0}, {'tour': [12, 14, 0, 22, 16, 37, 36, 15, 13, 21, 20, 9, 7, 58, 35, 28, 31, 3, 54, 64, 2, 6, 4, 53, 52, 40, 55, 51, 62, 43, 19, 49, 48, 45, 30, 44, 63, 41, 33, 18, 61, 27, 50, 1, 56, 39, 59, 17, 29, 42, 57, 65, 38, 23, 47, 60, 8, 24, 26, 46, 10, 32, 11, 25, 5, 34], 'cur_cost': 97509.0}, {'tour': [48, 22, 38, 58, 63, 14, 17, 52, 4, 37, 30, 65, 35, 56, 20, 60, 16, 26, 42, 24, 33, 9, 34, 40, 47, 15, 8, 5, 31, 59, 28, 41, 55, 29, 23, 49, 19, 2, 51, 46, 3, 62, 0, 53, 54, 12, 45, 6, 11, 10, 43, 13, 7, 27, 39, 36, 64, 1, 32, 57, 25, 18, 21, 61, 44, 50], 'cur_cost': 106266.0}, {'tour': [42, 47, 22, 37, 27, 14, 12, 9, 53, 10, 63, 64, 13, 19, 31, 21, 29, 1, 62, 55, 11, 59, 18, 28, 30, 23, 36, 15, 8, 0, 52, 65, 3, 61, 4, 54, 39, 17, 2, 20, 26, 24, 7, 58, 49, 40, 38, 50, 48, 44, 16, 5, 35, 25, 34, 46, 41, 43, 32, 45, 6, 60, 56, 57, 51, 33], 'cur_cost': 68436.0}, {'tour': [40, 65, 53, 28, 47, 31, 61, 26, 50, 15, 32, 54, 20, 24, 64, 60, 8, 9, 37, 63, 58, 51, 49, 41, 16, 21, 10, 11, 22, 45, 12, 55, 25, 1, 3, 57, 18, 5, 14, 36, 43, 62, 30, 19, 2, 0, 29, 44, 23, 39, 6, 56, 17, 46, 42, 34, 48, 4, 13, 27, 38, 59, 7, 33, 52, 35], 'cur_cost': 108741.0}, {'tour': [0, 22, 10, 1, 7, 3, 9, 11, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12770.0}, {'tour': [55, 13, 33, 38, 63, 51, 29, 36, 48, 22, 18, 37, 39, 24, 19, 6, 7, 34, 25, 11, 21, 64, 45, 47, 44, 4, 31, 20, 59, 60, 9, 56, 26, 15, 8, 2, 65, 40, 14, 46, 54, 41, 32, 58, 16, 28, 23, 57, 53, 10, 50, 35, 42, 27, 0, 43, 49, 61, 62, 1, 17, 30, 3, 12, 52, 5], 'cur_cost': 101808.0}]
2025-07-30 22:04:54,309 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒
2025-07-30 22:04:54,309 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 7, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 7, 'cache_hits': 0, 'similarity_calculations': 21, 'cache_hit_rate': 0.0, 'cache_size': 21}}
2025-07-30 22:04:54,309 - collaboration_manager - INFO - 个体 3 利用路径生成报告: {'new_tour': array([45, 44, 30, 61,  7, 57, 58, 52, 14, 33, 40, 65, 54, 59, 60, 18, 29,
       28, 36,  8,  6,  1,  5, 46, 32, 53, 27, 35, 15, 49, 56, 48, 25, 24,
       16, 23, 41,  0, 63, 22,  3, 12, 11, 55, 51, 39, 21,  9, 19, 62, 31,
        4, 34, 47, 13, 10, 26, 64, 38, 42, 37, 20, 17, 50,  2, 43],
      dtype=int64), 'cur_cost': 93048.0}
2025-07-30 22:04:54,309 - collaboration_manager - INFO - 为个体 4 生成探索路径
2025-07-30 22:04:54,309 - ExplorationExpert - INFO - 开始为个体 4 生成探索路径（算法实现）
2025-07-30 22:04:54,309 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-07-30 22:04:54,321 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-07-30 22:04:54,321 - ExplorationExpert - INFO - 计算路径成本
2025-07-30 22:04:54,321 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12329.0, 路径长度: 66
2025-07-30 22:04:54,321 - collaboration_manager - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 10, 4, 19, 16, 18, 12, 22, 23, 13, 20, 21, 14, 15, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12329.0}
2025-07-30 22:04:54,322 - collaboration_manager - INFO - 为个体 5 生成利用路径
2025-07-30 22:04:54,322 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-30 22:04:54,322 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-30 22:04:54,322 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 120677.0
2025-07-30 22:04:54,371 - ExploitationExpert - INFO - res_population_num: 8
2025-07-30 22:04:54,371 - ExploitationExpert - INFO - res_population_costs: [9521, 9526, 9529, 9540, 9542, 9546.0, 9521, 9521]
2025-07-30 22:04:54,371 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27,
       37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 43, 40,
       49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 14, 15, 17, 12, 22, 23, 16, 18, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 14, 15, 17, 12, 22, 23, 16, 18, 19, 27, 37,
       36, 26, 25, 31, 33, 35, 34, 30, 28, 32, 29, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 43, 40,
       49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 54, 57, 64,
       53, 62, 59, 56, 55, 61, 65, 52, 63,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-07-30 22:04:54,384 - ExploitationExpert - INFO - populations_num: 10
2025-07-30 22:04:54,384 - ExploitationExpert - INFO - populations: [{'tour': [4, 7, 24, 0, 15, 23, 26, 37, 17, 27, 40, 20, 12, 33, 63, 46, 43, 57, 36, 8, 18, 30, 21, 11, 31, 54, 64, 58, 10, 16, 6, 5, 48, 19, 35, 53, 52, 14, 42, 51, 38, 13, 59, 41, 61, 60, 29, 50, 34, 2, 1, 47, 39, 32, 45, 65, 49, 25, 62, 55, 9, 56, 3, 44, 22, 28], 'cur_cost': 98398.0}, {'tour': array([36,  5, 37, 51,  8,  9, 53, 18, 40, 33, 26,  1, 58, 45, 62, 16, 49,
       54,  7, 21, 23, 39,  0,  6, 44, 59, 63,  3, 17, 42, 10, 25, 57,  4,
       60, 24, 61, 46, 52, 55, 22, 38, 35, 56, 15, 50, 30, 12, 20, 11, 13,
       32, 28, 14, 47, 19, 65, 43, 31, 27, 48, 41, 34,  2, 29, 64],
      dtype=int64), 'cur_cost': 111027.0}, {'tour': [7, 1, 52, 6, 9, 22, 14, 24, 16, 8, 5, 20, 17, 25, 35, 34, 2, 3, 23, 12, 33, 31, 11, 64, 13, 19, 10, 62, 15, 40, 21, 39, 44, 36, 4, 60, 53, 59, 54, 48, 50, 49, 38, 27, 32, 26, 46, 45, 30, 47, 37, 29, 51, 42, 58, 61, 65, 55, 0, 56, 18, 43, 41, 28, 63, 57], 'cur_cost': 63786.0}, {'tour': array([45, 44, 30, 61,  7, 57, 58, 52, 14, 33, 40, 65, 54, 59, 60, 18, 29,
       28, 36,  8,  6,  1,  5, 46, 32, 53, 27, 35, 15, 49, 56, 48, 25, 24,
       16, 23, 41,  0, 63, 22,  3, 12, 11, 55, 51, 39, 21,  9, 19, 62, 31,
        4, 34, 47, 13, 10, 26, 64, 38, 42, 37, 20, 17, 50,  2, 43],
      dtype=int64), 'cur_cost': 93048.0}, {'tour': [0, 10, 4, 19, 16, 18, 12, 22, 23, 13, 20, 21, 14, 15, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12329.0}, {'tour': array([10, 48, 22, 52, 57, 36, 51, 25,  6, 35,  8, 37, 55, 49, 63, 14,  1,
       12, 18, 29, 17,  9, 43, 61,  3, 31, 39, 32, 19, 24, 40, 54, 33, 20,
       27, 13, 56, 42, 44, 30, 58,  4, 62, 28, 50,  2, 64, 15, 34, 11, 46,
       59, 47, 53, 65, 21, 26,  7, 60, 45, 16,  0, 41, 23, 38,  5],
      dtype=int64), 'cur_cost': 120677.0}, {'tour': [42, 47, 22, 37, 27, 14, 12, 9, 53, 10, 63, 64, 13, 19, 31, 21, 29, 1, 62, 55, 11, 59, 18, 28, 30, 23, 36, 15, 8, 0, 52, 65, 3, 61, 4, 54, 39, 17, 2, 20, 26, 24, 7, 58, 49, 40, 38, 50, 48, 44, 16, 5, 35, 25, 34, 46, 41, 43, 32, 45, 6, 60, 56, 57, 51, 33], 'cur_cost': 68436.0}, {'tour': [40, 65, 53, 28, 47, 31, 61, 26, 50, 15, 32, 54, 20, 24, 64, 60, 8, 9, 37, 63, 58, 51, 49, 41, 16, 21, 10, 11, 22, 45, 12, 55, 25, 1, 3, 57, 18, 5, 14, 36, 43, 62, 30, 19, 2, 0, 29, 44, 23, 39, 6, 56, 17, 46, 42, 34, 48, 4, 13, 27, 38, 59, 7, 33, 52, 35], 'cur_cost': 108741.0}, {'tour': [0, 22, 10, 1, 7, 3, 9, 11, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12770.0}, {'tour': [55, 13, 33, 38, 63, 51, 29, 36, 48, 22, 18, 37, 39, 24, 19, 6, 7, 34, 25, 11, 21, 64, 45, 47, 44, 4, 31, 20, 59, 60, 9, 56, 26, 15, 8, 2, 65, 40, 14, 46, 54, 41, 32, 58, 16, 28, 23, 57, 53, 10, 50, 35, 42, 27, 0, 43, 49, 61, 62, 1, 17, 30, 3, 12, 52, 5], 'cur_cost': 101808.0}]
2025-07-30 22:04:54,387 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒
2025-07-30 22:04:54,387 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 8, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 8, 'cache_hits': 0, 'similarity_calculations': 28, 'cache_hit_rate': 0.0, 'cache_size': 28}}
2025-07-30 22:04:54,388 - collaboration_manager - INFO - 个体 5 利用路径生成报告: {'new_tour': array([10, 48, 22, 52, 57, 36, 51, 25,  6, 35,  8, 37, 55, 49, 63, 14,  1,
       12, 18, 29, 17,  9, 43, 61,  3, 31, 39, 32, 19, 24, 40, 54, 33, 20,
       27, 13, 56, 42, 44, 30, 58,  4, 62, 28, 50,  2, 64, 15, 34, 11, 46,
       59, 47, 53, 65, 21, 26,  7, 60, 45, 16,  0, 41, 23, 38,  5],
      dtype=int64), 'cur_cost': 120677.0}
2025-07-30 22:04:54,389 - collaboration_manager - INFO - 为个体 6 生成探索路径
2025-07-30 22:04:54,389 - ExplorationExpert - INFO - 开始为个体 6 生成探索路径（算法实现）
2025-07-30 22:04:54,389 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-07-30 22:04:54,389 - ExplorationExpert - INFO - 使用策略 hybrid_construction 生成探索路径，长度: 66
2025-07-30 22:04:54,389 - ExplorationExpert - INFO - 计算路径成本
2025-07-30 22:04:54,389 - ExplorationExpert - INFO - 探索路径生成完成，成本: 48184.0, 路径长度: 66
2025-07-30 22:04:54,389 - collaboration_manager - INFO - 个体 6 探索路径生成报告: {'new_tour': [17, 23, 14, 22, 15, 19, 2, 52, 63, 65, 13, 6, 18, 9, 54, 64, 0, 11, 1, 21, 24, 32, 30, 27, 37, 31, 35, 49, 36, 40, 44, 34, 43, 12, 39, 50, 51, 16, 8, 55, 62, 59, 60, 5, 7, 20, 29, 28, 26, 25, 48, 38, 41, 42, 33, 10, 61, 57, 58, 53, 3, 4, 56, 47, 46, 45], 'cur_cost': 48184.0}
2025-07-30 22:04:54,389 - collaboration_manager - INFO - 为个体 7 生成利用路径
2025-07-30 22:04:54,389 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-30 22:04:54,389 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-30 22:04:54,389 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 117198.0
2025-07-30 22:04:54,438 - ExploitationExpert - INFO - res_population_num: 8
2025-07-30 22:04:54,438 - ExploitationExpert - INFO - res_population_costs: [9521, 9526, 9529, 9540, 9542, 9546.0, 9521, 9521]
2025-07-30 22:04:54,438 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27,
       37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 43, 40,
       49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 14, 15, 17, 12, 22, 23, 16, 18, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 14, 15, 17, 12, 22, 23, 16, 18, 19, 27, 37,
       36, 26, 25, 31, 33, 35, 34, 30, 28, 32, 29, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 43, 40,
       49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 54, 57, 64,
       53, 62, 59, 56, 55, 61, 65, 52, 63,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-07-30 22:04:54,454 - ExploitationExpert - INFO - populations_num: 10
2025-07-30 22:04:54,454 - ExploitationExpert - INFO - populations: [{'tour': [4, 7, 24, 0, 15, 23, 26, 37, 17, 27, 40, 20, 12, 33, 63, 46, 43, 57, 36, 8, 18, 30, 21, 11, 31, 54, 64, 58, 10, 16, 6, 5, 48, 19, 35, 53, 52, 14, 42, 51, 38, 13, 59, 41, 61, 60, 29, 50, 34, 2, 1, 47, 39, 32, 45, 65, 49, 25, 62, 55, 9, 56, 3, 44, 22, 28], 'cur_cost': 98398.0}, {'tour': array([36,  5, 37, 51,  8,  9, 53, 18, 40, 33, 26,  1, 58, 45, 62, 16, 49,
       54,  7, 21, 23, 39,  0,  6, 44, 59, 63,  3, 17, 42, 10, 25, 57,  4,
       60, 24, 61, 46, 52, 55, 22, 38, 35, 56, 15, 50, 30, 12, 20, 11, 13,
       32, 28, 14, 47, 19, 65, 43, 31, 27, 48, 41, 34,  2, 29, 64],
      dtype=int64), 'cur_cost': 111027.0}, {'tour': [7, 1, 52, 6, 9, 22, 14, 24, 16, 8, 5, 20, 17, 25, 35, 34, 2, 3, 23, 12, 33, 31, 11, 64, 13, 19, 10, 62, 15, 40, 21, 39, 44, 36, 4, 60, 53, 59, 54, 48, 50, 49, 38, 27, 32, 26, 46, 45, 30, 47, 37, 29, 51, 42, 58, 61, 65, 55, 0, 56, 18, 43, 41, 28, 63, 57], 'cur_cost': 63786.0}, {'tour': array([45, 44, 30, 61,  7, 57, 58, 52, 14, 33, 40, 65, 54, 59, 60, 18, 29,
       28, 36,  8,  6,  1,  5, 46, 32, 53, 27, 35, 15, 49, 56, 48, 25, 24,
       16, 23, 41,  0, 63, 22,  3, 12, 11, 55, 51, 39, 21,  9, 19, 62, 31,
        4, 34, 47, 13, 10, 26, 64, 38, 42, 37, 20, 17, 50,  2, 43],
      dtype=int64), 'cur_cost': 93048.0}, {'tour': [0, 10, 4, 19, 16, 18, 12, 22, 23, 13, 20, 21, 14, 15, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12329.0}, {'tour': array([10, 48, 22, 52, 57, 36, 51, 25,  6, 35,  8, 37, 55, 49, 63, 14,  1,
       12, 18, 29, 17,  9, 43, 61,  3, 31, 39, 32, 19, 24, 40, 54, 33, 20,
       27, 13, 56, 42, 44, 30, 58,  4, 62, 28, 50,  2, 64, 15, 34, 11, 46,
       59, 47, 53, 65, 21, 26,  7, 60, 45, 16,  0, 41, 23, 38,  5],
      dtype=int64), 'cur_cost': 120677.0}, {'tour': [17, 23, 14, 22, 15, 19, 2, 52, 63, 65, 13, 6, 18, 9, 54, 64, 0, 11, 1, 21, 24, 32, 30, 27, 37, 31, 35, 49, 36, 40, 44, 34, 43, 12, 39, 50, 51, 16, 8, 55, 62, 59, 60, 5, 7, 20, 29, 28, 26, 25, 48, 38, 41, 42, 33, 10, 61, 57, 58, 53, 3, 4, 56, 47, 46, 45], 'cur_cost': 48184.0}, {'tour': array([62, 18, 20,  7, 17, 21, 26, 12,  9, 31, 16, 65, 48, 58, 38, 37, 27,
       46, 57,  8, 25, 39, 35, 33, 54, 10, 41, 56, 40, 24,  6, 29, 32,  3,
       43, 55, 63, 42,  1, 53, 44,  4, 23, 28,  5, 52, 49, 45, 51, 22, 19,
       47, 61, 30, 59, 34,  2, 15, 60, 14, 36, 64, 11, 50,  0, 13],
      dtype=int64), 'cur_cost': 117198.0}, {'tour': [0, 22, 10, 1, 7, 3, 9, 11, 5, 4, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 12770.0}, {'tour': [55, 13, 33, 38, 63, 51, 29, 36, 48, 22, 18, 37, 39, 24, 19, 6, 7, 34, 25, 11, 21, 64, 45, 47, 44, 4, 31, 20, 59, 60, 9, 56, 26, 15, 8, 2, 65, 40, 14, 46, 54, 41, 32, 58, 16, 28, 23, 57, 53, 10, 50, 35, 42, 27, 0, 43, 49, 61, 62, 1, 17, 30, 3, 12, 52, 5], 'cur_cost': 101808.0}]
2025-07-30 22:04:54,454 - ExploitationExpert - INFO - 局部搜索耗时: 0.07秒
2025-07-30 22:04:54,454 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 9, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 9, 'cache_hits': 0, 'similarity_calculations': 36, 'cache_hit_rate': 0.0, 'cache_size': 36}}
2025-07-30 22:04:54,454 - collaboration_manager - INFO - 个体 7 利用路径生成报告: {'new_tour': array([62, 18, 20,  7, 17, 21, 26, 12,  9, 31, 16, 65, 48, 58, 38, 37, 27,
       46, 57,  8, 25, 39, 35, 33, 54, 10, 41, 56, 40, 24,  6, 29, 32,  3,
       43, 55, 63, 42,  1, 53, 44,  4, 23, 28,  5, 52, 49, 45, 51, 22, 19,
       47, 61, 30, 59, 34,  2, 15, 60, 14, 36, 64, 11, 50,  0, 13],
      dtype=int64), 'cur_cost': 117198.0}
2025-07-30 22:04:54,454 - collaboration_manager - INFO - 为个体 8 生成探索路径
2025-07-30 22:04:54,454 - ExplorationExpert - INFO - 开始为个体 8 生成探索路径（算法实现）
2025-07-30 22:04:54,454 - ExplorationExpert - INFO - 开始生成多样化探索路径
2025-07-30 22:04:54,454 - ExplorationExpert - INFO - 使用策略 region_exploration 生成探索路径，长度: 66
2025-07-30 22:04:54,454 - ExplorationExpert - INFO - 计算路径成本
2025-07-30 22:04:54,454 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10464.0, 路径长度: 66
2025-07-30 22:04:54,454 - collaboration_manager - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 6, 3, 8, 2, 4, 5, 9, 11, 7, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10464.0}
2025-07-30 22:04:54,454 - collaboration_manager - INFO - 为个体 9 生成利用路径
2025-07-30 22:04:54,454 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-30 22:04:54,454 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-30 22:04:54,454 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 103039.0
2025-07-30 22:04:54,505 - ExploitationExpert - INFO - res_population_num: 8
2025-07-30 22:04:54,505 - ExploitationExpert - INFO - res_population_costs: [9521, 9526, 9529, 9540, 9542, 9546.0, 9521, 9521]
2025-07-30 22:04:54,505 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27,
       37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 43, 40,
       49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 14, 15, 17, 12, 22, 23, 16, 18, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 14, 15, 17, 12, 22, 23, 16, 18, 19, 27, 37,
       36, 26, 25, 31, 33, 35, 34, 30, 28, 32, 29, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 43, 40,
       49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 54, 57, 64,
       53, 62, 59, 56, 55, 61, 65, 52, 63,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-07-30 22:04:54,505 - ExploitationExpert - INFO - populations_num: 10
2025-07-30 22:04:54,505 - ExploitationExpert - INFO - populations: [{'tour': [4, 7, 24, 0, 15, 23, 26, 37, 17, 27, 40, 20, 12, 33, 63, 46, 43, 57, 36, 8, 18, 30, 21, 11, 31, 54, 64, 58, 10, 16, 6, 5, 48, 19, 35, 53, 52, 14, 42, 51, 38, 13, 59, 41, 61, 60, 29, 50, 34, 2, 1, 47, 39, 32, 45, 65, 49, 25, 62, 55, 9, 56, 3, 44, 22, 28], 'cur_cost': 98398.0}, {'tour': array([36,  5, 37, 51,  8,  9, 53, 18, 40, 33, 26,  1, 58, 45, 62, 16, 49,
       54,  7, 21, 23, 39,  0,  6, 44, 59, 63,  3, 17, 42, 10, 25, 57,  4,
       60, 24, 61, 46, 52, 55, 22, 38, 35, 56, 15, 50, 30, 12, 20, 11, 13,
       32, 28, 14, 47, 19, 65, 43, 31, 27, 48, 41, 34,  2, 29, 64],
      dtype=int64), 'cur_cost': 111027.0}, {'tour': [7, 1, 52, 6, 9, 22, 14, 24, 16, 8, 5, 20, 17, 25, 35, 34, 2, 3, 23, 12, 33, 31, 11, 64, 13, 19, 10, 62, 15, 40, 21, 39, 44, 36, 4, 60, 53, 59, 54, 48, 50, 49, 38, 27, 32, 26, 46, 45, 30, 47, 37, 29, 51, 42, 58, 61, 65, 55, 0, 56, 18, 43, 41, 28, 63, 57], 'cur_cost': 63786.0}, {'tour': array([45, 44, 30, 61,  7, 57, 58, 52, 14, 33, 40, 65, 54, 59, 60, 18, 29,
       28, 36,  8,  6,  1,  5, 46, 32, 53, 27, 35, 15, 49, 56, 48, 25, 24,
       16, 23, 41,  0, 63, 22,  3, 12, 11, 55, 51, 39, 21,  9, 19, 62, 31,
        4, 34, 47, 13, 10, 26, 64, 38, 42, 37, 20, 17, 50,  2, 43],
      dtype=int64), 'cur_cost': 93048.0}, {'tour': [0, 10, 4, 19, 16, 18, 12, 22, 23, 13, 20, 21, 14, 15, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12329.0}, {'tour': array([10, 48, 22, 52, 57, 36, 51, 25,  6, 35,  8, 37, 55, 49, 63, 14,  1,
       12, 18, 29, 17,  9, 43, 61,  3, 31, 39, 32, 19, 24, 40, 54, 33, 20,
       27, 13, 56, 42, 44, 30, 58,  4, 62, 28, 50,  2, 64, 15, 34, 11, 46,
       59, 47, 53, 65, 21, 26,  7, 60, 45, 16,  0, 41, 23, 38,  5],
      dtype=int64), 'cur_cost': 120677.0}, {'tour': [17, 23, 14, 22, 15, 19, 2, 52, 63, 65, 13, 6, 18, 9, 54, 64, 0, 11, 1, 21, 24, 32, 30, 27, 37, 31, 35, 49, 36, 40, 44, 34, 43, 12, 39, 50, 51, 16, 8, 55, 62, 59, 60, 5, 7, 20, 29, 28, 26, 25, 48, 38, 41, 42, 33, 10, 61, 57, 58, 53, 3, 4, 56, 47, 46, 45], 'cur_cost': 48184.0}, {'tour': array([62, 18, 20,  7, 17, 21, 26, 12,  9, 31, 16, 65, 48, 58, 38, 37, 27,
       46, 57,  8, 25, 39, 35, 33, 54, 10, 41, 56, 40, 24,  6, 29, 32,  3,
       43, 55, 63, 42,  1, 53, 44,  4, 23, 28,  5, 52, 49, 45, 51, 22, 19,
       47, 61, 30, 59, 34,  2, 15, 60, 14, 36, 64, 11, 50,  0, 13],
      dtype=int64), 'cur_cost': 117198.0}, {'tour': [0, 6, 3, 8, 2, 4, 5, 9, 11, 7, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10464.0}, {'tour': array([17, 12, 21,  9, 10, 42, 14, 48, 41, 52, 44, 13, 16, 39, 54, 27, 61,
        2,  1, 11, 56, 38, 46, 36, 23, 22, 25, 55,  4, 47,  6,  0, 62, 30,
       64, 15, 34,  3,  8, 51, 31, 40, 28, 35, 45, 29, 49, 60, 33, 65, 32,
        5, 18, 20, 53,  7, 57, 19, 59, 63, 43, 50, 58, 24, 37, 26],
      dtype=int64), 'cur_cost': 103039.0}]
2025-07-30 22:04:54,505 - ExploitationExpert - INFO - 局部搜索耗时: 0.05秒
2025-07-30 22:04:54,505 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 10, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 10, 'cache_hits': 0, 'similarity_calculations': 45, 'cache_hit_rate': 0.0, 'cache_size': 45}}
2025-07-30 22:04:54,505 - collaboration_manager - INFO - 个体 9 利用路径生成报告: {'new_tour': array([17, 12, 21,  9, 10, 42, 14, 48, 41, 52, 44, 13, 16, 39, 54, 27, 61,
        2,  1, 11, 56, 38, 46, 36, 23, 22, 25, 55,  4, 47,  6,  0, 62, 30,
       64, 15, 34,  3,  8, 51, 31, 40, 28, 35, 45, 29, 49, 60, 33, 65, 32,
        5, 18, 20, 53,  7, 57, 19, 59, 63, 43, 50, 58, 24, 37, 26],
      dtype=int64), 'cur_cost': 103039.0}
2025-07-30 22:04:54,505 - collaboration_manager - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [4, 7, 24, 0, 15, 23, 26, 37, 17, 27, 40, 20, 12, 33, 63, 46, 43, 57, 36, 8, 18, 30, 21, 11, 31, 54, 64, 58, 10, 16, 6, 5, 48, 19, 35, 53, 52, 14, 42, 51, 38, 13, 59, 41, 61, 60, 29, 50, 34, 2, 1, 47, 39, 32, 45, 65, 49, 25, 62, 55, 9, 56, 3, 44, 22, 28], 'cur_cost': 98398.0}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([36,  5, 37, 51,  8,  9, 53, 18, 40, 33, 26,  1, 58, 45, 62, 16, 49,
       54,  7, 21, 23, 39,  0,  6, 44, 59, 63,  3, 17, 42, 10, 25, 57,  4,
       60, 24, 61, 46, 52, 55, 22, 38, 35, 56, 15, 50, 30, 12, 20, 11, 13,
       32, 28, 14, 47, 19, 65, 43, 31, 27, 48, 41, 34,  2, 29, 64],
      dtype=int64), 'cur_cost': 111027.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [7, 1, 52, 6, 9, 22, 14, 24, 16, 8, 5, 20, 17, 25, 35, 34, 2, 3, 23, 12, 33, 31, 11, 64, 13, 19, 10, 62, 15, 40, 21, 39, 44, 36, 4, 60, 53, 59, 54, 48, 50, 49, 38, 27, 32, 26, 46, 45, 30, 47, 37, 29, 51, 42, 58, 61, 65, 55, 0, 56, 18, 43, 41, 28, 63, 57], 'cur_cost': 63786.0}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([45, 44, 30, 61,  7, 57, 58, 52, 14, 33, 40, 65, 54, 59, 60, 18, 29,
       28, 36,  8,  6,  1,  5, 46, 32, 53, 27, 35, 15, 49, 56, 48, 25, 24,
       16, 23, 41,  0, 63, 22,  3, 12, 11, 55, 51, 39, 21,  9, 19, 62, 31,
        4, 34, 47, 13, 10, 26, 64, 38, 42, 37, 20, 17, 50,  2, 43],
      dtype=int64), 'cur_cost': 93048.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 10, 4, 19, 16, 18, 12, 22, 23, 13, 20, 21, 14, 15, 17, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 8, 2, 6, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 12329.0}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([10, 48, 22, 52, 57, 36, 51, 25,  6, 35,  8, 37, 55, 49, 63, 14,  1,
       12, 18, 29, 17,  9, 43, 61,  3, 31, 39, 32, 19, 24, 40, 54, 33, 20,
       27, 13, 56, 42, 44, 30, 58,  4, 62, 28, 50,  2, 64, 15, 34, 11, 46,
       59, 47, 53, 65, 21, 26,  7, 60, 45, 16,  0, 41, 23, 38,  5],
      dtype=int64), 'cur_cost': 120677.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [17, 23, 14, 22, 15, 19, 2, 52, 63, 65, 13, 6, 18, 9, 54, 64, 0, 11, 1, 21, 24, 32, 30, 27, 37, 31, 35, 49, 36, 40, 44, 34, 43, 12, 39, 50, 51, 16, 8, 55, 62, 59, 60, 5, 7, 20, 29, 28, 26, 25, 48, 38, 41, 42, 33, 10, 61, 57, 58, 53, 3, 4, 56, 47, 46, 45], 'cur_cost': 48184.0}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([62, 18, 20,  7, 17, 21, 26, 12,  9, 31, 16, 65, 48, 58, 38, 37, 27,
       46, 57,  8, 25, 39, 35, 33, 54, 10, 41, 56, 40, 24,  6, 29, 32,  3,
       43, 55, 63, 42,  1, 53, 44,  4, 23, 28,  5, 52, 49, 45, 51, 22, 19,
       47, 61, 30, 59, 34,  2, 15, 60, 14, 36, 64, 11, 50,  0, 13],
      dtype=int64), 'cur_cost': 117198.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 6, 3, 8, 2, 4, 5, 9, 11, 7, 1, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10464.0}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([17, 12, 21,  9, 10, 42, 14, 48, 41, 52, 44, 13, 16, 39, 54, 27, 61,
        2,  1, 11, 56, 38, 46, 36, 23, 22, 25, 55,  4, 47,  6,  0, 62, 30,
       64, 15, 34,  3,  8, 51, 31, 40, 28, 35, 45, 29, 49, 60, 33, 65, 32,
        5, 18, 20, 53,  7, 57, 19, 59, 63, 43, 50, 58, 24, 37, 26],
      dtype=int64), 'cur_cost': 103039.0}}]
2025-07-30 22:04:54,505 - collaboration_manager - INFO - 进化阶段完成
2025-07-30 22:04:54,505 - StatsExpert - INFO - 开始统计分析
2025-07-30 22:04:54,521 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=10464.0, 多样性=0.949
2025-07-30 22:04:54,521 - collaboration_manager - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-07-30 22:04:54,521 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment (Algorithm-based) ---
2025-07-30 22:04:54,521 - EvolutionAssessmentExpert - INFO - 开始执行算法评估分析
2025-07-30 22:04:54,521 - EvolutionAssessmentExpert - INFO - 算法评估报告: {'overall_score': 100, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', 'improvement_rate': 0.03416596107292997, 'best_improvement': 0.1510627940937855}, 'diversity_analysis': {'status': 'high_diversity', 'change_rate': -0.001416932341480431}, 'strategy_effectiveness': {'status': 'data_mismatch'}, 'convergence_trend': {'trend': 'insufficient_data', 'trend_strength': 0.0, 'recent_improvements': [], 'convergence_status': 'unknown'}, 'elite_analysis': {'old_count': 8, 'new_count': 8, 'count_change': 0, 'old_best_cost': 9521, 'new_best_cost': 9521, 'quality_improvement': 0.0, 'old_diversity': 0.8257575757575758, 'new_diversity': 0.8257575757575758, 'diversity_change': 0.0}, 'suggestions': ['种群多样性较高，可适当增加利用策略进行局部优化']}
2025-07-30 22:04:54,521 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-07-30 22:04:54,521 - __main__ - INFO - composite13_66 开始进化第 3 代
2025-07-30 22:04:54,521 - collaboration_manager - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-07-30 22:04:54,521 - StatsExpert - INFO - 开始统计分析
2025-07-30 22:04:54,548 - StatsExpert - INFO - 统计分析完成: 种群大小=10, 最优成本=10464.0, 多样性=0.949
2025-07-30 22:04:54,548 - PathExpert - INFO - 开始路径结构分析
2025-07-30 22:04:54,548 - PathExpert - INFO - 路径结构分析完成: 公共边数量=0, 路径相似性=0.022
2025-07-30 22:04:54,548 - EliteExpert - INFO - 开始精英解分析
2025-07-30 22:04:54,553 - EliteExpert - INFO - 精英解分析完成: 精英解数量=8, 多样性=0.826
2025-07-30 22:04:54,555 - LandscapeExpert - INFO - 开始景观分析
2025-07-30 22:04:54,555 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-30 22:04:54,555 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 2/5)
- population_size: 10
- cost_stats: min 10464.0, mean 77815.0, max 120677.0, std 41743.32339162489
- diversity: 0.9491582491582493
- convergence: 0
- clustering: No clustering information
- recent_best_costs: Not available (Δ Not available, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: []
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}
- elite_diversity: 4 key-value pairs, sample: diversity_score: 0.8257575757575758, pairwise_distances: [0.045454545454545456, 0.9696969696969697, 0.9696969696969697, 0.9848484848484849, 0.7575757575757576, 0.7575757575757576, 0.9848484848484849, 0.9696969696969697, 0.9696969696969697, 0.9696969696969697, 0.7575757575757576, 0.7575757575757576, 0.9696969696969697, 0.6060606060606061, 0.8787878787878788, 0.9696969696969697, 0.9696969696969697, 0.8636363636363636, 0.8787878787878788, 0.9848484848484849, 0.9848484848484849, 0.8636363636363636, 0.9848484848484849, 0.9848484848484849, 0.19696969696969696, 0.12121212121212122, 0.9848484848484849, 0.9848484848484849], min_distance: 0.045454545454545456

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

### Output Example
```json
{
  "search_space_features": {"ruggedness": 0.5, "modality": "multi", "deceptiveness": "unknown"},
  "population_state": {"diversity": 0.5, "convergence": 0.4, "clustering": "weak"},
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "unknown",
  "evolution_direction": {"recommended_focus": "explore", "operators": []}
}
```

### Formatting Guidelines (read carefully, DO NOT output this section)
1. Do NOT copy or repeat any *_sample values above; at least 50% of elements in difficult_regions and opportunity_regions must differ from those samples.
2. Use [] (empty array) when a list has no content; never output the string "None" or a null value.
3. For search_space_features use enumerated strings only:
    • ruggedness: "low" | "medium" | "high"
    • modality: "mono" | "multi" | "unknown"
    • deceptiveness: "low" | "moderate" | "high" | "unknown"
4. diversity and convergence must be floats with exactly 3 decimal places (e.g. 0.742).
5. Follow the JSON schema strictly; no additional keys or commentary.

2025-07-30 22:04:54,555 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-30 22:04:56,378 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 22:04:58,382 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 2/3)
2025-07-30 22:05:00,310 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 22:05:02,311 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 3/3)
2025-07-30 22:05:04,749 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 22:05:04,750 - LandscapeExpert - INFO - LLM返回的分析结果: API请求失败: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 22:05:04,750 - LandscapeExpert - WARNING - 解析景观分析结果失败: Unable to extract valid JSON data from the response
2025-07-30 22:05:04,750 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-30 22:05:04,750 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.5, 'modality': 'unknown', 'deceptiveness': 'unknown'}, 'population_state': {'diversity': 0.5, 'convergence': 0.5, 'clustering': 'unknown'}, 'difficult_regions': [], 'opportunity_regions': [], 'evolution_phase': 'unknown', 'evolution_direction': {'recommended_focus': 'balance', 'operators': []}, 'raw_text': 'API请求失败: 429 Client Error: Too Many Requests for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ'}
2025-07-30 22:05:04,750 - collaboration_manager - INFO - 开始策略分配阶段
2025-07-30 22:05:04,750 - StrategyExpert - INFO - 开始策略分配分析
2025-07-30 22:05:04,750 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 2
- Population: 10 individuals
  • diversity: 0.5
  • best_cost: 10464.0
  • mean_cost: 77815.0
- Search-space: ruggedness 0.5, modality unknown, deceptiveness unknown
- Evolution phase: unknown
- Landscape focus suggestion: balance
- Spatial density summary: None
- Elite diversity: N/A
- Difficult regions (# 0): None
- Opportunity regions (# 0): None
- Last-iteration feedback: {'overall_score': 100, 'iteration': 1, 'total_iterations': 5, 'cost_improvement': {'status': 'significant_improvement', ...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Formatting Guidelines (DO NOT output this section)
1. global_explore_ratio must be rounded to exactly 2 decimal places (e.g., 0.80).
2. The individual_assignments mapping MUST include every index from 0 to (population_size - 1) without omission.
3. Valid values are "explore" or "exploit" (lowercase).
4. rationale ≤ 40 words.

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-30 22:05:04,750 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-30 22:05:04,750 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
