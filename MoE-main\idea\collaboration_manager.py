"""
专家协作管理器模块

包含ExpertCollaborationManager类，负责管理专家间的交互和协作。
"""

import copy
import logging
from expert_base import ExpertBase
from exploration_expert import ExplorationExpert
from exploitation_expert import ExploitationExpert
from assessment_expert import EvolutionAssessmentExpert
from landscape_expert import LandscapeExpert
from strategy_expert import StrategyExpert
from stats_expert import StatsExpert
from path_expert import PathExpert
from elite_expert import EliteExpert
from gls_evol_enhanced import tour_cost
import utils


class ExpertCollaborationManager:
    """专家协作管理器，管理专家间的交互
    根据优化建议，优化专家与LLM的交互方式：
    1. 保留景观分析和策略选择专家与LLM的交互
    2. 统计分析、路径结构和精英解专家使用算法实现，不与LLM交互
    3. 探索专家使用纯算法实现，不依赖LLM生成多样化路径
    4. 利用专家完全使用局部搜索和扰动代码，不使用LLM
    5. 评估专家使用纯算法实现，基于数学指标和统计分析进行评估
    """
    
    def __init__(self, interface_llm, config=None):
        self.config = config or {}
        self.interface_llm = interface_llm
        self.experts = {}
        self._initialize_experts()
        self.logger = logging.getLogger(__name__)
        
        # 添加专家间数据共享存储
        self.shared_data = {
            "high_quality_edges": [],
            "difficult_regions": [],
            "opportunity_regions": [],
            "elite_features": {},
            "population_diversity": 0.0,
            "convergence_level": 0.0
        }
    
    def _initialize_experts(self):
        """初始化所有专家模块"""
        # 这些专家使用算法实现，不与LLM交互
        self.experts['stats'] = StatsExpert()
        self.experts['path'] = PathExpert()
        self.experts['elite'] = EliteExpert()
        
        # 这些专家需要与LLM交互
        self.experts['landscape'] = LandscapeExpert(self.interface_llm)
        self.experts['strategy'] = StrategyExpert(self.interface_llm)

        # 这些专家使用纯算法实现，不依赖LLM
        self.experts['exploration'] = ExplorationExpert()  # 纯算法实现
        self.experts['exploitation'] = ExploitationExpert()  # 纯算法实现
        self.experts['assessment'] = EvolutionAssessmentExpert()  # 纯算法实现
    
    def update_shared_data(self, key, value):
        """更新专家间共享数据"""
        if key in self.shared_data:
            self.shared_data[key] = value
            self.logger.info(f"更新共享数据: {key}")
    
    def run_analysis_phase(self, populations, res_populations, distance_matrix, iteration, total_iterations=10, coordinates=None):
        """运行分析阶段，包括统计、路径和精英分析"""
        self.logger.info(f"--- Running Analysis Phase (Iteration {iteration}) ---")
        
        # 引入空间统计计算函数
        from experts_prompt import compute_spatial_stats
        
        # 运行本地分析专家
        stats_analysis = self.experts["stats"].analyze(populations)
        stats_report = self.experts["stats"].generate_report(stats_analysis, coordinates, distance_matrix)
        
        path_analysis = self.experts["path"].analyze(populations, distance_matrix)
        path_report = self.experts["path"].generate_report(path_analysis)
        
        elite_solutions = sorted(res_populations, key=lambda p: p['cur_cost']) if res_populations else []
        elite_report = self.experts["elite"].analyze(elite_solutions, populations, distance_matrix)
        
        # 运行景观分析专家
        landscape_report = self.experts["landscape"].analyze(
            stats_report, path_report, elite_report, iteration, total_iterations, 
            history_data={'spatial_stats': compute_spatial_stats(coordinates, distance_matrix)}
        )
        
        self.update_shared_data('landscape_report', landscape_report)
        return landscape_report, stats_report
    
    def run_strategy_phase(self, landscape_report, populations, iteration, strategy_feedback=None):
        """运行策略分配阶段"""
        self.logger.info("开始策略分配阶段")
        
        # 使用优化后的策略专家
        strategy_result = self.experts['strategy'].analyze(
            landscape_report=landscape_report,
            populations=populations,
            iteration=iteration,
            strategy_feedback=strategy_feedback
        )
        
        # 记录策略分配报告
        strategy_selection, strategy_response = strategy_result
        self.logger.info(f"策略分配报告: {strategy_selection}")
        self.logger.info(f"策略分配完整报告: {strategy_response}")
        
        self.logger.info("策略分配阶段完成")
        return strategy_result

    def run_evolution_phase(self, populations, strategies, landscape_report, distance_matrix, res_populations=None):
        """运行进化阶段，生成新路径"""
        self.logger.info("开始进化阶段")

        new_populations = []
        evolution_reports = []

        for i, individual in enumerate(populations):
            # 将共享数据传递给专家
            if strategies[i] == 'explore':
                self.logger.info(f"为个体 {i} 生成探索路径")
                new_path_data = self.experts['exploration'].generate_path(
                    individual=individual,
                    landscape_report=landscape_report,  # 包含了共享数据
                    populations=populations,
                    distance_matrix=distance_matrix,
                    individual_index=i,
                    evo_populations=None,
                    res_populations=res_populations
                )

                # 记录探索路径生成报告
                if new_path_data:
                    evolution_reports.append({"individual": i, "strategy": "explore", "path_data": new_path_data})
                    self.logger.info(f"个体 {i} 探索路径生成报告: {new_path_data}")

                if new_path_data and 'new_tour' in new_path_data:
                    # 创建新个体
                    new_tour = new_path_data['new_tour']
                    new_cost = tour_cost(distance_matrix, new_tour)

                    new_individual = {
                        "tour": utils.convert_to_list(new_tour),
                        "cur_cost": new_cost
                    }
                else:
                    # 如果生成失败，保留原个体
                    new_individual = copy.deepcopy(individual)
            else:  # exploit
                self.logger.info(f"为个体 {i} 生成利用路径")

                # 传递共享数据给利用专家
                exploitation_landscape_report = {
                    "high_quality_edges": self.shared_data["high_quality_edges"],
                    "fixed_nodes": self.shared_data.get("elite_features", {}).get("fixed_nodes", []),
                    "low_quality_regions": self.shared_data["difficult_regions"]
                }

                new_path_data = self.experts['exploitation'].generate_path(
                    individual=individual,
                    landscape_report=exploitation_landscape_report,  # 使用共享数据
                    populations=populations,
                    distance_matrix=distance_matrix,
                    individual_index=i,
                    res_populations=res_populations  # 传递精英解集合
                )

                # 记录利用路径生成报告
                if new_path_data:
                    evolution_reports.append({"individual": i, "strategy": "exploit", "path_data": new_path_data})
                    self.logger.info(f"个体 {i} 利用路径生成报告: {new_path_data}")

                if new_path_data and 'new_tour' in new_path_data:
                    # 创建新个体
                    new_tour = new_path_data['new_tour']
                    new_cost = tour_cost(distance_matrix, new_tour)

                    new_individual = {
                        "tour": utils.convert_to_list(new_tour),
                        "cur_cost": new_cost
                    }
                else:
                    # 如果生成失败，保留原个体
                    self.logger.warning(f"个体 {i} 的利用路径生成失败，保留原个体")
                    new_individual = copy.deepcopy(individual)

            new_populations.append(new_individual)

        # 记录整体进化报告
        self.logger.info(f"进化阶段报告汇总: {evolution_reports}")
        self.logger.info("进化阶段完成")
        return new_populations

    def run_assessment_phase(self, old_stats_report, new_stats_report, strategies, iteration, total_iterations, old_res_populations=None, new_res_populations=None):
        """运行评估阶段"""
        self.logger.info(f"--- Running Assessment Phase (Iteration {iteration}) ---")

        assessment_report = self.experts["assessment"].evaluate(
            old_stats_report, new_stats_report, strategies, iteration, total_iterations,
            old_res_populations=old_res_populations, new_res_populations=new_res_populations
        )

        # 更新共享数据
        self.update_shared_data('assessment_report', assessment_report)
        return assessment_report
