"""
利用专家模块

包含ExploitationExpert类，负责为分配了利用策略的个体生成局部优化的新路径。
集成路径相似度优化器，提高路径存储和相似度比较效率。
"""

import copy
import time
import numpy as np
from expert_base import ExpertBase
from path_similarity_optimizer import PathSimilarityOptimizer
import gls_evol_enhanced
import gls_run


class ExploitationExpert(ExpertBase):
    """优化版利用路径生成专家，为分配了利用策略的个体生成局部优化的新路径
    集成路径相似度优化器，提高路径存储和相似度比较效率"""
    
    def __init__(self, similarity_threshold=1.0):
        """初始化优化版利用路径生成专家

        参数:
            similarity_threshold: 相似度阈值，当路径相似度超过此阈值时，跳过局部搜索
        """
        super().__init__()
        
        # 使用优化的路径相似度优化器替代简单的路径存储列表
        self.path_optimizer = PathSimilarityOptimizer(similarity_threshold=similarity_threshold)
        
        # 记录性能统计信息
        self.skipped_searches = 0
        self.total_searches = 0
        self.search_time_saved = 0  # 估计节省的搜索时间（秒）
    
    def check_path_similarity(self, path):
        """检查路径与已存储路径的相似度
        
        参数:
            path: 待检查的路径
            
        返回:
            bool: 如果与任何已存储路径的相似度超过阈值，返回True；否则返回False
        """
        # 使用路径优化器检查相似度
        is_similar, similar_id, similarity = self.path_optimizer.check_similarity(path)
        
        if is_similar:
            self.logger.info(f"发现相似路径，相似度: {similarity:.4f}，相似id：{similar_id},跳过局部搜索")
            self.skipped_searches += 1
            # 估计节省的搜索时间（假设每次搜索平均需要5秒）
            self.search_time_saved += 5
        
        self.total_searches += 1
        
        return is_similar
    
    def calculate_path_similarity(self, path1, path2):
        """计算两条路径之间的相似度
        
        参数:
            path1, path2: 两个路径
        
        返回:
            float: 相似度，范围[0,1]，值越大表示越相似
        """
        # 使用路径优化器计算相似度
        return self.path_optimizer.calculate_similarity(path1, path2)

    def get_performance_stats(self):
        """获取性能统计信息
        
        返回:
            dict: 性能统计信息
        """
        # 获取路径优化器的统计信息
        optimizer_stats = self.path_optimizer.get_statistics()
        
        # 计算跳过率
        skip_rate = self.skipped_searches / max(1, self.total_searches)
        
        # 合并统计信息
        stats = {
            "skipped_searches": self.skipped_searches,
            "total_searches": self.total_searches,
            "skip_rate": skip_rate,
            "estimated_time_saved": self.search_time_saved,
            "path_optimizer": optimizer_stats
        }
        return stats

    def generate_path(self, individual, landscape_report, populations, distance_matrix, individual_index, res_populations=None):
        """生成利用路径，完全使用局部搜索和扰动算法

        参数:
            individual: 当前个体
            landscape_report: 景观分析报告
            populations: 当前种群
            distance_matrix: 距离矩阵
            individual_index: 个体在种群中的索引
            res_populations: 精英解种群，用于存储高质量解
        """
        self.logger.info("开始生成利用路径 - 使用局部搜索和扰动算法")

        # 找出精英解
        elite_solutions = sorted(populations, key=lambda x: x["cur_cost"])[:3]

        # 初始化种群列表（如果未提供）
        if populations is None:
            populations = []
        if res_populations is None:
            res_populations = []

        # 使用局部搜索加扰动策略
        try:
            # 导入优化版的局部搜索和拓扑感知扰动模块
            from gls_evol_enhanced import route2tour, tour_cost, tour2route
            from optimized_topology_aware_perturbation import topology_aware_perturbation
            from time_tracker import time_tracker
            import gls_run
            import numpy as np
            import time

            self.logger.info("使用优化版局部搜索加拓扑感知扰动策略优化路径")

            # 提取精英解的路径列表，用于拓扑感知扰动
            known_solutions = [e["tour"] for e in elite_solutions]
            # 添加已有的精英解到known_solutions
            if res_populations:
                res_tours = [res_indival["tour"] for res_indival in res_populations]
                known_solutions.extend(res_tours)
                # 去重
                known_solutions = list({tuple(sol.tolist()) if isinstance(sol, np.ndarray) else (tuple(sol) if isinstance(sol, list) else sol) for sol in known_solutions})
                known_solutions = [np.array(sol) if isinstance(sol, tuple) else (list(sol) if isinstance(sol, tuple) else sol) for sol in known_solutions]

            # 创建临时进化个体和种群，用于局部搜索过程中更新
            evo_individual = copy.deepcopy(individual)

            # 确保输入的tour是正确的格式
            input_tour = individual["tour"]
            if isinstance(input_tour, list):
                input_tour = np.array(input_tour)

            # 记录开始时间
            start_time = time.time()

            from greedy_path_generator import generate_path
            new_tour, new_cost = generate_path(distance_matrix)

            # 更新种群中对应位置的路径
            populations[individual_index]["tour"] = new_tour
            populations[individual_index]["cur_cost"] = new_cost

            self.logger.info(f"已用贪心路径替换种群中索引 {individual_index} 处的路径，新成本: {new_cost}")

            # 检查路径相似度，如果与已搜索路径相似度高，则跳过局部搜索并生成一条新的贪心路径替换
            if self.check_path_similarity(input_tour):
                self.logger.info("路径与已搜索路径相似度高，跳过局部搜索，生成新的贪心路径替换")

                return {"new_tour": new_tour, "cur_cost": new_cost}

            # 将当前路径添加到路径优化器中
            self.path_optimizer.add_path(input_tour)

            # 执行优化版局部搜索，并传递种群参数
            init_cost = tour_cost(distance_matrix, individual["tour"])
            init_route = np.array(tour2route(input_tour))

            gls_run.solve_instance(distance_matrix,  # 第i个实例距离矩阵
                                    0.5,   # 时间限制
                                    100,      # 最大搜索次数
                                    5,  # 扰动时每次修改次数
                                    init_route,
                                    evo_individual, populations, res_populations)

            costs = [ind["cur_cost"] for ind in res_populations]
            tours = [ind["tour"] for ind in res_populations]
            self.logger.info("res_population_num: %s", len(res_populations))
            self.logger.info("res_population_costs: %s", costs)
            self.logger.info("res_populations: %s", tours)
            self.logger.info("populations_num: %s", len(populations))
            self.logger.info("populations: %s", populations)

            # 记录结束时间
            end_time = time.time()
            search_time = end_time - start_time

            # 更新统计信息
            self.logger.info(f"局部搜索耗时: {search_time:.2f}秒")
            status = self.get_performance_stats()
            self.logger.info(f"路径优化器性能统计: {status}")

            # 返回优化后的路径（从种群中获取最新的路径）
            optimized_individual = populations[individual_index]
            return {"new_tour": optimized_individual["tour"], "cur_cost": optimized_individual["cur_cost"]}

        except Exception as e:
            self.logger.error(f"生成利用路径时出错: {str(e)}")
            # 发生错误时，也生成一条新的贪心路径替换
            try:
                from greedy_path_generator import generate_path
                new_tour, new_cost = generate_path(distance_matrix)

                # 更新种群中对应位置的路径
                populations[individual_index]["tour"] = new_tour
                populations[individual_index]["cur_cost"] = new_cost

                self.logger.info(f"发生错误后已用贪心路径替换种群中索引 {individual_index} 处的路径，新成本: {new_cost}")
                return {"new_tour": new_tour, "cur_cost": new_cost}
            except Exception as e2:
                self.logger.error(f"生成贪心路径时出错: {str(e2)}")
                return {"new_tour": individual["tour"], "cur_cost": individual["cur_cost"]}
    
    def generate_path(self, individual, landscape_report, populations, distance_matrix, individual_index, res_populations=None):
        """生成利用路径，完全使用局部搜索和扰动算法
        
        参数:
            individual: 当前个体
            landscape_report: 景观分析报告
            populations: 当前种群
            distance_matrix: 距离矩阵
            individual_index: 个体在种群中的索引
            res_populations: 精英解种群，用于存储高质量解
        """
        self.logger.info("开始生成利用路径 - 使用局部搜索和扰动算法")
        
        # 找出精英解
        elite_solutions = sorted(populations, key=lambda x: x["cur_cost"])[:3]
        
        # 初始化种群列表（如果未提供）
        if populations is None:
            populations = []
        if res_populations is None:
            res_populations = []
        
        # 使用局部搜索加扰动策略
        try:
            # 导入优化版的局部搜索和拓扑感知扰动模块
            from gls_evol_enhanced import route2tour, tour_cost
            from optimized_topology_aware_perturbation import topology_aware_perturbation
            from time_tracker import time_tracker
            
            self.logger.info("使用优化版局部搜索加拓扑感知扰动策略优化路径")
            
            # 提取精英解的路径列表，用于拓扑感知扰动
            known_solutions = [e["tour"] for e in elite_solutions]
            # 添加已有的精英解到known_solutions
            if res_populations:
                res_tours = [res_indival["tour"] for res_indival in res_populations]
                known_solutions.extend(res_tours)
                # 去重
                known_solutions = list({tuple(sol.tolist()) if isinstance(sol, np.ndarray) else (tuple(sol) if isinstance(sol, list) else sol) for sol in known_solutions})
                known_solutions = [np.array(sol) if isinstance(sol, tuple) else (list(sol) if isinstance(sol, tuple) else sol) for sol in known_solutions]
            
            # 创建临时进化个体和种群，用于局部搜索过程中更新
            evo_individual = copy.deepcopy(individual)
            
            # 确保输入的tour是正确的格式
            input_tour = individual["tour"]
            if isinstance(input_tour, list):
                input_tour = np.array(input_tour)
            
            # 记录开始时间
            start_time = time.time()

            from greedy_path_generator import generate_path
            new_tour, new_cost = generate_path(distance_matrix)
            
            # 更新种群中对应位置的路径
            populations[individual_index]["tour"] = new_tour
            populations[individual_index]["cur_cost"] = new_cost
            
            self.logger.info(f"已用贪心路径替换种群中索引 {individual_index} 处的路径，新成本: {new_cost}")
            
            # 检查路径相似度，如果与已搜索路径相似度高，则跳过局部搜索并生成一条新的贪心路径替换
            if self.check_path_similarity(input_tour):
                self.logger.info("路径与已搜索路径相似度高，跳过局部搜索，生成新的贪心路径替换")
               
                return {"new_tour": new_tour, "cur_cost": new_cost}
            
            # 将当前路径添加到路径优化器中
            self.path_optimizer.add_path(input_tour)
            
            # 执行优化版局部搜索，并传递种群参数
            init_cost = tour_cost(distance_matrix, individual["tour"])
            init_route = np.array(gls_evol_enhanced.tour2route(input_tour))
           
            gls_run.solve_instance(distance_matrix,  # 第i个实例距离矩阵
                                   0.5,   # 时间限制
                                   100,      # 最大搜索次数
                                   5,  # 扰动时每次修改次数
                                   init_route,
                                   evo_individual, populations, res_populations)
            
            costs = [ind["cur_cost"] for ind in res_populations]
            tours = [ind["tour"] for ind in res_populations]
            self.logger.info("res_population_num: %s", len(res_populations))
            self.logger.info("res_population_costs: %s", costs)
            self.logger.info("res_populations: %s", tours)
            self.logger.info("populations_num: %s", len(populations))
            self.logger.info("populations: %s", populations)
            
            # 记录结束时间
            end_time = time.time()
            search_time = end_time - start_time
            
            # 更新统计信息
            self.logger.info(f"局部搜索耗时: {search_time:.2f}秒")
            status = self.get_performance_stats()
            self.logger.info(f"路径优化器性能统计: {status}")

            # 返回优化后的路径（从种群中获取最新的路径）
            optimized_individual = populations[individual_index]
            return {"new_tour": optimized_individual["tour"], "cur_cost": optimized_individual["cur_cost"]}

        except Exception as e:
            self.logger.error(f"生成利用路径时出错: {str(e)}")
            # 发生错误时，也生成一条新的贪心路径替换
            try:
                from greedy_path_generator import generate_path
                new_tour, new_cost = generate_path(distance_matrix)
                
                # 更新种群中对应位置的路径
                populations[individual_index]["tour"] = new_tour
                populations[individual_index]["cur_cost"] = new_cost
                
                self.logger.info(f"发生错误后已用贪心路径替换种群中索引 {individual_index} 处的路径，新成本: {new_cost}")
                return {"new_tour": new_tour, "cur_cost": new_cost}
            except Exception as e2:
                self.logger.error(f"生成贪心路径时出错: {str(e2)}")
                return {"new_tour": individual["tour"], "cur_cost": individual["cur_cost"]}
