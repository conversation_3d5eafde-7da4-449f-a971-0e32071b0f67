from loadinstance import load_all_instances
import pickle as pkl
from initpop import INIT
import random
import json
import re
import numpy as np
import logging
import time
import os
import sys
import copy
from datetime import datetime
import gls_run
from path_similarity_optimizer import PathSimilarityOptimizer
import gls_evol_enhanced
# 导入API接口
from api_general import InterfaceAPI

# 导入工具类
from idea_extractor import IdeaExtractor
from gls_evol_enhanced import route2tour, tour_cost
import utils
from utils import convert_numpy_types

# 导入配置文件
from config import API_CONFIG, ALGORITHM_CONFIG, get_paths
import argparse

# 导入日志配置模块
from log_config import setup_logging, get_instance_log_file, get_logger

# 导入专家提示词模块
from experts_prompt import *

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

# 定义项目根目录
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))

# 获取路径配置
paths = get_paths(project_root)
GLOBAL_INPUT_PATH = paths["input_path"]
GLOBAL_OUTPUT_PATH = paths["output_path"]

# 创建主模块的日志记录器
main_logger = get_logger(__name__)


class ExpertBase:
    """专家基类，所有专家模块继承自此类"""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def analyze(self, *args, **kwargs):
        """专家分析方法，由子类实现"""
        raise NotImplementedError
    
    def generate_report(self, analysis_result):
        """生成分析报告，由子类实现"""
        raise NotImplementedError


class StatsExpert(ExpertBase):
    """统计分析专家，负责种群的统计学特征分析"""
    
    def __init__(self):
        super().__init__()
    
    def analyze(self, populations, distance_matrix=None):
        """分析种群的统计学特征"""
        self.logger.info("开始统计分析")
        
        # 计算成本统计
        costs = [p["cur_cost"] for p in populations]
        cost_stats = {
            "min": min(costs),
            "max": max(costs),
            "mean": sum(costs) / len(costs),
            "std": np.std(costs)
        }
        
        # 计算种群多样性
        diversity = utils.calculate_population_diversity(populations)
        
        # 聚类分析
        from stats_analyzer import PopulationAnalyzer
        clusters = PopulationAnalyzer.analyze_solution_clusters(populations)
        
        # 收敛度分析
        convergence = PopulationAnalyzer.common_edge_analysis(populations)
        
        analysis_result = {
            "population_size": len(populations),
            "cost_stats": cost_stats,
            "diversity": diversity,
            "clusters": clusters,
            "convergence": convergence
        }
        
        self.logger.info(f"统计分析完成: {analysis_result}")
        return analysis_result
    
    def generate_report(self, analysis_result, coordinates=None, distance_matrix=None):
        """生成统计分析报告
        新增 coordinates 和 distance_matrix 字段，供后续空间统计计算使用。"""
        report = {
            "population_size": analysis_result.get("population_size", 0),
            "cost_stats": analysis_result.get("cost_stats", {}),
            "diversity_level": analysis_result.get("diversity", 0),
            "convergence_level": analysis_result.get("convergence", 0),
            "clustering_info": analysis_result.get("clusters", {}),
            # 新增空间相关字段
            "coordinates": coordinates,
            "distance_matrix": distance_matrix
        }
        return report


class PathExpert(ExpertBase):
    """路径结构专家，分析路径结构特征"""
    
    def __init__(self):
        super().__init__()
    
    def analyze(self, populations, distance_matrix):
        """分析路径结构特征"""
        self.logger.info("开始路径结构分析")
        
        # 验证输入数据
        if not populations:
            self.logger.warning("种群为空，无法进行路径结构分析")
            return {
                "high_quality_edges": [],
                "common_subpaths": [],
                "edge_frequency": {},
                "low_quality_regions": []
            }
        
        if distance_matrix is None or not hasattr(distance_matrix, 'shape'):
            self.logger.warning("距离矩阵无效，无法进行路径结构分析")
            return {
                "high_quality_edges": [],
                "common_subpaths": [],
                "edge_frequency": {},
                "low_quality_regions": []
            }
        
        # 验证tour中的索引是否有效
        matrix_size = distance_matrix.shape[0]
        valid_populations = []
        
        for individual in populations:
            tour = individual.get('tour', [])
            # 处理NumPy数组
            try:
                if isinstance(tour, np.ndarray):
                    if tour.size == 0:
                        continue
                else:
                    if not tour:
                        continue
            except ImportError:
                # 如果没有NumPy，使用标准检查
                if not tour:
                    continue
                
            # 检查tour中的索引是否有效
            invalid_indices = [idx for idx in tour if idx >= matrix_size]
            if invalid_indices:
                self.logger.warning(f"发现无效的城市索引: {invalid_indices}, 距离矩阵大小: {matrix_size}，跳过此路径")
                # 创建一个有效的tour副本，将无效索引替换为有效值
                valid_tour = [idx if idx < matrix_size else idx % matrix_size for idx in tour]
                individual_copy = individual.copy()
                individual_copy['tour'] = valid_tour
                valid_populations.append(individual_copy)
            else:
                valid_populations.append(individual)
        
        if not valid_populations:
            self.logger.warning("没有有效的路径可供分析")
            return {
                "high_quality_edges": [],
                "common_subpaths": [],
                "edge_frequency": {},
                "low_quality_regions": []
            }
        
        # 导入路径结构分析器
        from path_structure_analyzer import PathStructureAnalyzer
        
        try:
            # 分析高质量边
            high_quality_edges = PathStructureAnalyzer.identify_high_quality_edges(valid_populations, distance_matrix)
            
            # 分析公共子路径
            common_subpaths = PathStructureAnalyzer.identify_common_subpaths(valid_populations)
            
            # 边频率分析
            edge_frequency = PathStructureAnalyzer.analyze_edge_frequency(valid_populations)
            
            # 低质量区域识别
            low_quality_regions = PathStructureAnalyzer.identify_low_quality_regions(valid_populations, distance_matrix)
            
            analysis_result = {
                "high_quality_edges": high_quality_edges,
                "common_subpaths": common_subpaths,
                "edge_frequency": edge_frequency,
                "low_quality_regions": low_quality_regions
            }
        except Exception as e:
            self.logger.error(f"路径结构分析出错: {str(e)}")
            analysis_result = {
                "high_quality_edges": [],
                "common_subpaths": [],
                "edge_frequency": {},
                "low_quality_regions": []
            }
        
        self.logger.info("路径结构分析完成")
        return analysis_result
    
    def generate_report(self, analysis_result):
        """生成路径结构分析报告"""
        report = {
            "high_quality_edges": analysis_result.get("high_quality_edges", []),
            "common_subpaths": analysis_result.get("common_subpaths", []),
            "edge_frequency": analysis_result.get("edge_frequency", {}),
            "low_quality_regions": analysis_result.get("low_quality_regions", [])
        }
        return report


class EliteExpert(ExpertBase):
    """精英解分析专家，分析已发现的精英解"""
    
    def __init__(self):
        super().__init__()
    
    def analyze(self, elite_solutions, populations, distance_matrix):
        """分析精英解特征"""
        self.logger.info("开始精英解分析")
        
        # 如果没有精英解，返回空结果
        # 处理NumPy数组
        try:
            if isinstance(elite_solutions, np.ndarray):
                # 对NumPy数组使用size属性检查是否为空
                if elite_solutions.size == 0:
                    self.logger.warning("没有精英解可供分析")
                    return {}
            else:
                # 标准列表处理
                if not elite_solutions:
                    self.logger.warning("没有精英解可供分析")
                    return {}
        except ImportError:
            # 如果没有NumPy，使用标准处理
            if not elite_solutions:
                self.logger.warning("没有精英解可供分析")
                return {}
        
        # 从stats_analyzer导入精英解分析方法
        from stats_analyzer import PopulationAnalyzer
        
        # 精英解共有特征分析
        common_features = PopulationAnalyzer.analyze_common_features(elite_solutions)
        
        # 精英解与当前种群的差异分析
        population_gap = PopulationAnalyzer.analyze_population_gap(elite_solutions, populations)
        
        # 固定位置节点识别
        fixed_nodes = PopulationAnalyzer.identify_fixed_nodes(elite_solutions)
        
        # 精英解多样性分析
        elite_diversity = PopulationAnalyzer.analyze_elite_diversity(elite_solutions)
        
        analysis_result = {
            "elite_count": len(elite_solutions),
            "common_features": common_features,
            "population_gap": population_gap,
            "fixed_nodes": fixed_nodes,
            "elite_diversity": elite_diversity
        }
        
        self.logger.info("精英解分析完成")
        return analysis_result
    
    def generate_report(self, analysis_result):
        """生成精英解分析报告"""
        report = {
            "elite_count": analysis_result.get("elite_count", 0),
            "elite_common_features": analysis_result.get("common_features", {}),
            "fixed_nodes": analysis_result.get("fixed_nodes", []),
            "population_gap": analysis_result.get("population_gap", {}),
            "elite_diversity": analysis_result.get("elite_diversity", {})
        }
        return report


class LandscapeExpert(ExpertBase):
    """景观分析专家，整合其他专家的分析结果"""
    
    def __init__(self, interface_llm):
        super().__init__()
        self.interface_llm = interface_llm
        # Use the optimized landscape prompt template from experts_prompt.py
        from experts_prompt import LANDSCAPE_PROMPT
        self.prompt_template = LANDSCAPE_PROMPT
    

    
    def analyze(self, stats_report, path_report, elite_report, iteration=0, total_iterations=10, history_data=None):
        """整合分析结果，生成综合景观分析"""
        self.logger.info("开始景观分析")
        
        # Import the optimized function from experts_prompt.py
        from experts_prompt import generate_landscape_expert_prompt
        
        # Generate the optimized prompt
        prompt = generate_landscape_expert_prompt(
            stats_report=stats_report, 
            path_report=path_report, 
            elite_report=elite_report,
            iteration=iteration,
            total_iterations=total_iterations,
            history_data=history_data
        )
        
        # 调用LLM获取分析结果
        self.logger.info("调用LLM进行景观分析")
        self.logger.info(f"发送给LLM的提示词: {prompt}")
        landscape_analysis_text = self.interface_llm.get_response(prompt)
        self.logger.info(f"LLM返回的分析结果: {landscape_analysis_text}")
        
        # 解析LLM返回的文本为JSON对象
        from experts_prompt import parse_expert_response
        landscape_analysis = parse_expert_response(landscape_analysis_text)
        
        # 如果解析失败，创建一个基本的结构化数据
        if "error" in landscape_analysis:
            self.logger.warning(f"解析景观分析结果失败: {landscape_analysis['error']}")
            # 创建一个基本结构，包含原始文本
            landscape_analysis = {
                "search_space_features": {"ruggedness": 0.5, "modality": "unknown", "deceptiveness": "unknown"},
                "population_state": {"diversity": 0.5, "convergence": 0.5, "clustering": "unknown"},
                "difficult_regions": [],
                "opportunity_regions": [],
                "evolution_phase": "unknown",
                "evolution_direction": {"recommended_focus": "balance", "operators": []},
                "raw_text": landscape_analysis_text  # 保留原始文本以备后用
            }
        
        self.logger.info("=====景观分析完成====")
        return landscape_analysis
    
    def generate_report(self, analysis_result):
        """直接返回LLM生成的分析结果"""
        return analysis_result


class StrategyExpert(ExpertBase):
    """策略选择专家，为每个个体分配最适合的策略"""
    
    def __init__(self, interface_llm):
        super().__init__()
        self.interface_llm = interface_llm
        # Use the optimized strategy prompt template from experts_prompt.py
        from experts_prompt import STRATEGY_PROMPT
        self.prompt_template = STRATEGY_PROMPT
    
    def analyze(self, landscape_report, populations, iteration, strategy_feedback=None):
        """基于景观分析，为每个个体分配策略"""
        self.logger.info("开始策略分配分析")
        
        # Import the optimized function from experts_prompt.py
        from experts_prompt import generate_strategy_expert_prompt
        
        # Generate the optimized prompt
        prompt = generate_strategy_expert_prompt(
            landscape_report=landscape_report,
            population=populations,
            previous_feedback=strategy_feedback,
            iteration=iteration
        )
        
        # 调用LLM获取策略分配结果
        self.logger.info(f"发送给LLM的策略分配提示词: {prompt}")
        self.logger.info("调用LLM进行策略分配")
        strategy_response = self.interface_llm.get_response(prompt)
        self.logger.info(f"LLM返回的策略分配结果: {strategy_response}")
        
        # 解析策略分配结果
        try:
            # Import the response parsing function
            from experts_prompt import parse_expert_response
            
            # Parse the response
            strategy_data = parse_expert_response(strategy_response)
            
            if "error" in strategy_data:
                self.logger.warning(f"解析策略分配结果时出错: {strategy_data['error']}")
                # 使用默认策略
                strategy_selection = ["explore" if i % 2 == 0 else "exploit" for i in range(len(populations))]
            else:
                individual_assignments = strategy_data.get("individual_assignments", {})
                
                # 转换为策略列表
                strategy_selection = []
                for i in range(len(populations)):
                    strategy = individual_assignments.get(str(i), "explore")
                    strategy_selection.append(strategy)
        except Exception as e:
            self.logger.error(f"解析策略分配结果时出错: {str(e)}")
            # 使用默认策略
            strategy_selection = ["explore" if i % 2 == 0 else "exploit" for i in range(len(populations))]
        
        self.logger.info(f"策略分配完成: {strategy_selection}")
        return strategy_selection, strategy_response
    
    def generate_report(self, analysis_result):
        """生成策略分配报告"""
        strategy_selection, strategy_response = analysis_result
        return {
            "strategy_selection": strategy_selection,
            "strategy_response": strategy_response
        }


class ExplorationExpert(ExpertBase):
    """探索路径生成专家，为分配了探索策略的个体生成多样化的新路径
    使用纯算法实现，不依赖LLM"""

    def __init__(self, interface_llm=None):
        super().__init__()
        # 保留接口参数以保持兼容性，但不使用
        self.interface_llm = interface_llm

        # 探索路径生成的参数配置
        self.diversity_weight = 0.7  # 多样性权重
        self.quality_weight = 0.3    # 质量权重
        self.exploration_radius = 0.3  # 探索半径（相对于问题规模）
        self.knowledge_base = None  # 暂不实现知识库
        
    def calculate_path_cost(self, tour, distance_matrix):
        """计算路径的总成本
        
        参数:
            tour (list): 旅行路径
            distance_matrix (numpy.ndarray): 距离矩阵
            
        返回:
            float: 路径总成本
        """
        self.logger.info("计算路径成本")
        return tour_cost(distance_matrix, tour)
    
    def generate_path(self, individual, landscape_report, populations, distance_matrix, individual_index, evo_populations=None, res_populations=None):
        """生成探索路径，使用纯算法实现，不依赖LLM

        参数:
            individual: 当前个体
            landscape_report: 景观分析报告（保留接口兼容性）
            populations: 当前种群
            distance_matrix: 距离矩阵
            individual_index: 个体在种群中的索引
            evo_populations: 进化种群，用于存储生成的路径
            res_populations: 精英解种群，用于存储高质量解
        """
        self.logger.info(f"开始为个体 {individual_index} 生成探索路径（算法实现）")

        # 初始化种群列表（如果未提供）
        if populations is None:
            populations = []
        if res_populations is None:
            res_populations = []

        try:
            # 使用算法生成多样化探索路径
            new_tour = self.generate_diverse_exploration_path(
                current_individual=individual,
                populations=populations,
                distance_matrix=distance_matrix,
                individual_index=individual_index
            )

            # 计算新路径的成本
            new_cost = self.calculate_path_cost(new_tour, distance_matrix)

            # 创建探索路径结果
            exploration_path = {
                "new_tour": new_tour,
                "cur_cost": new_cost
            }

            # 创建进化个体的副本
            evo_individual = copy.deepcopy(individual)
            evo_individual["tour"] = new_tour
            evo_individual["cur_cost"] = new_cost

            # 更新种群中对应位置的路径
            populations[individual_index]["tour"] = evo_individual["tour"]
            populations[individual_index]["cur_cost"] = evo_individual["cur_cost"]

            self.logger.info(f"探索路径生成完成，成本: {new_cost}, 路径长度: {len(new_tour)}")
            return exploration_path

        except Exception as e:
            self.logger.error(f"生成探索路径时发生错误: {e}")
            # 使用备选方案：随机扰动
            backup_tour = self.generate_random_exploration_path(individual["tour"], distance_matrix)
            backup_cost = self.calculate_path_cost(backup_tour, distance_matrix)

            backup_path = {
                "new_tour": backup_tour,
                "cur_cost": backup_cost
            }

            # 更新种群
            populations[individual_index]["tour"] = backup_tour
            populations[individual_index]["cur_cost"] = backup_cost

            self.logger.info(f"使用备选探索路径，成本: {backup_cost}")
            return backup_path

    def generate_diverse_exploration_path(self, current_individual, populations, distance_matrix, individual_index):
        """生成多样化的探索路径，基于多样性原则和区域覆盖

        参数:
            current_individual: 当前个体
            populations: 当前种群
            distance_matrix: 距离矩阵
            individual_index: 个体索引

        返回:
            新的探索路径
        """
        self.logger.info("开始生成多样化探索路径")

        # 获取问题规模
        n_cities = len(current_individual["tour"])

        # 分析当前种群的覆盖情况
        coverage_info = self.analyze_population_coverage(populations, distance_matrix)

        # 选择探索策略
        strategy = self.select_exploration_strategy(current_individual, populations, coverage_info)

        if strategy == "diversification":
            new_path = self.generate_diversification_path(current_individual["tour"], populations, distance_matrix)
        elif strategy == "region_exploration":
            new_path = self.generate_region_exploration_path(current_individual["tour"], coverage_info, distance_matrix)
        elif strategy == "hybrid_construction":
            new_path = self.generate_hybrid_construction_path(current_individual["tour"], populations, distance_matrix)
        else:  # random_restart
            new_path = self.generate_random_restart_path(n_cities, distance_matrix)

        # 验证和修复路径
        new_path = self.validate_and_repair_path(new_path, n_cities)

        self.logger.info(f"使用策略 {strategy} 生成探索路径，长度: {len(new_path)}")
        return new_path

    def analyze_population_coverage(self, populations, distance_matrix):
        """分析当前种群的区域覆盖情况

        参数:
            populations: 当前种群
            distance_matrix: 距离矩阵

        返回:
            覆盖信息字典
        """
        n_cities = distance_matrix.shape[0]

        # 统计边的使用频率
        edge_usage = {}
        for individual in populations:
            tour = individual["tour"]
            for i in range(len(tour)):
                edge = tuple(sorted([tour[i], tour[(i + 1) % len(tour)]]))
                edge_usage[edge] = edge_usage.get(edge, 0) + 1

        # 识别高频边和低频边
        total_edges = len(edge_usage)
        avg_usage = sum(edge_usage.values()) / total_edges if total_edges > 0 else 0

        high_freq_edges = {edge: count for edge, count in edge_usage.items() if count > avg_usage * 1.5}
        low_freq_edges = {edge: count for edge, count in edge_usage.items() if count < avg_usage * 0.5}

        # 识别未探索的区域（很少使用的城市对）
        unexplored_regions = []
        for i in range(n_cities):
            for j in range(i + 1, n_cities):
                edge = (i, j)
                if edge not in edge_usage or edge_usage[edge] <= 1:
                    unexplored_regions.append(edge)

        return {
            "edge_usage": edge_usage,
            "high_freq_edges": high_freq_edges,
            "low_freq_edges": low_freq_edges,
            "unexplored_regions": unexplored_regions[:min(20, len(unexplored_regions))],  # 限制数量
            "avg_usage": avg_usage
        }

    def select_exploration_strategy(self, current_individual, populations, coverage_info):
        """选择探索策略

        参数:
            current_individual: 当前个体
            populations: 当前种群
            coverage_info: 覆盖信息

        返回:
            选择的策略名称
        """
        import random

        # 计算种群多样性
        diversity = self.calculate_population_diversity(populations)

        # 根据多样性和覆盖情况选择策略
        if diversity < 0.3:  # 低多样性，需要大幅度探索
            strategies = ["diversification", "random_restart", "region_exploration"]
            weights = [0.4, 0.4, 0.2]
        elif len(coverage_info["unexplored_regions"]) > 10:  # 有很多未探索区域
            strategies = ["region_exploration", "hybrid_construction", "diversification"]
            weights = [0.5, 0.3, 0.2]
        else:  # 平衡探索
            strategies = ["hybrid_construction", "diversification", "region_exploration"]
            weights = [0.4, 0.3, 0.3]

        return random.choices(strategies, weights=weights, k=1)[0]

    def calculate_population_diversity(self, populations):
        """计算种群多样性

        参数:
            populations: 当前种群

        返回:
            多样性值 (0-1)
        """
        if len(populations) < 2:
            return 1.0

        # 计算路径间的平均汉明距离
        total_distance = 0
        comparisons = 0

        for i in range(len(populations)):
            for j in range(i + 1, len(populations)):
                tour1 = populations[i]["tour"]
                tour2 = populations[j]["tour"]

                # 计算汉明距离（不同位置的数量）
                distance = sum(1 for a, b in zip(tour1, tour2) if a != b)
                total_distance += distance
                comparisons += 1

        if comparisons == 0:
            return 1.0

        avg_distance = total_distance / comparisons
        max_possible_distance = len(populations[0]["tour"])

        return min(1.0, avg_distance / max_possible_distance)

    def generate_diversification_path(self, current_tour, populations, distance_matrix):
        """生成多样化路径，最大化与现有种群的差异

        参数:
            current_tour: 当前路径
            populations: 当前种群
            distance_matrix: 距离矩阵

        返回:
            新的多样化路径
        """
        import random
        n_cities = len(current_tour)

        # 统计种群中每个位置上城市的出现频率
        position_freq = {}
        for individual in populations:
            tour = individual["tour"]
            for pos, city in enumerate(tour):
                if pos not in position_freq:
                    position_freq[pos] = {}
                position_freq[pos][city] = position_freq[pos].get(city, 0) + 1

        # 构建新路径，优先选择在每个位置上出现频率较低的城市
        new_tour = []
        used_cities = set()

        for pos in range(n_cities):
            if pos in position_freq:
                # 获取该位置上城市的频率，选择频率最低的未使用城市
                city_freq = position_freq[pos]
                available_cities = [(city, freq) for city, freq in city_freq.items() if city not in used_cities]

                if available_cities:
                    # 按频率排序，选择频率较低的城市
                    available_cities.sort(key=lambda x: x[1])
                    # 从频率最低的前30%中随机选择
                    top_candidates = available_cities[:max(1, len(available_cities) // 3)]
                    selected_city = random.choice(top_candidates)[0]
                    new_tour.append(selected_city)
                    used_cities.add(selected_city)
                    continue

            # 如果没有频率信息或所有城市都已使用，随机选择未使用的城市
            available_cities = [city for city in range(n_cities) if city not in used_cities]
            if available_cities:
                selected_city = random.choice(available_cities)
                new_tour.append(selected_city)
                used_cities.add(selected_city)

        return new_tour

    def generate_region_exploration_path(self, current_tour, coverage_info, distance_matrix):
        """生成区域探索路径，重点探索未充分覆盖的区域

        参数:
            current_tour: 当前路径
            coverage_info: 覆盖信息
            distance_matrix: 距离矩阵

        返回:
            新的区域探索路径
        """
        import random
        n_cities = len(current_tour)

        # 获取未探索的区域
        unexplored_regions = coverage_info["unexplored_regions"]

        if not unexplored_regions:
            # 如果没有未探索区域，使用随机重启
            return self.generate_random_restart_path(n_cities, distance_matrix)

        # 选择几个未探索的区域作为构建路径的核心
        num_core_regions = min(3, len(unexplored_regions))
        core_regions = random.sample(unexplored_regions, num_core_regions)

        # 从核心区域开始构建路径
        new_tour = []
        used_cities = set()

        # 添加核心区域的城市
        for region in core_regions:
            for city in region:
                if city not in used_cities:
                    new_tour.append(city)
                    used_cities.add(city)

        # 使用最近邻启发式填充剩余城市
        while len(new_tour) < n_cities:
            if not new_tour:
                # 如果路径为空，随机选择一个起始城市
                start_city = random.randint(0, n_cities - 1)
                new_tour.append(start_city)
                used_cities.add(start_city)
                continue

            current_city = new_tour[-1]
            available_cities = [city for city in range(n_cities) if city not in used_cities]

            if not available_cities:
                break

            # 选择距离当前城市最近的未访问城市
            nearest_city = min(available_cities, key=lambda city: distance_matrix[current_city][city])
            new_tour.append(nearest_city)
            used_cities.add(nearest_city)

        return new_tour

    def generate_hybrid_construction_path(self, current_tour, populations, distance_matrix):
        """生成混合构建路径，结合贪心和多样性策略

        参数:
            current_tour: 当前路径
            populations: 当前种群
            distance_matrix: 距离矩阵

        返回:
            新的混合构建路径
        """
        import random
        n_cities = len(current_tour)

        # 随机选择起始城市
        start_city = random.randint(0, n_cities - 1)
        new_tour = [start_city]
        used_cities = {start_city}

        while len(new_tour) < n_cities:
            current_city = new_tour[-1]
            available_cities = [city for city in range(n_cities) if city not in used_cities]

            if not available_cities:
                break

            # 计算每个可用城市的评分（距离 + 多样性）
            city_scores = []
            for city in available_cities:
                # 距离分数（越近越好）
                distance_score = distance_matrix[current_city][city]

                # 多样性分数（与种群中路径的差异）
                diversity_score = 0
                for individual in populations:
                    tour = individual["tour"]
                    if len(tour) > len(new_tour):
                        # 检查在相同位置上是否选择了不同的城市
                        if tour[len(new_tour)] != city:
                            diversity_score += 1

                # 综合评分（距离权重 + 多样性权重）
                total_score = self.quality_weight * distance_score - self.diversity_weight * diversity_score
                city_scores.append((city, total_score))

            # 选择评分最好的城市（考虑一定的随机性）
            city_scores.sort(key=lambda x: x[1])
            # 从最好的前50%中随机选择
            top_candidates = city_scores[:max(1, len(city_scores) // 2)]
            selected_city = random.choice(top_candidates)[0]

            new_tour.append(selected_city)
            used_cities.add(selected_city)

        return new_tour

    def generate_random_restart_path(self, n_cities, distance_matrix):
        """生成随机重启路径

        参数:
            n_cities: 城市数量
            distance_matrix: 距离矩阵

        返回:
            新的随机路径
        """
        import random

        # 创建城市列表并随机打乱
        cities = list(range(n_cities))
        random.shuffle(cities)

        return cities

    def generate_random_exploration_path(self, current_tour, distance_matrix):
        """生成随机探索路径（备选方案）

        参数:
            current_tour: 当前路径
            distance_matrix: 距离矩阵

        返回:
            新的随机探索路径
        """
        import random

        # 复制当前路径
        new_tour = current_tour.copy()

        # 执行多次随机交换
        n_swaps = max(2, len(new_tour) // 4)
        for _ in range(n_swaps):
            i, j = random.sample(range(len(new_tour)), 2)
            new_tour[i], new_tour[j] = new_tour[j], new_tour[i]

        return new_tour

    def validate_and_repair_path(self, path, n_cities):
        """验证和修复路径，确保满足TSP约束

        参数:
            path: 待验证的路径
            n_cities: 城市数量

        返回:
            修复后的有效路径
        """
        import random

        if not path:
            # 如果路径为空，创建随机路径
            cities = list(range(n_cities))
            random.shuffle(cities)
            return cities

        # 确保所有城市都在有效范围内
        valid_path = []
        for city in path:
            if isinstance(city, (int, float)) and 0 <= city < n_cities:
                valid_path.append(int(city))

        # 移除重复城市，保留第一次出现的
        seen = set()
        unique_path = []
        for city in valid_path:
            if city not in seen:
                unique_path.append(city)
                seen.add(city)

        # 添加缺失的城市
        missing_cities = [city for city in range(n_cities) if city not in seen]
        random.shuffle(missing_cities)
        unique_path.extend(missing_cities)

        # 确保路径长度正确
        if len(unique_path) > n_cities:
            unique_path = unique_path[:n_cities]
        elif len(unique_path) < n_cities:
            # 如果仍然缺少城市，用随机城市填充
            while len(unique_path) < n_cities:
                unique_path.append(random.randint(0, n_cities - 1))

        return unique_path

    def _update_populations(self, evo_individual, populations, res_populations):
        """更新进化种群和精英解种群
        
        参数:
            evo_individual: 当前进化个体
            populations: 进化种群
            res_populations: 精英解种群
        """
        # 使用gls_evol_enhanced中的函数
        from gls_evol_enhanced import share_distance_o2a, is_tsplib_instance, calculate_path_similarity
        
        tour = evo_individual["tour"]
        cur_cost = evo_individual["cur_cost"]
        
        # 提取种群中的路径
        tours = [indival["tour"] for indival in populations]
        in_tours_flag, index = share_distance_o2a(tour, tours)
        
        # 提取精英解种群中的路径
        res_tours = [res_indival["tour"] for res_indival in res_populations]
        in_res_tours_flag, index_res = share_distance_o2a(tour, res_tours)
        
        # 获取当前最佳成本
        if res_populations:
            best_res_cost = min(res_populations, key=lambda x: x["cur_cost"])["cur_cost"]
        else:
            best_res_cost = float('inf')
        
        # 检查是否为TSPLIB实例
        is_tsplib = False
        if "func_name" in evo_individual and is_tsplib_instance(evo_individual["func_name"]):
            is_tsplib = True
        
        # 设置相似度阈值和成本比例参数
        similarity_threshold = 0.7  # 相似度阈值，可根据需要调整
        cost_ratio = 0.05  # 成本比例，即x值，可根据需要调整
        
        # 更新种群
        if not in_tours_flag:  # 如果路径不在进化种群中
            if is_tsplib:  # 对TSPLIB实例使用新的加入条件
                # 检查是否满足成本条件：当前成本小于等于(1+x)*最佳成本
                cost_condition = cur_cost <= best_res_cost * (1 + cost_ratio) if res_populations else True
                
                # 检查是否满足相似度条件：与所有精英解的相似度都小于等于y
                similarity_condition = True
                for res_tour in res_tours:
                    similarity = calculate_path_similarity(tour, res_tour)
                    if similarity > similarity_threshold:
                        similarity_condition = False
                        break
                
                # 同时满足成本条件和相似度条件时加入精英解
                if cost_condition and similarity_condition and not in_res_tours_flag:
                    res_populations.append(copy.deepcopy(evo_individual))
                    self.logger.info(f"添加新路径到精英解种群(TSPLIB实例)，成本: {cur_cost}，成本比: {cur_cost/best_res_cost if best_res_cost > 0 else 'N/A'}")
                else:
                    # 否则添加到进化种群
                    populations.append(copy.deepcopy(evo_individual))
                    self.logger.info(f"添加新路径到进化种群，成本: {cur_cost}")
            else:  # 非TSPLIB实例使用原有逻辑
                # 如果成本低于精英解且不在精英解中，添加到精英解
                if (cur_cost <= best_res_cost) and not in_res_tours_flag:
                    res_populations.append(copy.deepcopy(evo_individual))
                    self.logger.info(f"添加新路径到精英解种群，成本: {cur_cost}")
                else:
                    # 否则添加到进化种群
                    populations.append(copy.deepcopy(evo_individual))
                    self.logger.info(f"添加新路径到进化种群，成本: {cur_cost}")
        else:  # 如果路径已在进化种群中
            if is_tsplib:  # 对TSPLIB实例使用新的加入条件
                if not in_res_tours_flag:
                    # 检查是否满足成本条件
                    cost_condition = cur_cost <= best_res_cost * (1 + cost_ratio) if res_populations else True
                    
                    # 检查是否满足相似度条件
                    similarity_condition = True
                    for res_tour in res_tours:
                        similarity = calculate_path_similarity(tour, res_tour)
                        if similarity > similarity_threshold:
                            similarity_condition = False
                            break
                    
                    # 同时满足成本条件和相似度条件时加入精英解
                    if cost_condition and similarity_condition:
                        res_populations.append(copy.deepcopy(populations[index]))
                        self.logger.info(f"将进化种群中的路径移动到精英解种群(TSPLIB实例)，成本: {cur_cost}，成本比: {cur_cost/best_res_cost if best_res_cost > 0 else 'N/A'}")
                # 如果已在精英解中，从进化种群中移除
                elif in_res_tours_flag:
                    populations.pop(index)
                    self.logger.info("从进化种群中移除已存在于精英解的路径")
            else:  # 非TSPLIB实例使用原有逻辑
                # 检查是否应该移动到精英解
                if (cur_cost <= best_res_cost) and not in_res_tours_flag:
                    res_populations.append(copy.deepcopy(populations[index]))
                    self.logger.info(f"将进化种群中的路径移动到精英解种群，成本: {cur_cost}")
                # 如果已在精英解中，从进化种群中移除
                elif in_res_tours_flag:
                    populations.pop(index)
                    self.logger.info("从进化种群中移除已存在于精英解的路径")
        
        self.logger.info(f"当前进化种群大小: {len(populations)}, 精英解种群大小: {len(res_populations)}")



class ExploitationExpert(ExpertBase):
    """优化版利用路径生成专家，为分配了利用策略的个体生成局部优化的新路径
    集成路径相似度优化器，提高路径存储和相似度比较效率"""
    
    def __init__(self, interface_llm, similarity_threshold=1.0):
        """初始化优化版利用路径生成专家
        
        参数:
            interface_llm: LLM接口
            similarity_threshold: 相似度阈值，当路径相似度超过此阈值时，跳过局部搜索
        """
        super().__init__()
        self.interface_llm = interface_llm  # 保留接口但不使用
        
        # 使用优化的路径相似度优化器替代简单的路径存储列表
        self.path_optimizer = PathSimilarityOptimizer(similarity_threshold=similarity_threshold)
        
        # 记录性能统计信息
        self.skipped_searches = 0
        self.total_searches = 0
        self.search_time_saved = 0  # 估计节省的搜索时间（秒）
    
    def check_path_similarity(self, path):
        """检查路径与已存储路径的相似度
        
        参数:
            path: 待检查的路径
            
        返回:
            bool: 如果与任何已存储路径的相似度超过阈值，返回True；否则返回False
        """
        # 使用路径优化器检查相似度
        is_similar, similar_id, similarity = self.path_optimizer.check_similarity(path)
        
        if is_similar:
            self.logger.info(f"发现相似路径，相似度: {similarity:.4f}，相似id：{similar_id},跳过局部搜索")
            self.skipped_searches += 1
            # 估计节省的搜索时间（假设每次搜索平均需要5秒）
            self.search_time_saved += 5
        
        self.total_searches += 1
        
        return is_similar
    
    def calculate_path_similarity(self, path1, path2):
        """计算两条路径之间的相似度
        
        参数:
            path1, path2: 两个路径
        
        返回:
            float: 相似度，范围[0,1]，值越大表示越相似
        """
        # 使用路径优化器计算相似度
        return self.path_optimizer.calculate_similarity(path1, path2)

    def get_performance_stats(self):
        """获取性能统计信息
        
        返回:
            dict: 性能统计信息
        """
        # 获取路径优化器的统计信息
        optimizer_stats = self.path_optimizer.get_statistics()
        
        # 计算跳过率
        skip_rate = self.skipped_searches / max(1, self.total_searches)
        
        # 合并统计信息
        stats = {
            "skipped_searches": self.skipped_searches,
            "total_searches": self.total_searches,
            "skip_rate": skip_rate,
            "estimated_time_saved": self.search_time_saved,
            "path_optimizer": optimizer_stats
        }
        return stats
    
    def generate_path(self, individual, landscape_report, populations, distance_matrix,individual_index, res_populations=None):
        """生成利用路径，完全使用局部搜索和扰动算法
        
        参数:
            individual: 当前个体
            landscape_report: 景观分析报告
            populations: 当前种群
            distance_matrix: 距离矩阵
            populations: 进化种群，用于存储生成的路径
            res_populations: 精英解种群，用于存储高质量解
        """
        self.logger.info("开始生成利用路径 - 使用局部搜索和扰动算法")
        
        # 找出精英解
        elite_solutions = sorted(populations, key=lambda x: x["cur_cost"])[:3]
        
        # 初始化种群列表（如果未提供）
        if populations is None:
            populations = []
        if res_populations is None:
            res_populations = []
        
        # 使用局部搜索加扰动策略
        try:
            # 导入优化版的局部搜索和拓扑感知扰动模块
            from gls_evol_enhanced import route2tour, tour_cost
            from optimized_topology_aware_perturbation import topology_aware_perturbation
            from time_tracker import time_tracker
            
            self.logger.info("使用优化版局部搜索加拓扑感知扰动策略优化路径")
            
            # 提取精英解的路径列表，用于拓扑感知扰动
            known_solutions = [e["tour"] for e in elite_solutions]
            # 添加已有的精英解到known_solutions
            if res_populations:
                res_tours = [res_indival["tour"] for res_indival in res_populations]
                known_solutions.extend(res_tours)
                # 去重
                known_solutions = list({tuple(sol.tolist()) if isinstance(sol, np.ndarray) else (tuple(sol) if isinstance(sol, list) else sol) for sol in known_solutions})
                known_solutions = [np.array(sol) if isinstance(sol, tuple) else (list(sol) if isinstance(sol, tuple) else sol) for sol in known_solutions]
            
            # 创建临时进化个体和种群，用于局部搜索过程中更新
            evo_individual = copy.deepcopy(individual)
            
            # 确保输入的tour是正确的格式
            input_tour = individual["tour"]
            if isinstance(input_tour, list):
                input_tour = np.array(input_tour)
            
            # 记录开始时间
            start_time = time.time()

            from greedy_path_generator import generate_path
            new_tour, new_cost = generate_path(distance_matrix)
            
            # 更新种群中对应位置的路径
            populations[individual_index]["tour"] = new_tour
            populations[individual_index]["cur_cost"] = new_cost
            
            self.logger.info(f"已用贪心路径替换种群中索引 {individual_index} 处的路径，新成本: {new_cost}")
            
            # 检查路径相似度，如果与已搜索路径相似度高，则跳过局部搜索并生成一条新的贪心路径替换
            if self.check_path_similarity(input_tour):
                self.logger.info("路径与已搜索路径相似度高，跳过局部搜索，生成新的贪心路径替换")
               
                return {"new_tour": new_tour, "cur_cost": new_cost}
            
            # 将当前路径添加到路径优化器中
            self.path_optimizer.add_path(input_tour)
            
            # 执行优化版局部搜索，并传递种群参数
            init_cost = tour_cost(distance_matrix, individual["tour"])
            init_route = np.array(gls_evol_enhanced.tour2route(input_tour))
           
            gls_run.solve_instance( distance_matrix,  #第i个实例距离矩阵  #   #第i个实例的每个节点的坐标
                                    0.5,   #时间限制
                                    100,      #最大搜索次数
                                    5,#扰动时每次修改次数
                                    init_route,
                                    evo_individual,populations,res_populations)
            
            
            costs= [ind["cur_cost"] for ind in res_populations]
            tours= [ind["tour"] for ind in res_populations]
            self.logger.info("res_population_num: %s", len(res_populations))
            self.logger.info("res_population_costs: %s", costs)
            self.logger.info("res_populations: %s", tours)
            self.logger.info("populations_num: %s", len(populations))
            self.logger.info("populations: %s", populations)
            
            # 记录结束时间
            end_time = time.time()
            search_time = end_time - start_time
            
            # 更新统计信息
            self.logger.info(f"局部搜索耗时: {search_time:.2f}秒")
            staus= self.get_performance_stats()
            self.logger.info(f"路径优化器性能统计: {staus}")

            # 返回优化后的路径（从种群中获取最新的路径）
            optimized_individual = populations[individual_index]
            return {"new_tour": optimized_individual["tour"], "cur_cost": optimized_individual["cur_cost"]}

        except Exception as e:
            self.logger.error(f"生成利用路径时出错: {str(e)}")
            # 发生错误时，也生成一条新的贪心路径替换
            try:
                from greedy_path_generator import generate_path
                new_tour, new_cost = generate_path(distance_matrix)
                
                # 更新种群中对应位置的路径
                populations[individual_index]["tour"] = new_tour
                populations[individual_index]["cur_cost"] = new_cost
                
                self.logger.info(f"发生错误后已用贪心路径替换种群中索引 {individual_index} 处的路径，新成本: {new_cost}")
                return {"new_tour": new_tour, "cur_cost": new_cost}
            except Exception as e2:
                self.logger.error(f"生成贪心路径时出错: {str(e2)}")
                return {"new_tour": individual["tour"], "cur_cost": individual["cur_cost"]}
    


class EvolutionAssessmentExpert(ExpertBase):
    """进化评估专家，基于数学指标和统计分析评估策略效果，提供反馈
    使用纯算法实现，不依赖LLM"""

    def __init__(self, interface_llm=None):
        super().__init__()
        # 保留接口参数以保持兼容性，但不使用
        self.interface_llm = interface_llm

        # 评估历史记录
        self.feedback_history = []

        # 评估参数配置
        self.improvement_threshold = 0.01  # 改进阈值（1%）
        self.diversity_weight = 0.3        # 多样性权重
        self.convergence_window = 3        # 收敛趋势分析窗口
    

    
    def evaluate(self, old_stats_report, new_stats_report, strategies, iteration, total_iterations, old_res_populations=None, new_res_populations=None):
        """评估进化结果并生成报告，使用纯算法分析"""
        self.logger.info(f"--- Iteration {iteration} Assessment (Algorithm-based) ---")

        # 执行算法评估分析
        assessment_result = self.perform_algorithmic_assessment(
            old_stats_report=old_stats_report,
            new_stats_report=new_stats_report,
            strategies=strategies,
            iteration=iteration,
            total_iterations=total_iterations,
            old_res_populations=old_res_populations,
            new_res_populations=new_res_populations
        )

        # 生成评估报告
        assessment_response = self.generate_assessment_report(assessment_result)
        self.logger.info("算法评估报告: %s", assessment_response)
        
        # 保存反馈历史 - 添加精英解统计信息
        
        # --- 兼容: old_stats_report / new_stats_report 可能是 dict（stats_report）或 list（population） ---
        def _extract_costs(entity):
            if isinstance(entity, list):
                return [p.get("cur_cost", 0) for p in entity if isinstance(p, dict)]
            elif isinstance(entity, dict):
                cs = entity.get("cost_stats", {})
                return [cs.get("min", 0), cs.get("max", 0), cs.get("mean", 0)]
            else:
                return [0]

        def _calc_diversity(entity):
            if isinstance(entity, list):
                return utils.calculate_population_diversity(entity)
            return entity.get("diversity_level", 0) if isinstance(entity, dict) else 0

        old_costs = _extract_costs(old_stats_report)
        new_costs = _extract_costs(new_stats_report)

        old_population_stats = {
            "min_cost": min(old_costs) if old_costs else 0,
            "max_cost": max(old_costs) if old_costs else 0,
            "mean_cost": sum(old_costs) / len(old_costs) if old_costs else 0,
            "diversity": _calc_diversity(old_stats_report)
        }

        new_population_stats = {
            "min_cost": min(new_costs) if new_costs else 0,
            "max_cost": max(new_costs) if new_costs else 0,
            "mean_cost": sum(new_costs) / len(new_costs) if new_costs else 0,
            "diversity": _calc_diversity(new_stats_report)
        }
        
        # 计算精英解数量变化
        old_elite_count = len(old_res_populations) if old_res_populations is not None else 0
        new_elite_count = len(new_res_populations) if new_res_populations is not None else 0
        
        self.feedback_history.append({
            "iteration": iteration,
            "old_stats": old_population_stats,
            "new_stats": new_population_stats,
            "strategy_distribution": {
                "explore": strategies.count("explore"),
                "exploit": strategies.count("exploit")
            },
            "elite_stats": {
                "old_elite_count": old_elite_count,
                "new_elite_count": new_elite_count,
                "best_cost_improvement": old_population_stats["min_cost"] - new_population_stats["min_cost"]
            }
        })
        
        # 限制历史记录长度
        if len(self.feedback_history) > 5:
            self.feedback_history.pop(0)
        
        self.logger.info("进化评估完成")
        return assessment_response

    def perform_algorithmic_assessment(self, old_stats_report, new_stats_report, strategies, iteration, total_iterations, old_res_populations=None, new_res_populations=None):
        """执行基于算法的评估分析

        参数:
            old_stats_report: 旧种群统计报告
            new_stats_report: 新种群统计报告
            strategies: 策略分配列表
            iteration: 当前迭代次数
            total_iterations: 总迭代次数
            old_res_populations: 旧精英解种群
            new_res_populations: 新精英解种群

        返回:
            评估结果字典
        """
        self.logger.info("开始执行算法评估分析")

        # 1. 计算种群成本改进率
        cost_improvement = self.calculate_cost_improvement(old_stats_report, new_stats_report)

        # 2. 分析种群多样性变化
        diversity_analysis = self.analyze_diversity_change(old_stats_report, new_stats_report)

        # 3. 统计策略效果
        strategy_effectiveness = self.analyze_strategy_effectiveness(strategies, old_stats_report, new_stats_report)

        # 4. 分析收敛趋势
        convergence_analysis = self.analyze_convergence_trend(iteration)

        # 5. 分析精英解变化
        elite_analysis = self.analyze_elite_populations(old_res_populations, new_res_populations)

        # 6. 生成改进建议
        improvement_suggestions = self.generate_improvement_suggestions(
            cost_improvement, diversity_analysis, strategy_effectiveness,
            convergence_analysis, iteration, total_iterations
        )

        # 构建评估结果
        assessment_result = {
            "iteration": iteration,
            "cost_improvement": cost_improvement,
            "diversity_analysis": diversity_analysis,
            "strategy_effectiveness": strategy_effectiveness,
            "convergence_analysis": convergence_analysis,
            "elite_analysis": elite_analysis,
            "improvement_suggestions": improvement_suggestions,
            "overall_score": self.calculate_overall_score(cost_improvement, diversity_analysis, strategy_effectiveness)
        }

        return assessment_result

    def calculate_cost_improvement(self, old_stats_report, new_stats_report):
        """计算种群成本改进率

        参数:
            old_stats_report: 旧种群统计报告
            new_stats_report: 新种群统计报告

        返回:
            成本改进分析结果
        """
        # 提取成本数据
        old_costs = self._extract_costs(old_stats_report)
        new_costs = self._extract_costs(new_stats_report)

        if not old_costs or not new_costs:
            return {"improvement_rate": 0.0, "status": "no_data"}

        # 计算各项指标
        old_min, old_max, old_mean = min(old_costs), max(old_costs), sum(old_costs) / len(old_costs)
        new_min, new_max, new_mean = min(new_costs), max(new_costs), sum(new_costs) / len(new_costs)

        # 计算改进率
        min_improvement = (old_min - new_min) / old_min if old_min > 0 else 0
        mean_improvement = (old_mean - new_mean) / old_mean if old_mean > 0 else 0

        # 计算改进个体数量
        improved_count = 0
        if len(old_costs) == len(new_costs):
            improved_count = sum(1 for old, new in zip(old_costs, new_costs) if new < old)

        improvement_rate = improved_count / len(old_costs) if old_costs else 0

        # 评估改进状态
        if mean_improvement > self.improvement_threshold:
            status = "significant_improvement"
        elif mean_improvement > 0:
            status = "minor_improvement"
        elif mean_improvement == 0:
            status = "no_change"
        else:
            status = "deterioration"

        return {
            "improvement_rate": improvement_rate,
            "min_improvement": min_improvement,
            "mean_improvement": mean_improvement,
            "improved_count": improved_count,
            "total_count": len(old_costs),
            "status": status,
            "old_stats": {"min": old_min, "max": old_max, "mean": old_mean},
            "new_stats": {"min": new_min, "max": new_max, "mean": new_mean}
        }

    def analyze_diversity_change(self, old_stats_report, new_stats_report):
        """分析种群多样性变化

        参数:
            old_stats_report: 旧种群统计报告
            new_stats_report: 新种群统计报告

        返回:
            多样性分析结果
        """
        old_diversity = self._calc_diversity(old_stats_report)
        new_diversity = self._calc_diversity(new_stats_report)

        diversity_change = new_diversity - old_diversity
        diversity_change_rate = diversity_change / old_diversity if old_diversity > 0 else 0

        # 评估多样性状态
        if diversity_change_rate > 0.1:
            status = "diversity_increased"
        elif diversity_change_rate < -0.1:
            status = "diversity_decreased"
        else:
            status = "diversity_stable"

        return {
            "old_diversity": old_diversity,
            "new_diversity": new_diversity,
            "diversity_change": diversity_change,
            "diversity_change_rate": diversity_change_rate,
            "status": status
        }

    def analyze_strategy_effectiveness(self, strategies, old_stats_report, new_stats_report):
        """分析策略效果统计

        参数:
            strategies: 策略分配列表
            old_stats_report: 旧种群统计报告
            new_stats_report: 新种群统计报告

        返回:
            策略效果分析结果
        """
        old_costs = self._extract_costs(old_stats_report)
        new_costs = self._extract_costs(new_stats_report)

        if len(old_costs) != len(new_costs) or len(strategies) != len(old_costs):
            return {"status": "data_mismatch"}

        # 分别统计探索和利用策略的效果
        explore_improvements = []
        exploit_improvements = []

        for i, strategy in enumerate(strategies):
            if i < len(old_costs) and i < len(new_costs):
                improvement = (old_costs[i] - new_costs[i]) / old_costs[i] if old_costs[i] > 0 else 0

                if strategy == 'explore':
                    explore_improvements.append(improvement)
                elif strategy == 'exploit':
                    exploit_improvements.append(improvement)

        # 计算策略统计
        explore_stats = self._calculate_strategy_stats(explore_improvements, "explore")
        exploit_stats = self._calculate_strategy_stats(exploit_improvements, "exploit")

        # 比较策略效果
        explore_success_rate = sum(1 for imp in explore_improvements if imp > 0) / len(explore_improvements) if explore_improvements else 0
        exploit_success_rate = sum(1 for imp in exploit_improvements if imp > 0) / len(exploit_improvements) if exploit_improvements else 0

        # 策略分布
        strategy_distribution = {
            "explore_count": strategies.count("explore"),
            "exploit_count": strategies.count("exploit"),
            "total_count": len(strategies)
        }

        return {
            "explore_stats": explore_stats,
            "exploit_stats": exploit_stats,
            "explore_success_rate": explore_success_rate,
            "exploit_success_rate": exploit_success_rate,
            "strategy_distribution": strategy_distribution,
            "better_strategy": "explore" if explore_success_rate > exploit_success_rate else "exploit"
        }

    def _calculate_strategy_stats(self, improvements, strategy_name):
        """计算单个策略的统计信息"""
        if not improvements:
            return {"count": 0, "mean_improvement": 0, "success_rate": 0}

        mean_improvement = sum(improvements) / len(improvements)
        success_count = sum(1 for imp in improvements if imp > 0)
        success_rate = success_count / len(improvements)

        return {
            "count": len(improvements),
            "mean_improvement": mean_improvement,
            "success_rate": success_rate,
            "best_improvement": max(improvements) if improvements else 0,
            "worst_improvement": min(improvements) if improvements else 0
        }

    def analyze_convergence_trend(self, iteration):
        """分析收敛趋势

        参数:
            iteration: 当前迭代次数

        返回:
            收敛趋势分析结果
        """
        if len(self.feedback_history) < 2:
            return {
                "status": "insufficient_data",
                "trend": "unknown",
                "recent_trend": 0,
                "avg_improvement": 0,
                "improvement_history": [],
                "convergence_window": 0
            }

        # 获取最近几代的成本改进数据
        recent_history = self.feedback_history[-self.convergence_window:]

        # 计算成本改进趋势
        improvements = []
        for record in recent_history:
            old_min = record["old_stats"]["min_cost"]
            new_min = record["new_stats"]["min_cost"]
            improvement = (old_min - new_min) / old_min if old_min > 0 else 0
            improvements.append(improvement)

        # 分析趋势
        if len(improvements) >= 2:
            recent_trend = improvements[-1] - improvements[-2]
            avg_improvement = sum(improvements) / len(improvements)

            # 判断收敛状态
            if avg_improvement < 0.001:  # 改进很小
                if recent_trend <= 0:
                    status = "converged"
                else:
                    status = "slow_improvement"
            elif recent_trend > 0.01:
                status = "accelerating"
            elif recent_trend < -0.01:
                status = "decelerating"
            else:
                status = "stable_improvement"
        else:
            status = "insufficient_data"
            recent_trend = 0
            avg_improvement = improvements[0] if improvements else 0

        return {
            "status": status,
            "recent_trend": recent_trend,
            "avg_improvement": avg_improvement,
            "improvement_history": improvements,
            "convergence_window": len(recent_history)
        }

    def analyze_elite_populations(self, old_res_populations, new_res_populations):
        """分析精英解种群变化

        参数:
            old_res_populations: 旧精英解种群
            new_res_populations: 新精英解种群

        返回:
            精英解分析结果
        """
        old_count = len(old_res_populations) if old_res_populations else 0
        new_count = len(new_res_populations) if new_res_populations else 0

        count_change = new_count - old_count

        # 分析精英解质量变化
        if old_res_populations and new_res_populations:
            old_elite_costs = [ind["cur_cost"] for ind in old_res_populations if "cur_cost" in ind]
            new_elite_costs = [ind["cur_cost"] for ind in new_res_populations if "cur_cost" in ind]

            if old_elite_costs and new_elite_costs:
                old_best = min(old_elite_costs)
                new_best = min(new_elite_costs)
                quality_improvement = (old_best - new_best) / old_best if old_best > 0 else 0
            else:
                quality_improvement = 0
        else:
            quality_improvement = 0

        return {
            "old_count": old_count,
            "new_count": new_count,
            "count_change": count_change,
            "quality_improvement": quality_improvement,
            "status": "improved" if count_change > 0 or quality_improvement > 0 else "stable" if count_change == 0 and quality_improvement == 0 else "declined"
        }

    def generate_improvement_suggestions(self, cost_improvement, diversity_analysis, strategy_effectiveness, convergence_analysis, iteration, total_iterations):
        """生成改进建议

        参数:
            cost_improvement: 成本改进分析
            diversity_analysis: 多样性分析
            strategy_effectiveness: 策略效果分析
            convergence_analysis: 收敛趋势分析
            iteration: 当前迭代次数
            total_iterations: 总迭代次数

        返回:
            改进建议列表
        """
        suggestions = []

        # 基于成本改进状态的建议
        if cost_improvement["status"] == "deterioration":
            suggestions.append({
                "type": "cost_improvement",
                "priority": "high",
                "suggestion": "种群成本出现恶化，建议增加利用策略比例，加强局部搜索"
            })
        elif cost_improvement["status"] == "no_change":
            suggestions.append({
                "type": "cost_improvement",
                "priority": "medium",
                "suggestion": "成本改进停滞，建议调整探索-利用平衡，增加扰动强度"
            })

        # 基于多样性分析的建议
        if diversity_analysis["status"] == "diversity_decreased":
            suggestions.append({
                "type": "diversity",
                "priority": "high",
                "suggestion": "种群多样性下降，建议增加探索策略比例，引入更多随机扰动"
            })
        elif diversity_analysis["new_diversity"] < 0.2:
            suggestions.append({
                "type": "diversity",
                "priority": "medium",
                "suggestion": "种群多样性过低，可能陷入局部最优，建议重启部分个体"
            })

        # 基于策略效果的建议
        if "strategy_distribution" in strategy_effectiveness:
            explore_rate = strategy_effectiveness["explore_success_rate"]
            exploit_rate = strategy_effectiveness["exploit_success_rate"]

            if explore_rate > exploit_rate + 0.2:
                suggestions.append({
                    "type": "strategy_balance",
                    "priority": "medium",
                    "suggestion": f"探索策略效果更好(成功率{explore_rate:.2f} vs {exploit_rate:.2f})，建议增加探索比例"
                })
            elif exploit_rate > explore_rate + 0.2:
                suggestions.append({
                    "type": "strategy_balance",
                    "priority": "medium",
                    "suggestion": f"利用策略效果更好(成功率{exploit_rate:.2f} vs {explore_rate:.2f})，建议增加利用比例"
                })

        # 基于收敛趋势的建议
        if convergence_analysis["status"] == "converged":
            suggestions.append({
                "type": "convergence",
                "priority": "high",
                "suggestion": "算法已收敛，建议重启或增加扰动强度以跳出局部最优"
            })
        elif convergence_analysis["status"] == "decelerating":
            suggestions.append({
                "type": "convergence",
                "priority": "medium",
                "suggestion": "改进速度放缓，建议调整参数或改变策略分配"
            })

        # 基于进度的建议
        progress = iteration / total_iterations if total_iterations > 0 else 0
        if progress > 0.8 and cost_improvement["mean_improvement"] < 0.001:
            suggestions.append({
                "type": "late_stage",
                "priority": "low",
                "suggestion": "进化后期且改进微小，可考虑提前终止或进行最终优化"
            })
        elif progress < 0.3 and diversity_analysis["new_diversity"] < 0.3:
            suggestions.append({
                "type": "early_stage",
                "priority": "medium",
                "suggestion": "进化早期多样性不足，建议增加探索强度"
            })

        return suggestions

    def calculate_overall_score(self, cost_improvement, diversity_analysis, strategy_effectiveness):
        """计算总体评估分数

        参数:
            cost_improvement: 成本改进分析
            diversity_analysis: 多样性分析
            strategy_effectiveness: 策略效果分析

        返回:
            总体评估分数 (0-100)
        """
        score = 0

        # 成本改进分数 (40分)
        if cost_improvement["status"] == "significant_improvement":
            score += 40
        elif cost_improvement["status"] == "minor_improvement":
            score += 25
        elif cost_improvement["status"] == "no_change":
            score += 10
        # deterioration: 0分

        # 多样性分数 (30分)
        diversity_score = min(30, diversity_analysis["new_diversity"] * 30)
        score += diversity_score

        # 策略效果分数 (30分)
        if "explore_success_rate" in strategy_effectiveness and "exploit_success_rate" in strategy_effectiveness:
            avg_success_rate = (strategy_effectiveness["explore_success_rate"] + strategy_effectiveness["exploit_success_rate"]) / 2
            strategy_score = min(30, avg_success_rate * 30)
            score += strategy_score
        else:
            score += 15  # 默认分数

        return min(100, max(0, score))

    def generate_assessment_report(self, assessment_result):
        """生成评估报告文本

        参数:
            assessment_result: 评估结果字典

        返回:
            格式化的评估报告文本
        """
        report_lines = []

        # 标题
        report_lines.append(f"=== 第 {assessment_result['iteration']} 代进化评估报告 ===")
        report_lines.append(f"总体评估分数: {assessment_result['overall_score']:.1f}/100")
        report_lines.append("")

        # 成本改进分析
        cost_info = assessment_result['cost_improvement']
        report_lines.append("【成本改进分析】")
        report_lines.append(f"- 改进状态: {cost_info['status']}")
        report_lines.append(f"- 个体改进率: {cost_info['improvement_rate']:.2%} ({cost_info['improved_count']}/{cost_info['total_count']})")
        report_lines.append(f"- 平均成本改进: {cost_info['mean_improvement']:.4f}")
        report_lines.append(f"- 最优解改进: {cost_info['min_improvement']:.4f}")
        report_lines.append("")

        # 多样性分析
        diversity_info = assessment_result['diversity_analysis']
        report_lines.append("【多样性分析】")
        report_lines.append(f"- 多样性状态: {diversity_info['status']}")
        report_lines.append(f"- 当前多样性: {diversity_info['new_diversity']:.3f}")
        report_lines.append(f"- 多样性变化: {diversity_info['diversity_change']:+.3f}")
        report_lines.append("")

        # 策略效果分析
        strategy_info = assessment_result['strategy_effectiveness']
        if 'explore_success_rate' in strategy_info:
            report_lines.append("【策略效果分析】")
            report_lines.append(f"- 探索策略成功率: {strategy_info['explore_success_rate']:.2%}")
            report_lines.append(f"- 利用策略成功率: {strategy_info['exploit_success_rate']:.2%}")
            report_lines.append(f"- 更优策略: {strategy_info['better_strategy']}")
            report_lines.append("")

        # 收敛趋势分析
        convergence_info = assessment_result['convergence_analysis']
        report_lines.append("【收敛趋势分析】")
        report_lines.append(f"- 收敛状态: {convergence_info['status']}")
        report_lines.append(f"- 平均改进率: {convergence_info['avg_improvement']:.4f}")
        report_lines.append("")

        # 改进建议
        suggestions = assessment_result['improvement_suggestions']
        if suggestions:
            report_lines.append("【改进建议】")
            for i, suggestion in enumerate(suggestions, 1):
                priority_mark = "🔴" if suggestion['priority'] == 'high' else "🟡" if suggestion['priority'] == 'medium' else "🟢"
                report_lines.append(f"{i}. {priority_mark} {suggestion['suggestion']}")

        return "\n".join(report_lines)

    def _extract_costs(self, entity):
        """从统计报告或种群中提取成本数据"""
        if isinstance(entity, list):
            return [p.get("cur_cost", 0) for p in entity if isinstance(p, dict)]
        elif isinstance(entity, dict):
            cs = entity.get("cost_stats", {})
            return [cs.get("min", 0), cs.get("max", 0), cs.get("mean", 0)]
        else:
            return [0]

    def _calc_diversity(self, entity):
        """计算种群多样性"""
        if isinstance(entity, list):
            # 对于种群列表，计算路径多样性
            return self._calculate_population_diversity_from_list(entity)
        return entity.get("diversity_level", 0) if isinstance(entity, dict) else 0

    def _calculate_population_diversity_from_list(self, populations):
        """从种群列表计算多样性"""
        if len(populations) < 2:
            return 1.0

        # 计算路径间的平均汉明距离
        total_distance = 0
        comparisons = 0

        for i in range(len(populations)):
            for j in range(i + 1, len(populations)):
                if "tour" in populations[i] and "tour" in populations[j]:
                    tour1 = populations[i]["tour"]
                    tour2 = populations[j]["tour"]

                    # 计算汉明距离（不同位置的数量）
                    distance = sum(1 for a, b in zip(tour1, tour2) if a != b)
                    total_distance += distance
                    comparisons += 1

        if comparisons == 0:
            return 1.0

        avg_distance = total_distance / comparisons
        max_possible_distance = len(populations[0]["tour"]) if populations and "tour" in populations[0] else 1

        return min(1.0, avg_distance / max_possible_distance)


class ExpertCollaborationManager:
    """专家协作管理器，管理专家间的交互
    根据优化建议，优化专家与LLM的交互方式：
    1. 保留景观分析和策略选择专家与LLM的交互
    2. 统计分析、路径结构和精英解专家使用算法实现，不与LLM交互
    3. 探索专家使用纯算法实现，不依赖LLM生成多样化路径
    4. 利用专家完全使用局部搜索和扰动代码，不使用LLM
    5. 评估专家使用纯算法实现，基于数学指标和统计分析进行评估
    """
    
    def __init__(self, interface_llm, config=None):
        self.config = config or {}
        self.interface_llm = interface_llm
        self.experts = {}
        self._initialize_experts()
        self.logger = logging.getLogger(__name__)
        
        # 添加专家间数据共享存储
        self.shared_data = {
            "high_quality_edges": [],
            "difficult_regions": [],
            "opportunity_regions": [],
            "elite_features": {},
            "population_diversity": 0.0,
            "convergence_level": 0.0
        }
    
    def _initialize_experts(self):
        """初始化所有专家模块"""
        # 这些专家使用算法实现，不与LLM交互
        self.experts['stats'] = StatsExpert()
        self.experts['path'] = PathExpert()
        self.experts['elite'] = EliteExpert()
        
        # 这些专家需要与LLM交互
        self.experts['landscape'] = LandscapeExpert(self.interface_llm)
        self.experts['strategy'] = StrategyExpert(self.interface_llm)

        # 这些专家使用纯算法实现，不依赖LLM
        self.experts['exploration'] = ExplorationExpert(self.interface_llm)  # 传递接口保持兼容性，但不使用
        self.experts['exploitation'] = ExploitationExpert(self.interface_llm)  # 传递接口保持兼容性，但不使用
        self.experts['assessment'] = EvolutionAssessmentExpert(self.interface_llm)  # 传递接口保持兼容性，但不使用
    
    def update_shared_data(self, key, value):
        """更新专家间共享数据"""
        if key in self.shared_data:
            self.shared_data[key] = value
            self.logger.info(f"更新共享数据: {key}")
    
    def run_analysis_phase(self, populations, res_populations, distance_matrix, iteration, total_iterations=10, coordinates=None):
        """运行分析阶段，包括统计、路径和精英分析"""
        self.logger.info(f"--- Running Analysis Phase (Iteration {iteration}) ---")
        
        # 引入空间统计计算函数
        from experts_prompt import compute_spatial_stats
        
        # 运行本地分析专家
        stats_analysis = self.experts["stats"].analyze(populations)
        stats_report = self.experts["stats"].generate_report(stats_analysis, coordinates, distance_matrix)
        
        path_analysis = self.experts["path"].analyze(populations, distance_matrix)
        path_report = self.experts["path"].generate_report(path_analysis)
        
        elite_solutions = sorted(res_populations, key=lambda p: p['cur_cost']) if res_populations else []
        elite_report = self.experts["elite"].analyze(elite_solutions, populations, distance_matrix)
        
        # 运行景观分析专家
        landscape_report = self.experts["landscape"].analyze(
            stats_report, path_report, elite_report, iteration, total_iterations, 
            history_data={'spatial_stats': compute_spatial_stats(coordinates, distance_matrix)}
        )
        
        self.update_shared_data('landscape_report', landscape_report)
        return landscape_report, stats_report
    
    def run_strategy_phase(self, landscape_report, populations, iteration, strategy_feedback=None):
        """运行策略分配阶段"""
        self.logger.info("开始策略分配阶段")
        
        # 使用优化后的策略专家
        strategy_result = self.experts['strategy'].analyze(
            landscape_report=landscape_report,
            populations=populations,
            iteration=iteration,
            strategy_feedback=strategy_feedback
        )
        
        # 记录策略分配报告
        strategy_selection, strategy_response = strategy_result
        self.logger.info(f"策略分配报告: {strategy_selection}")
        self.logger.info(f"策略分配完整报告: {strategy_response}")
        
        self.logger.info("策略分配阶段完成")
        return strategy_result
    
    def run_evolution_phase(self, populations, strategies, landscape_report, distance_matrix, res_populations=None):
        """运行进化阶段，生成新路径"""
        self.logger.info("开始进化阶段")
        
        new_populations = []
        evolution_reports = []
        
        for i, individual in enumerate(populations):
            # 将共享数据传递给专家
            if strategies[i] == 'explore':
                self.logger.info(f"为个体 {i} 生成探索路径")
                new_path_data = self.experts['exploration'].generate_path(
                    individual=individual,
                    landscape_report=landscape_report,  # 包含了共享数据
                    populations=populations,
                    distance_matrix=distance_matrix,
                    individual_index=i,
                    evo_populations=None,
                    res_populations=res_populations
                )
                
                # 记录探索路径生成报告
                if new_path_data:
                    evolution_reports.append({"individual": i, "strategy": "explore", "path_data": new_path_data})
                    self.logger.info(f"个体 {i} 探索路径生成报告: {new_path_data}")
                
                if new_path_data and 'new_tour' in new_path_data:
                    # 创建新个体
                    new_tour = new_path_data['new_tour']
                    new_cost = tour_cost(distance_matrix, new_tour)
                    
                    new_individual = {
                        "tour": utils.convert_to_list(new_tour),
                        "cur_cost": new_cost
                    }
                else:
                    # 如果生成失败，保留原个体
                    new_individual = copy.deepcopy(individual)
            else:  # exploit
                self.logger.info(f"为个体 {i} 生成利用路径")
                
                # 传递共享数据给利用专家
                exploitation_landscape_report = {
                    "high_quality_edges": self.shared_data["high_quality_edges"],
                    "fixed_nodes": self.shared_data.get("elite_features", {}).get("fixed_nodes", []),
                    "low_quality_regions": self.shared_data["difficult_regions"]
                }
                
                new_path_data = self.experts['exploitation'].generate_path(
                    individual=individual,
                    landscape_report=exploitation_landscape_report,  # 使用共享数据
                    populations=populations,
                    distance_matrix=distance_matrix,
                    individual_index=i,
                    res_populations=res_populations  # 传递精英解集合
                )
                
                # 记录利用路径生成报告
                if new_path_data:
                    evolution_reports.append({"individual": i, "strategy": "exploit", "path_data": new_path_data})
                    self.logger.info(f"个体 {i} 利用路径生成报告: {new_path_data}")
                
                if new_path_data and 'new_tour' in new_path_data:
                    # 创建新个体
                    new_tour = new_path_data['new_tour']
                    new_cost = tour_cost(distance_matrix, new_tour)
                    
                    new_individual = {
                        "tour": utils.convert_to_list(new_tour),
                        "cur_cost": new_cost
                    }
                else:
                    # 如果生成失败，保留原个体
                    self.logger.warning(f"个体 {i} 的利用路径生成失败，保留原个体")
                    new_individual = copy.deepcopy(individual)
            
            new_populations.append(new_individual)
        
        # 记录整体进化报告
        self.logger.info(f"进化阶段报告汇总: {evolution_reports}")
        self.logger.info("进化阶段完成")
        return new_populations
    
    def run_assessment_phase(self, old_stats_report, new_stats_report, strategies, iteration, total_iterations, old_res_populations=None, new_res_populations=None):
        """运行评估阶段"""
        self.logger.info(f"--- Running Assessment Phase (Iteration {iteration}) ---")
        
        assessment_report = self.experts["assessment"].evaluate(
            old_stats_report, new_stats_report, strategies, iteration, total_iterations,
            old_res_populations=old_res_populations, new_res_populations=new_res_populations
        )
        
        # 更新共享数据
        self.update_shared_data('assessment_report', assessment_report)
        return assessment_report


def main():
    """多专家系统主函数"""
    # 导入并调用JIT预热模块，确保在处理实际数据前完成JIT编译
    from jit_warmup import warmup_jit_functions
    warmup_jit_functions()
    
    parser = argparse.ArgumentParser(description="多专家协作进化算法框架")
    parser.add_argument('--func_begin', type=int, required=False, default=24, help='起始索引，默认为0')
    parser.add_argument('--func_end', type=int, required=False, default=24,help='结束索引，默认为0')
    parser.add_argument('--iter_num', type=int, help='迭代次数', default=5)
    parser.add_argument('--pop_size', type=int, help='种群规模', default=10)
    args = parser.parse_args()

    # 定义实例列表并处理空值
    func_name = [
        "simple1_9", "simple2_10", "simple3_10", "simple4_11", "simple5_12", "simple6_12",
        "geometry1_10", "geometry2_12", "geometry3_10", "geometry4_10", "geometry5_10", "geometry6_15",
        "composite1_28", "composite2_34", "composite3_22", "composite4_33", "composite5_35", "composite6_39", 
        "composite7_42", "composite8_45", "composite9_48", "composite10_55", "composite11_59", "composite12_60", "composite13_66",
        "eil51","berlin52","st70","pr76","kroA100","lin105"
    ]
    func_name = [name for name in func_name if name]

    # 设置func_end默认值
    if args.func_end == 0 or args.func_end >= len(func_name):
        args.func_end = args.func_begin

    # 验证参数有效性
    if args.func_begin < 0 or args.func_end >= len(func_name) or args.func_begin > args.func_end:
        print(f"错误：参数范围无效。有效范围: 0-{len(func_name)-1}，当前值: {args.func_begin}-{args.func_end}")
        sys.exit(1)
    
    # 设置日志系统
    # 修改日志保存路径到MoE-main/Log文件夹
    log_dir = os.path.join(os.path.dirname(current_dir), "Log")
    os.makedirs(log_dir, exist_ok=True)
    setup_logging(log_dir=log_dir, log_file="moe_app.log")
    
    # 打印路径信息以便调试
    main_logger.info(f"项目根目录: {project_root}")
    main_logger.info(f"输入路径: {GLOBAL_INPUT_PATH}")
    main_logger.info(f"输出路径: {GLOBAL_OUTPUT_PATH}")
    
    # 检查输入目录是否存在
    if not os.path.exists(GLOBAL_INPUT_PATH):
        print(f"错误: 输入目录不存在: {GLOBAL_INPUT_PATH}")
        return
    
    # 确保目录存在
    os.makedirs(GLOBAL_INPUT_PATH, exist_ok=True)
    os.makedirs(GLOBAL_OUTPUT_PATH, exist_ok=True)
    
    # 从配置文件获取算法参数
    pop_size = args.pop_size if args.pop_size else ALGORITHM_CONFIG.get("pop_size", 20)
    evo_num = args.iter_num if args.iter_num else ALGORITHM_CONFIG.get("evo_num", 10)
    
    # 读取实例内容
    try:
        load_all_instances(func_name, GLOBAL_INPUT_PATH, args.func_begin, args.func_end, GLOBAL_OUTPUT_PATH)
        
        instance_file = os.path.join(GLOBAL_OUTPUT_PATH, "mmtsp_instances.pkl")
        
        if not os.path.exists(instance_file):
            print(f"警告: 实例文件不存在: {instance_file}")
            instances = {"func_name": [], "coordinate": [], "distance_matrix": [], "opt_cost": [], "opt_tour": []}
        else:
            with open(instance_file, "rb") as f: 
                instances = pkl.load(f)
                
        if len(instances["func_name"]) == 0:
            print("警告: 没有成功加载任何实例")
            return
            
    except Exception as e:
        print(f"错误: 加载或处理实例时出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return
    
    # 准备实例列表
    instances_selected = []
    for i in range(len(instances["func_name"])):
        instance_selected = {
            "func_name": instances["func_name"][i],
            "coordinate": instances["coordinate"][i],
            "distance_matrix": instances["distance_matrix"][i],
        }
        instances_selected.append(instance_selected)
    
    # 从配置文件获取API配置
    xfyun_config = API_CONFIG["xfyun"]
    api_endpoint = xfyun_config["endpoint"]
    api_key = xfyun_config["api_key"]
    model_LLM = xfyun_config["model"]
    
    # API接口设置
    debug_mode = False
    interface_llm = InterfaceAPI(api_type="gemini", debug_mode=debug_mode)
    # interface_llm = InterfaceAPI(api_type="deepseek")
    
    # 初始化专家协作管理器
    collaboration_manager = ExpertCollaborationManager(interface_llm)
    
    # 主循环，对每个实例进行处理
    for iter_idx in range(len(instances_selected)):
        instance = instances_selected[iter_idx]
        main_logger.info(f"开始处理实例: {instance['func_name']}")
        
        # 初始化种群
        # 使用INIT类的静态方法初始化种群
        populations = INIT.mixed_init(instance['distance_matrix'], pop_size)
        # 计算种群成本
        populations = INIT.calculate_population_costs(populations, instance['distance_matrix'])
        cur_best_cost = min(populations, key=lambda x: x["cur_cost"])["cur_cost"]
        print(f"当前最佳适应度：{cur_best_cost}")
        main_logger.info(f"初始化种群完成，当前最佳适应度: {cur_best_cost}")
        
        # 为当前实例创建专用的日志文件处理器
        # 确保使用MoE-main/Log文件夹保存实例日志
        instance_log_dir = os.path.join(os.path.dirname(current_dir), "Log")
        os.makedirs(instance_log_dir, exist_ok=True)
        instance_log_file = get_instance_log_file(instance['func_name'], instance_log_dir)
        file_handler = logging.FileHandler(instance_log_file, mode='w', encoding='utf-8')
        file_handler.setLevel(logging.INFO)
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)
        root_logger = logging.getLogger('')
        root_logger.addHandler(file_handler)
        
        # 存储各代进化个体和精英解
        evo_populations = []
        res_populations = []  # 存储精英解
        old_res_populations = None  # 初始化上一代精英解为None
        strategy_feedback = None
        
        # 每个实例进化evo_num代
        for evo_iter in range(evo_num):
            main_logger.info(f"{instance['func_name']} 开始进化第 {evo_iter+1} 代")
            print(f"iter: {evo_iter}")
            
            # 1. 分析阶段
            landscape_report, old_stats_report = collaboration_manager.run_analysis_phase(
                populations=populations,
                res_populations=res_populations,
                coordinates=instance["coordinate"],
                distance_matrix=instance["distance_matrix"],
                iteration=evo_iter,
                total_iterations=evo_num
            )
            # 记录完整的景观分析报告
            main_logger.info(f"景观分析完整报告: {landscape_report}")
            
            # 2. 策略分配阶段
            strategy_result = collaboration_manager.run_strategy_phase(
                landscape_report=landscape_report,
                populations=populations,
                iteration=evo_iter,
                strategy_feedback=strategy_feedback
            )
            strategy_selection, strategy_response = strategy_result
            # 记录完整的策略分配报告
            main_logger.info(f"策略分配: {strategy_selection}")
            main_logger.info(f"策略分配完整报告: {strategy_response}")
            
            # 保存当前种群副本用于后续评估
            old_populations = copy.deepcopy(populations)
            
            # 3. 进化阶段
            new_populations = collaboration_manager.run_evolution_phase(
                populations=populations,
                strategies=strategy_selection,
                landscape_report=landscape_report,
                distance_matrix=instance["distance_matrix"],
                res_populations=res_populations  # 传递精英解集合
            )
            
            # 更新种群
            populations = new_populations
            res_populations.sort(key=lambda x: x["cur_cost"])
            
            # 4. 评估阶段
            # 为新种群计算统计数据
            stats_expert = collaboration_manager.experts["stats"]
            new_stats_analysis = stats_expert.analyze(populations)
            new_stats_report = stats_expert.generate_report(new_stats_analysis, instance["coordinate"], instance["distance_matrix"])

            assessment_report = collaboration_manager.run_assessment_phase(
                old_stats_report, new_stats_report, strategy_selection, evo_iter, evo_num,
                old_res_populations=res_populations, new_res_populations=res_populations
            )
            
            # 更新下一次迭代的输入
            strategy_feedback = assessment_report
            populations = new_populations
            res_populations = res_populations
            
            main_logger.info(f"--- Finished Evolution Iteration {evo_iter+1} ---")
        
        # 保存最终结果
        # 首先按照JSON格式保存，保持原有功能
        final_result = {
            "instance_name": instance["func_name"],
            "best_cost": min(res_populations, key=lambda x: x["cur_cost"])["cur_cost"] if res_populations else float('inf'),
            "best_tour": min(res_populations, key=lambda x: x["cur_cost"])["tour"] if res_populations else [],
            "elite_solutions": [{
                "cost": sol["cur_cost"],
                "tour": sol["tour"]
            } for sol in res_populations[:3]]
        }
        
        # 修改解集保存路径到MoE-main/solution文件夹
        result_dir = os.path.join(os.path.dirname(current_dir), "solution")
        os.makedirs(result_dir, exist_ok=True)
        result_path = os.path.join(result_dir, f"{instance['func_name']}_solution.json")
        
        with open(result_path, 'w', encoding='utf-8') as f:
            json.dump(final_result, f, ensure_ascii=False, indent=2, cls=utils.NumpyEncoder)
        
        solution_dir = os.path.join(os.path.dirname(current_dir), "solution")
        os.makedirs(solution_dir, exist_ok=True)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        solution_path = os.path.join(solution_dir, f"{instance['func_name']}_{timestamp}.solution")
        
        with open(solution_path, 'w', encoding='utf-8') as f:
            # 对精英解按成本排序
            sorted_solutions = sorted(res_populations, key=lambda x: x["cur_cost"])
            
            if sorted_solutions:
                # 获取最优成本
                best_cost = int(round(sorted_solutions[0]['cur_cost']))
                
                # 写入每个解
                for sol in sorted_solutions:
                    # 构建行：成本 + 空格 + 节点序列
                    # 确保成本值为整数
                    cost = int(sol["cur_cost"]) if isinstance(sol["cur_cost"], (int, float)) else int(round(sol["cur_cost"]))
                    
                    # 只保存成本最优的路径
                    if cost > best_cost:
                        break  # 因为已排序，后续成本都会更大
                    
                    # 确保路径以0开始
                    tour = sol["tour"]
                    # if tour[0] != 0:
                    #     tour = [0] + [node for node in tour if node != 0]
                    
                    # 使用gls_evol_enhanced中的normalize_path函数，确保它返回有效的路径
                    normalized_tour = gls_evol_enhanced.normalize_path(tour)
                    if normalized_tour is None:
                        # 如果normalize_path返回None，则使用原始tour
                        normalized_tour = tour
                        
                    # 转换为字符串
                    tour_str = ' '.join(str(node) for node in normalized_tour)
                    line = f"{cost} {tour_str}\n"
                    f.write(line)
        
        main_logger.info(f"最终结果已保存到: {result_path}")
        main_logger.info(f"实例 {instance['func_name']} 处理完成")
        
        # 移除实例专用的日志处理器
        root_logger.removeHandler(file_handler)
        file_handler.close()


if __name__ == "__main__":
    main()