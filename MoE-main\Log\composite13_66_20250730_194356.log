2025-07-30 19:43:56,688 - __main__ - INFO - composite13_66 开始进化第 1 代
2025-07-30 19:43:56,689 - __main__ - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-07-30 19:43:56,689 - StatsExpert - INFO - 开始统计分析
2025-07-30 19:43:56,710 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 9975.0, 'max': 115683.0, 'mean': 79953.2, 'std': 45882.042183843565}, 'diversity': 0.9168350168350167, 'clusters': {'clusters': 8, 'cluster_sizes': [3, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-30 19:43:56,710 - PathExpert - INFO - 开始路径结构分析
2025-07-30 19:43:56,714 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-07-30 19:43:56,714 - PathExpert - INFO - 路径结构分析完成
2025-07-30 19:43:56,714 - EliteExpert - INFO - 开始精英解分析
2025-07-30 19:43:56,714 - EliteExpert - WARNING - 没有精英解可供分析
2025-07-30 19:43:59,896 - LandscapeExpert - INFO - 开始景观分析
2025-07-30 19:43:59,896 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-30 19:43:59,896 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 0/5)
- population_size: 10
- cost_stats: min 9975.0, mean 79953.2, max 115683.0, std 45882.042183843565
- diversity: 0.9168350168350167
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Not available (Δ Not available, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: []
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}
- elite_diversity: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

### Output Example
```json
{
  "search_space_features": {"ruggedness": 0.5, "modality": "multi", "deceptiveness": "unknown"},
  "population_state": {"diversity": 0.5, "convergence": 0.4, "clustering": "weak"},
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "unknown",
  "evolution_direction": {"recommended_focus": "explore", "operators": []}
}
```

### Formatting Guidelines (read carefully, DO NOT output this section)
1. Do NOT copy or repeat any *_sample values above; at least 50% of elements in difficult_regions and opportunity_regions must differ from those samples.
2. Use [] (empty array) when a list has no content; never output the string "None" or a null value.
3. For search_space_features use enumerated strings only:
    • ruggedness: "low" | "medium" | "high"
    • modality: "mono" | "multi" | "unknown"
    • deceptiveness: "low" | "moderate" | "high" | "unknown"
4. diversity and convergence must be floats with exactly 3 decimal places (e.g. 0.742).
5. Follow the JSON schema strictly; no additional keys or commentary.

2025-07-30 19:43:59,896 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-30 19:44:05,131 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "medium",
    "modality": "multi",
    "deceptiveness": "unknown"
  },
  "population_state": {
    "diversity": 0.917,
    "convergence": 0.000,
    "clustering": "unknown"
  },
  "difficult_regions": [
    [436.0, 321.0, 1000.0, 800.0],
    [2500.0, 1800.0, 3345.0, 2463.0]
  ],
  "opportunity_regions": [
    [1300.0, 1400.0, 2000.0, 2000.0],
    [2000.0, 1000.0, 2400.0, 1400.0]
  ],
  "evolution_phase": "exploration",
  "evolution_direction": {
    "recommended_focus": "explore",
    "operators": [
      "Mutation (e.g., 2-opt)",
      "Randomized Initialization",
      "Increase Population Size"
    ]
  }
}
```
2025-07-30 19:44:05,131 - utils - INFO - 成功从JSON中提取数据
2025-07-30 19:44:05,131 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-30 19:44:05,132 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'medium', 'modality': 'multi', 'deceptiveness': 'unknown'}, 'population_state': {'diversity': 0.917, 'convergence': 0.0, 'clustering': 'unknown'}, 'difficult_regions': [[436.0, 321.0, 1000.0, 800.0], [2500.0, 1800.0, 3345.0, 2463.0]], 'opportunity_regions': [[1300.0, 1400.0, 2000.0, 2000.0], [2000.0, 1000.0, 2400.0, 1400.0]], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'explore', 'operators': ['Mutation (e.g., 2-opt)', 'Randomized Initialization', 'Increase Population Size']}}
2025-07-30 19:44:05,132 - __main__ - INFO - 开始策略分配阶段
2025-07-30 19:44:05,132 - StrategyExpert - INFO - 开始策略分配分析
2025-07-30 19:44:05,132 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 0
- Population: 10 individuals
  • diversity: 0.917
  • best_cost: 9975.0
  • mean_cost: 79953.2
- Search-space: ruggedness medium, modality multi, deceptiveness unknown
- Evolution phase: exploration
- Landscape focus suggestion: explore
- Spatial density summary: None
- Elite diversity: N/A
- Difficult regions (# 2): [[436.0, 321.0, 1000.0, 800.0], [2500.0, 1800.0, 3345.0, 2463.0]]
- Opportunity regions (# 2): [[1300.0, 1400.0, 2000.0, 2000.0], [2000.0, 1000.0, 2400.0, 1400.0]]
- Last-iteration feedback: None

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Formatting Guidelines (DO NOT output this section)
1. global_explore_ratio must be rounded to exactly 2 decimal places (e.g., 0.80).
2. The individual_assignments mapping MUST include every index from 0 to (population_size - 1) without omission.
3. Valid values are "explore" or "exploit" (lowercase).
4. rationale ≤ 40 words.

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-30 19:44:05,132 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-30 19:44:05,132 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-30 19:44:09,134 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.75,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Population diversity is high, but focus is explore. Explore more difficult regions to improve global fitness and find better opportunities."
}
```
2025-07-30 19:44:09,134 - utils - INFO - 成功从JSON中提取数据
2025-07-30 19:44:09,134 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-07-30 19:44:09,134 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-07-30 19:44:09,134 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.75,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Population diversity is high, but focus is explore. Explore more difficult regions to improve global fitness and find better opportunities."
}
```
2025-07-30 19:44:09,134 - __main__ - INFO - 策略分配阶段完成
2025-07-30 19:44:09,134 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-07-30 19:44:09,134 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.75,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Population diversity is high, but focus is explore. Explore more difficult regions to improve global fitness and find better opportunities."
}
```
2025-07-30 19:44:09,135 - __main__ - INFO - 开始进化阶段
2025-07-30 19:44:09,135 - __main__ - INFO - 为个体 0 生成探索路径
2025-07-30 19:44:09,135 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-30 19:44:09,136 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 10097.0 (rank 10.0%)
- Population diversity: 0.7415384615384616
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): [1300.0, 1400.0, [2000.0, 1000.0
- Difficult regions to avoid (sample): [436.0, 321.0, , [2500.0, 1800.0
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-30 19:44:09,137 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-30 19:44:09,137 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-30 19:44:19,140 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-07-30 19:44:21,141 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 2/3)
2025-07-30 19:44:31,936 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-07-30 19:44:33,937 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 3/3)
2025-07-30 19:44:44,675 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-07-30 19:44:44,675 - ExplorationExpert - INFO - LLM生成的探索路径: API请求失败: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-07-30 19:44:44,675 - IdeaExtractor - INFO - 开始从探索响应中提取路径
2025-07-30 19:44:44,675 - IdeaExtractor - INFO - 开始从响应中提取路径
2025-07-30 19:44:44,675 - IdeaExtractor - WARNING - 无法从响应中提取路径，返回原始路径
2025-07-30 19:44:44,675 - IdeaExtractor - ERROR - 提取路径时出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-07-30 19:44:44,675 - ExplorationExpert - ERROR - 提取探索路径时出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-07-30 19:44:44,675 - __main__ - INFO - 为个体 1 生成探索路径
2025-07-30 19:44:44,675 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-30 19:44:44,675 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 10333.0 (rank 20.0%)
- Population diversity: 0.7415384615384616
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): [1300.0, 1400.0, [2000.0, 1000.0
- Difficult regions to avoid (sample): [436.0, 321.0, , [2500.0, 1800.0
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-30 19:44:44,675 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-30 19:44:44,675 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-30 19:44:49,115 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "Opportunity regions near nodes 13 & 20, avoid nodes near region [436.0, 321.0]",
  "strategy_comment": "Prioritizes opportunity regions, avoiding known difficult ones to maintain cost-effectiveness and exploration focus."
}
```
2025-07-30 19:44:49,116 - utils - INFO - 成功从JSON中提取数据
2025-07-30 19:44:49,116 - ExplorationExpert - INFO - 计算路径成本
2025-07-30 19:44:49,224 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-30 19:44:49,224 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-30 19:44:49,224 - __main__ - INFO - 为个体 2 生成探索路径
2025-07-30 19:44:49,224 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-30 19:44:49,224 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 9975.0 (rank 0.0%)
- Population diversity: 0.803076923076923
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): [1300.0, 1400.0, [2000.0, 1000.0
- Difficult regions to avoid (sample): [436.0, 321.0, , [2500.0, 1800.0
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-30 19:44:49,224 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-30 19:44:49,224 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-30 19:44:59,947 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-07-30 19:45:01,948 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 2/3)
2025-07-30 19:45:11,981 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-07-30 19:45:13,982 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 3/3)
2025-07-30 19:45:24,726 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-07-30 19:45:24,726 - ExplorationExpert - INFO - LLM生成的探索路径: API请求失败: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-07-30 19:45:24,726 - IdeaExtractor - INFO - 开始从探索响应中提取路径
2025-07-30 19:45:24,726 - IdeaExtractor - INFO - 开始从响应中提取路径
2025-07-30 19:45:24,726 - IdeaExtractor - WARNING - 无法从响应中提取路径，返回原始路径
2025-07-30 19:45:24,726 - IdeaExtractor - ERROR - 提取路径时出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-07-30 19:45:24,726 - ExplorationExpert - ERROR - 提取探索路径时出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-07-30 19:45:24,726 - __main__ - INFO - 为个体 3 生成探索路径
2025-07-30 19:45:24,726 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-30 19:45:24,726 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 108589.0 (rank 40.0%)
- Population diversity: 0.803076923076923
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): [1300.0, 1400.0, [2000.0, 1000.0
- Difficult regions to avoid (sample): [436.0, 321.0, , [2500.0, 1800.0
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-30 19:45:24,726 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-30 19:45:24,726 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-30 19:45:35,502 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-07-30 19:45:37,504 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 2/3)
2025-07-30 19:45:44,064 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "Opportunity and sparse cell exploration.",
  "strategy_comment": "Basic traversal, sampling cells near opportunity regions to boost diversity in unexplored areas."
}
```
2025-07-30 19:45:44,064 - utils - INFO - 成功从JSON中提取数据
2025-07-30 19:45:44,064 - ExplorationExpert - INFO - 计算路径成本
2025-07-30 19:45:44,064 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-30 19:45:44,064 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-30 19:45:44,064 - __main__ - INFO - 为个体 4 生成探索路径
2025-07-30 19:45:44,064 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-30 19:45:44,064 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 111993.0 (rank 70.0%)
- Population diversity: 0.72
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): [1300.0, 1400.0, [2000.0, 1000.0
- Difficult regions to avoid (sample): [436.0, 321.0, , [2500.0, 1800.0
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-30 19:45:44,064 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-30 19:45:44,064 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-30 19:45:54,794 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-07-30 19:45:56,795 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 2/3)
2025-07-30 19:46:07,513 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-07-30 19:46:09,514 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 3/3)
2025-07-30 19:46:18,698 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [1, 0, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2],
  "targeted_regions": "Opportunity regions around nodes 0, 1, and 2.",
  "strategy_comment": "Prioritizes exploration near potential opportunity zones and avoids difficult regions by initial path design."
}
```
2025-07-30 19:46:18,698 - utils - INFO - 成功从JSON中提取数据
2025-07-30 19:46:18,698 - ExplorationExpert - INFO - 计算路径成本
2025-07-30 19:46:18,698 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [1, 0, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2]
2025-07-30 19:46:18,698 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [1, 0, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2], 'cur_cost': 11479.0}
2025-07-30 19:46:18,698 - __main__ - INFO - 为个体 5 生成探索路径
2025-07-30 19:46:18,698 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-30 19:46:18,698 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 111748.0 (rank 70.0%)
- Population diversity: 0.6338461538461538
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): [1300.0, 1400.0, [2000.0, 1000.0
- Difficult regions to avoid (sample): [436.0, 321.0, , [2500.0, 1800.0
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-30 19:46:18,698 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-30 19:46:18,698 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-30 19:46:22,468 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 65, 1, 2, 64, 3, 4, 63, 5, 6, 62, 7, 8, 61, 9, 10, 60, 11, 12, 59, 13, 14, 58, 15, 16, 57, 17, 18, 56, 19, 20, 55, 21, 22, 54, 23, 24, 53, 25, 26, 52, 27, 28, 51, 29, 30, 50, 31, 32, 49, 33, 34, 48, 35, 36, 47, 37, 38, 46, 39, 40, 45, 41, 42, 44, 43],
  "targeted_regions": "Explore adjacent cells of opportunity regions",
  "strategy_comment": "Prioritizes exploration by starting at the edges then proceeding inwards to sample the sparse cells."
}
```
2025-07-30 19:46:22,468 - utils - INFO - 成功从JSON中提取数据
2025-07-30 19:46:22,468 - ExplorationExpert - INFO - 计算路径成本
2025-07-30 19:46:22,468 - ExplorationExpert - INFO - 探索路径生成完成，成本: 77143.0, 路径: [0, 65, 1, 2, 64, 3, 4, 63, 5, 6, 62, 7, 8, 61, 9, 10, 60, 11, 12, 59, 13, 14, 58, 15, 16, 57, 17, 18, 56, 19, 20, 55, 21, 22, 54, 23, 24, 53, 25, 26, 52, 27, 28, 51, 29, 30, 50, 31, 32, 49, 33, 34, 48, 35, 36, 47, 37, 38, 46, 39, 40, 45, 41, 42, 44, 43]
2025-07-30 19:46:22,468 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 65, 1, 2, 64, 3, 4, 63, 5, 6, 62, 7, 8, 61, 9, 10, 60, 11, 12, 59, 13, 14, 58, 15, 16, 57, 17, 18, 56, 19, 20, 55, 21, 22, 54, 23, 24, 53, 25, 26, 52, 27, 28, 51, 29, 30, 50, 31, 32, 49, 33, 34, 48, 35, 36, 47, 37, 38, 46, 39, 40, 45, 41, 42, 44, 43], 'cur_cost': 77143.0}
2025-07-30 19:46:22,468 - __main__ - INFO - 为个体 6 生成利用路径
2025-07-30 19:46:22,468 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-30 19:46:22,468 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-30 19:46:22,468 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 102547.0
2025-07-30 19:46:23,719 - ExploitationExpert - INFO - res_population_num: 1
2025-07-30 19:46:23,719 - ExploitationExpert - INFO - res_population_costs: [9586.0]
2025-07-30 19:46:23,719 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 47, 39, 44, 41, 38, 51, 50, 45, 46, 48, 42, 34, 30,
       28, 35, 26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  7,  1],
      dtype=int64)]
2025-07-30 19:46:23,719 - ExploitationExpert - INFO - populations_num: 10
2025-07-30 19:46:23,719 - ExploitationExpert - INFO - populations: [{'tour': array([15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36,
       35, 28, 30, 34, 33, 31, 24, 29, 32,  3,  7,  1, 11,  9,  5,  4,  8,
        2,  6, 10,  0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52,
       63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42],
      dtype=int64), 'cur_cost': 10097.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 21, 20, 13,
       23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30,
       34, 33, 31, 24, 29, 32,  3,  7,  1, 11,  9,  5,  4,  8,  2,  6, 10,
        0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63],
      dtype=int64), 'cur_cost': 9975.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [1, 0, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2], 'cur_cost': 11479.0}, {'tour': [0, 65, 1, 2, 64, 3, 4, 63, 5, 6, 62, 7, 8, 61, 9, 10, 60, 11, 12, 59, 13, 14, 58, 15, 16, 57, 17, 18, 56, 19, 20, 55, 21, 22, 54, 23, 24, 53, 25, 26, 52, 27, 28, 51, 29, 30, 50, 31, 32, 49, 33, 34, 48, 35, 36, 47, 37, 38, 46, 39, 40, 45, 41, 42, 44, 43], 'cur_cost': 77143.0}, {'tour': array([62, 30, 24, 14, 34, 43,  8,  7, 65, 48, 27, 12, 39, 20,  1, 56, 37,
       25, 61, 53, 50, 46,  6, 40,  4, 33, 32, 10, 64, 22, 51, 36, 35, 44,
       47,  5, 59, 26, 54,  0, 41, 11, 23, 38, 19, 28, 21, 52,  9, 16, 17,
       18, 29, 60, 58,  2, 15, 57, 45, 13, 42, 31,  3, 63, 55, 49],
      dtype=int64), 'cur_cost': 102547.0}, {'tour': array([48, 58, 36, 14, 62, 18, 39, 42, 20, 35, 17, 46,  9, 44, 28, 27, 45,
       54, 65,  1, 52, 13,  8, 16,  0, 49, 57, 59, 22, 64,  3, 43, 61, 47,
       53, 33, 19,  7, 15, 31, 41, 29,  4, 24, 34, 26, 12, 30,  5, 21, 32,
       60,  6, 51, 55, 11, 10, 56, 37, 38, 25, 50,  2, 63, 40, 23],
      dtype=int64), 'cur_cost': 112029.0}, {'tour': array([30, 19, 15, 41, 54,  0, 39, 10, 59, 52, 23, 64,  5, 51, 22,  3,  4,
       25, 48, 31,  1, 47, 29, 58, 53, 42, 63, 33,  8, 28, 24, 62,  9, 61,
       26, 57, 20, 37,  6, 12, 36, 38, 65, 45, 16, 46, 17, 50, 11, 18, 56,
       14, 40, 43,  7,  2, 27, 49, 55, 21, 32, 44, 35, 60, 13, 34],
      dtype=int64), 'cur_cost': 115683.0}, {'tour': array([47, 40, 14, 54, 24, 25, 37, 64, 41, 29, 23, 36, 38, 31, 15, 16, 65,
       60,  6, 26, 35, 30, 49, 39, 57, 19, 33, 20, 63, 46, 28,  0,  1, 18,
        5,  2, 11, 13, 34, 10, 59, 32,  4, 22,  8, 42, 56, 61, 58, 45,  7,
        3, 12,  9, 44, 52, 43, 62, 17, 50, 51, 27, 21, 48, 53, 55],
      dtype=int64), 'cur_cost': 99270.0}]
2025-07-30 19:46:23,719 - ExploitationExpert - INFO - 局部搜索耗时: 1.25秒
2025-07-30 19:46:23,719 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-07-30 19:46:23,719 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-07-30 19:46:23,719 - __main__ - INFO - 为个体 7 生成利用路径
2025-07-30 19:46:23,719 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-30 19:46:23,719 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-30 19:46:23,719 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 103954.0
2025-07-30 19:46:25,232 - ExploitationExpert - INFO - res_population_num: 2
2025-07-30 19:46:25,232 - ExploitationExpert - INFO - res_population_costs: [9586.0, 9563.0]
2025-07-30 19:46:25,232 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 47, 39, 44, 41, 38, 51, 50, 45, 46, 48, 42, 34, 30,
       28, 35, 26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 54, 57, 64, 53, 62,
       59, 56, 60, 58, 49, 40, 43, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50,
       42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 18, 17,
       12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  9, 11,  3,  7,  1],
      dtype=int64)]
2025-07-30 19:46:25,232 - ExploitationExpert - INFO - populations_num: 10
2025-07-30 19:46:25,232 - ExploitationExpert - INFO - populations: [{'tour': array([15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36,
       35, 28, 30, 34, 33, 31, 24, 29, 32,  3,  7,  1, 11,  9,  5,  4,  8,
        2,  6, 10,  0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52,
       63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42],
      dtype=int64), 'cur_cost': 10097.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 21, 20, 13,
       23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30,
       34, 33, 31, 24, 29, 32,  3,  7,  1, 11,  9,  5,  4,  8,  2,  6, 10,
        0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63],
      dtype=int64), 'cur_cost': 9975.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [1, 0, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2], 'cur_cost': 11479.0}, {'tour': [0, 65, 1, 2, 64, 3, 4, 63, 5, 6, 62, 7, 8, 61, 9, 10, 60, 11, 12, 59, 13, 14, 58, 15, 16, 57, 17, 18, 56, 19, 20, 55, 21, 22, 54, 23, 24, 53, 25, 26, 52, 27, 28, 51, 29, 30, 50, 31, 32, 49, 33, 34, 48, 35, 36, 47, 37, 38, 46, 39, 40, 45, 41, 42, 44, 43], 'cur_cost': 77143.0}, {'tour': array([62, 30, 24, 14, 34, 43,  8,  7, 65, 48, 27, 12, 39, 20,  1, 56, 37,
       25, 61, 53, 50, 46,  6, 40,  4, 33, 32, 10, 64, 22, 51, 36, 35, 44,
       47,  5, 59, 26, 54,  0, 41, 11, 23, 38, 19, 28, 21, 52,  9, 16, 17,
       18, 29, 60, 58,  2, 15, 57, 45, 13, 42, 31,  3, 63, 55, 49],
      dtype=int64), 'cur_cost': 102547.0}, {'tour': array([42, 22, 20, 21, 38, 39, 27, 40, 13, 62, 65,  3, 32, 56,  1, 31, 15,
       25, 46, 53, 63,  0, 30, 36,  8, 35, 52, 47, 54, 24, 37,  9, 29, 59,
       18,  4, 55, 49, 48, 16,  2, 11, 45, 34, 14, 28, 12, 50, 23, 57, 61,
       17, 33, 60, 51, 44, 58, 43, 10,  5, 19,  6, 64,  7, 41, 26],
      dtype=int64), 'cur_cost': 103954.0}, {'tour': array([30, 19, 15, 41, 54,  0, 39, 10, 59, 52, 23, 64,  5, 51, 22,  3,  4,
       25, 48, 31,  1, 47, 29, 58, 53, 42, 63, 33,  8, 28, 24, 62,  9, 61,
       26, 57, 20, 37,  6, 12, 36, 38, 65, 45, 16, 46, 17, 50, 11, 18, 56,
       14, 40, 43,  7,  2, 27, 49, 55, 21, 32, 44, 35, 60, 13, 34],
      dtype=int64), 'cur_cost': 115683.0}, {'tour': array([47, 40, 14, 54, 24, 25, 37, 64, 41, 29, 23, 36, 38, 31, 15, 16, 65,
       60,  6, 26, 35, 30, 49, 39, 57, 19, 33, 20, 63, 46, 28,  0,  1, 18,
        5,  2, 11, 13, 34, 10, 59, 32,  4, 22,  8, 42, 56, 61, 58, 45,  7,
        3, 12,  9, 44, 52, 43, 62, 17, 50, 51, 27, 21, 48, 53, 55],
      dtype=int64), 'cur_cost': 99270.0}]
2025-07-30 19:46:25,232 - ExploitationExpert - INFO - 局部搜索耗时: 1.51秒
2025-07-30 19:46:25,232 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-07-30 19:46:25,232 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-07-30 19:46:25,232 - __main__ - INFO - 为个体 8 生成利用路径
2025-07-30 19:46:25,232 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-30 19:46:25,232 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-30 19:46:25,232 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 111708.0
2025-07-30 19:46:25,732 - ExploitationExpert - INFO - res_population_num: 8
2025-07-30 19:46:25,732 - ExploitationExpert - INFO - res_population_costs: [9586.0, 9563.0, 9561.0, 9536, 9528, 9522, 9521, 9521]
2025-07-30 19:46:25,732 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 47, 39, 44, 41, 38, 51, 50, 45, 46, 48, 42, 34, 30,
       28, 35, 26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 54, 57, 64, 53, 62,
       59, 56, 60, 58, 49, 40, 43, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50,
       42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 18, 17,
       12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  8,  2,  6,  4,  5,  9,  3, 27, 37, 36, 26, 25, 31,
       24, 29, 32, 33, 28, 30, 35, 34, 42, 50, 51, 38, 41, 45, 44, 39, 47,
       46, 48, 49, 40, 43, 21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14,
       56, 58, 60, 54, 57, 64, 62, 59, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 56, 58, 60, 54, 57, 64, 62,
       59, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 31, 33, 35, 34, 30, 28, 32, 29, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-07-30 19:46:25,734 - ExploitationExpert - INFO - populations_num: 10
2025-07-30 19:46:25,734 - ExploitationExpert - INFO - populations: [{'tour': array([15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36,
       35, 28, 30, 34, 33, 31, 24, 29, 32,  3,  7,  1, 11,  9,  5,  4,  8,
        2,  6, 10,  0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52,
       63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42],
      dtype=int64), 'cur_cost': 10097.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 21, 20, 13,
       23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30,
       34, 33, 31, 24, 29, 32,  3,  7,  1, 11,  9,  5,  4,  8,  2,  6, 10,
        0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63],
      dtype=int64), 'cur_cost': 9975.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [1, 0, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2], 'cur_cost': 11479.0}, {'tour': [0, 65, 1, 2, 64, 3, 4, 63, 5, 6, 62, 7, 8, 61, 9, 10, 60, 11, 12, 59, 13, 14, 58, 15, 16, 57, 17, 18, 56, 19, 20, 55, 21, 22, 54, 23, 24, 53, 25, 26, 52, 27, 28, 51, 29, 30, 50, 31, 32, 49, 33, 34, 48, 35, 36, 47, 37, 38, 46, 39, 40, 45, 41, 42, 44, 43], 'cur_cost': 77143.0}, {'tour': array([62, 30, 24, 14, 34, 43,  8,  7, 65, 48, 27, 12, 39, 20,  1, 56, 37,
       25, 61, 53, 50, 46,  6, 40,  4, 33, 32, 10, 64, 22, 51, 36, 35, 44,
       47,  5, 59, 26, 54,  0, 41, 11, 23, 38, 19, 28, 21, 52,  9, 16, 17,
       18, 29, 60, 58,  2, 15, 57, 45, 13, 42, 31,  3, 63, 55, 49],
      dtype=int64), 'cur_cost': 102547.0}, {'tour': array([42, 22, 20, 21, 38, 39, 27, 40, 13, 62, 65,  3, 32, 56,  1, 31, 15,
       25, 46, 53, 63,  0, 30, 36,  8, 35, 52, 47, 54, 24, 37,  9, 29, 59,
       18,  4, 55, 49, 48, 16,  2, 11, 45, 34, 14, 28, 12, 50, 23, 57, 61,
       17, 33, 60, 51, 44, 58, 43, 10,  5, 19,  6, 64,  7, 41, 26],
      dtype=int64), 'cur_cost': 103954.0}, {'tour': array([26, 49, 42, 53,  8, 37, 30,  2,  0, 40, 51, 47,  3, 22, 45, 54, 46,
       17, 52, 38, 25, 63, 16, 50, 24, 64, 55,  5, 31,  1, 18, 58, 28,  7,
       61, 36, 56, 34,  6, 10, 35, 11, 32,  9, 43, 14, 59, 57, 21, 33, 29,
       48, 23, 15, 27, 41, 39,  4, 44, 20, 60, 12, 13, 65, 19, 62],
      dtype=int64), 'cur_cost': 111708.0}, {'tour': array([47, 40, 14, 54, 24, 25, 37, 64, 41, 29, 23, 36, 38, 31, 15, 16, 65,
       60,  6, 26, 35, 30, 49, 39, 57, 19, 33, 20, 63, 46, 28,  0,  1, 18,
        5,  2, 11, 13, 34, 10, 59, 32,  4, 22,  8, 42, 56, 61, 58, 45,  7,
        3, 12,  9, 44, 52, 43, 62, 17, 50, 51, 27, 21, 48, 53, 55],
      dtype=int64), 'cur_cost': 99270.0}]
2025-07-30 19:46:25,734 - ExploitationExpert - INFO - 局部搜索耗时: 0.50秒
2025-07-30 19:46:25,734 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-07-30 19:46:25,734 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-07-30 19:46:25,734 - __main__ - INFO - 为个体 9 生成利用路径
2025-07-30 19:46:25,734 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-30 19:46:25,734 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-30 19:46:25,734 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 98977.0
2025-07-30 19:46:26,234 - ExploitationExpert - INFO - res_population_num: 13
2025-07-30 19:46:26,234 - ExploitationExpert - INFO - res_population_costs: [9586.0, 9563.0, 9561.0, 9536, 9528, 9522, 9521, 9521, 9521, 9521, 9521, 9521, 9521]
2025-07-30 19:46:26,235 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 47, 39, 44, 41, 38, 51, 50, 45, 46, 48, 42, 34, 30,
       28, 35, 26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 54, 57, 64, 53, 62,
       59, 56, 60, 58, 49, 40, 43, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50,
       42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 18, 17,
       12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  8,  2,  6,  4,  5,  9,  3, 27, 37, 36, 26, 25, 31,
       24, 29, 32, 33, 28, 30, 35, 34, 42, 50, 51, 38, 41, 45, 44, 39, 47,
       46, 48, 49, 40, 43, 21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14,
       56, 58, 60, 54, 57, 64, 62, 59, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 56, 58, 60, 54, 57, 64, 62,
       59, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 31, 33, 35, 34, 30, 28, 32, 29, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-07-30 19:46:26,239 - ExploitationExpert - INFO - populations_num: 10
2025-07-30 19:46:26,239 - ExploitationExpert - INFO - populations: [{'tour': array([15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36,
       35, 28, 30, 34, 33, 31, 24, 29, 32,  3,  7,  1, 11,  9,  5,  4,  8,
        2,  6, 10,  0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52,
       63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42],
      dtype=int64), 'cur_cost': 10097.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 21, 20, 13,
       23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30,
       34, 33, 31, 24, 29, 32,  3,  7,  1, 11,  9,  5,  4,  8,  2,  6, 10,
        0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63],
      dtype=int64), 'cur_cost': 9975.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [1, 0, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2], 'cur_cost': 11479.0}, {'tour': [0, 65, 1, 2, 64, 3, 4, 63, 5, 6, 62, 7, 8, 61, 9, 10, 60, 11, 12, 59, 13, 14, 58, 15, 16, 57, 17, 18, 56, 19, 20, 55, 21, 22, 54, 23, 24, 53, 25, 26, 52, 27, 28, 51, 29, 30, 50, 31, 32, 49, 33, 34, 48, 35, 36, 47, 37, 38, 46, 39, 40, 45, 41, 42, 44, 43], 'cur_cost': 77143.0}, {'tour': array([62, 30, 24, 14, 34, 43,  8,  7, 65, 48, 27, 12, 39, 20,  1, 56, 37,
       25, 61, 53, 50, 46,  6, 40,  4, 33, 32, 10, 64, 22, 51, 36, 35, 44,
       47,  5, 59, 26, 54,  0, 41, 11, 23, 38, 19, 28, 21, 52,  9, 16, 17,
       18, 29, 60, 58,  2, 15, 57, 45, 13, 42, 31,  3, 63, 55, 49],
      dtype=int64), 'cur_cost': 102547.0}, {'tour': array([42, 22, 20, 21, 38, 39, 27, 40, 13, 62, 65,  3, 32, 56,  1, 31, 15,
       25, 46, 53, 63,  0, 30, 36,  8, 35, 52, 47, 54, 24, 37,  9, 29, 59,
       18,  4, 55, 49, 48, 16,  2, 11, 45, 34, 14, 28, 12, 50, 23, 57, 61,
       17, 33, 60, 51, 44, 58, 43, 10,  5, 19,  6, 64,  7, 41, 26],
      dtype=int64), 'cur_cost': 103954.0}, {'tour': array([26, 49, 42, 53,  8, 37, 30,  2,  0, 40, 51, 47,  3, 22, 45, 54, 46,
       17, 52, 38, 25, 63, 16, 50, 24, 64, 55,  5, 31,  1, 18, 58, 28,  7,
       61, 36, 56, 34,  6, 10, 35, 11, 32,  9, 43, 14, 59, 57, 21, 33, 29,
       48, 23, 15, 27, 41, 39,  4, 44, 20, 60, 12, 13, 65, 19, 62],
      dtype=int64), 'cur_cost': 111708.0}, {'tour': array([12, 41, 49, 33, 62,  6, 55, 46,  5, 17, 23, 45,  7, 14, 30, 36, 34,
       20,  4, 27, 47,  1, 61, 52, 56,  9, 43, 13, 28, 37, 44, 48, 54,  0,
       42, 38, 21, 57, 40, 22, 19, 58, 64, 11, 24, 16, 15, 31, 32, 39, 53,
       50, 60, 35, 51, 63, 25, 18,  3, 29, 10, 26, 59,  2, 65,  8],
      dtype=int64), 'cur_cost': 98977.0}]
2025-07-30 19:46:26,241 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-30 19:46:26,242 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-07-30 19:46:26,242 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-07-30 19:46:26,242 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [1, 0, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2], 'cur_cost': 11479.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 65, 1, 2, 64, 3, 4, 63, 5, 6, 62, 7, 8, 61, 9, 10, 60, 11, 12, 59, 13, 14, 58, 15, 16, 57, 17, 18, 56, 19, 20, 55, 21, 22, 54, 23, 24, 53, 25, 26, 52, 27, 28, 51, 29, 30, 50, 31, 32, 49, 33, 34, 48, 35, 36, 47, 37, 38, 46, 39, 40, 45, 41, 42, 44, 43], 'cur_cost': 77143.0}}]
2025-07-30 19:46:26,242 - __main__ - INFO - 进化阶段完成
2025-07-30 19:46:26,242 - StatsExpert - INFO - 开始统计分析
2025-07-30 19:46:26,259 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 9975.0, 'max': 111708.0, 'mean': 54883.8, 'std': 44746.98302634491}, 'diversity': 0.8464646464646464, 'clusters': {'clusters': 7, 'cluster_sizes': [2, 3, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-30 19:46:26,259 - __main__ - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-07-30 19:46:26,259 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment ---
2025-07-30 19:46:26,259 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-07-30 19:46:26,259 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 0/5

#### Population metrics
- Min cost: 0 → 0 (Δ 0)
- Mean cost: 0 → 0
- Diversity: 0.000 → 0.000 (Δ 0)

#### Elite metrics
- Elite count: 13 → 13
- Best elite cost: 9521 → 9521 (Δ 0)
- Elite diversity: 0.147 → 0.147 (Δ 0.0)

#### Strategy performance
- Explore: 0 inds, success 0% , avg Δ 0
- Exploit: 0 inds, success 0% , avg Δ 0

#### Other indicators
- No-change individuals: 0
- Regression individuals: 0
- Historical trends: N/A

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

### Output Example
```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.5, "exploit": 0.5},
  "balance_state": "balanced",
  "recommendations": ["maintain_current_ratio"]
}
```

2025-07-30 19:46:26,259 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-30 19:46:36,978 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-07-30 19:46:38,979 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 2/3)
2025-07-30 19:46:49,741 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-07-30 19:46:51,742 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 3/3)
2025-07-30 19:47:02,485 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-07-30 19:47:02,485 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: API请求失败: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-07-30 19:47:02,485 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-07-30 19:47:02,485 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-07-30 19:47:02,485 - __main__ - INFO - composite13_66 开始进化第 2 代
2025-07-30 19:47:02,485 - __main__ - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-07-30 19:47:02,485 - StatsExpert - INFO - 开始统计分析
2025-07-30 19:47:02,508 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 9975.0, 'max': 111708.0, 'mean': 54883.8, 'std': 44746.98302634491}, 'diversity': 0.8464646464646464, 'clusters': {'clusters': 7, 'cluster_sizes': [2, 3, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-30 19:47:02,508 - PathExpert - INFO - 开始路径结构分析
2025-07-30 19:47:02,511 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-07-30 19:47:02,511 - PathExpert - INFO - 路径结构分析完成
2025-07-30 19:47:02,511 - EliteExpert - INFO - 开始精英解分析
2025-07-30 19:47:02,516 - EliteExpert - INFO - 精英解分析完成
2025-07-30 19:47:02,517 - LandscapeExpert - INFO - 开始景观分析
2025-07-30 19:47:02,519 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-30 19:47:02,519 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 1/5)
- population_size: 10
- cost_stats: min 9975.0, mean 54883.8, max 111708.0, std 44746.98302634491
- diversity: 0.8464646464646464
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Not available (Δ Not available, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {'common_edges': {}, 'common_edge_ratio': 0.0}
- elite_diversity: {'diversity_score': 0.24300699300699302}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

### Output Example
```json
{
  "search_space_features": {"ruggedness": 0.5, "modality": "multi", "deceptiveness": "unknown"},
  "population_state": {"diversity": 0.5, "convergence": 0.4, "clustering": "weak"},
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "unknown",
  "evolution_direction": {"recommended_focus": "explore", "operators": []}
}
```

### Formatting Guidelines (read carefully, DO NOT output this section)
1. Do NOT copy or repeat any *_sample values above; at least 50% of elements in difficult_regions and opportunity_regions must differ from those samples.
2. Use [] (empty array) when a list has no content; never output the string "None" or a null value.
3. For search_space_features use enumerated strings only:
    • ruggedness: "low" | "medium" | "high"
    • modality: "mono" | "multi" | "unknown"
    • deceptiveness: "low" | "moderate" | "high" | "unknown"
4. diversity and convergence must be floats with exactly 3 decimal places (e.g. 0.742).
5. Follow the JSON schema strictly; no additional keys or commentary.

2025-07-30 19:47:02,520 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-30 19:47:13,262 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-07-30 19:47:15,263 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 2/3)
2025-07-30 19:47:27,518 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-07-30 19:47:29,519 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 3/3)
2025-07-30 19:47:30,555 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: 503 Server Error: Service Unavailable for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 19:47:30,555 - LandscapeExpert - INFO - LLM返回的分析结果: API请求失败: 503 Server Error: Service Unavailable for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ
2025-07-30 19:47:30,555 - LandscapeExpert - WARNING - 解析景观分析结果失败: Unable to extract valid JSON data from the response
2025-07-30 19:47:30,555 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-30 19:47:30,555 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.5, 'modality': 'unknown', 'deceptiveness': 'unknown'}, 'population_state': {'diversity': 0.5, 'convergence': 0.5, 'clustering': 'unknown'}, 'difficult_regions': [], 'opportunity_regions': [], 'evolution_phase': 'unknown', 'evolution_direction': {'recommended_focus': 'balance', 'operators': []}, 'raw_text': 'API请求失败: 503 Server Error: Service Unavailable for url: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ'}
2025-07-30 19:47:30,555 - __main__ - INFO - 开始策略分配阶段
2025-07-30 19:47:30,556 - StrategyExpert - INFO - 开始策略分配分析
2025-07-30 19:47:30,556 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 1
- Population: 10 individuals
  • diversity: 0.5
  • best_cost: 9975.0
  • mean_cost: 54883.8
- Search-space: ruggedness 0.5, modality unknown, deceptiveness unknown
- Evolution phase: unknown
- Landscape focus suggestion: balance
- Spatial density summary: None
- Elite diversity: N/A
- Difficult regions (# 0): None
- Opportunity regions (# 0): None
- Last-iteration feedback: API请求失败: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Formatting Guidelines (DO NOT output this section)
1. global_explore_ratio must be rounded to exactly 2 decimal places (e.g., 0.80).
2. The individual_assignments mapping MUST include every index from 0 to (population_size - 1) without omission.
3. Valid values are "explore" or "exploit" (lowercase).
4. rationale ≤ 40 words.

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-30 19:47:30,556 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-30 19:47:30,556 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-30 19:47:41,281 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-07-30 19:47:43,281 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 2/3)
2025-07-30 19:47:54,017 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-07-30 19:47:56,018 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 3/3)
2025-07-30 19:48:06,769 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-07-30 19:48:06,769 - StrategyExpert - INFO - LLM返回的策略分配结果: API请求失败: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-07-30 19:48:06,769 - StrategyExpert - WARNING - 解析策略分配结果时出错: Unable to extract valid JSON data from the response
2025-07-30 19:48:06,769 - StrategyExpert - INFO - 策略分配完成: ['explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-07-30 19:48:06,769 - __main__ - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-07-30 19:48:06,769 - __main__ - INFO - 策略分配完整报告: API请求失败: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-07-30 19:48:06,769 - __main__ - INFO - 策略分配阶段完成
2025-07-30 19:48:06,769 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-07-30 19:48:06,769 - __main__ - INFO - 策略分配完整报告: API请求失败: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-07-30 19:48:06,769 - __main__ - INFO - 开始进化阶段
2025-07-30 19:48:06,769 - __main__ - INFO - 为个体 0 生成探索路径
2025-07-30 19:48:06,769 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-30 19:48:06,769 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 10097.0 (rank 10.0%)
- Population diversity: 0.6153846153846154
- Evolution phase: unknown
- Landscape recommends focus: balance
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-30 19:48:06,769 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-30 19:48:06,769 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-30 19:48:17,518 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-07-30 19:48:19,519 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 2/3)
2025-07-30 19:48:22,523 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "All nodes",
  "strategy_comment": "Creates a simple path that visits all nodes sequentially while meeting constraints."
}
```
2025-07-30 19:48:22,523 - utils - INFO - 成功从JSON中提取数据
2025-07-30 19:48:22,523 - ExplorationExpert - INFO - 计算路径成本
2025-07-30 19:48:22,523 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-30 19:48:22,524 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-30 19:48:22,524 - __main__ - INFO - 为个体 1 生成利用路径
2025-07-30 19:48:22,524 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-30 19:48:22,525 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-30 19:48:22,525 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 114187.0
2025-07-30 19:48:23,027 - ExploitationExpert - INFO - res_population_num: 16
2025-07-30 19:48:23,027 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9522, 9528, 9536, 9561.0, 9563.0, 9586.0, 9521, 9521, 9521]
2025-07-30 19:48:23,027 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 31, 33, 35, 34, 30, 28, 32, 29, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 56, 58, 60, 54, 57, 64, 62,
       59, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  8,  2,  6,  4,  5,  9,  3, 27, 37, 36, 26, 25, 31,
       24, 29, 32, 33, 28, 30, 35, 34, 42, 50, 51, 38, 41, 45, 44, 39, 47,
       46, 48, 49, 40, 43, 21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14,
       56, 58, 60, 54, 57, 64, 62, 59, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 54, 57, 64, 53, 62,
       59, 56, 60, 58, 49, 40, 43, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50,
       42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 18, 17,
       12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 47, 39, 44, 41, 38, 51, 50, 45, 46, 48, 42, 34, 30,
       28, 35, 26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-07-30 19:48:23,027 - ExploitationExpert - INFO - populations_num: 10
2025-07-30 19:48:23,027 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([21, 57, 16, 56, 23, 53, 26, 51, 35, 32,  8,  5, 12, 13, 36, 28, 64,
       17, 54, 46, 40,  3, 15, 50, 22, 52,  4, 44, 45, 34, 55,  6,  7, 25,
       10, 42, 58, 20, 18, 24, 47, 30, 11, 27, 19, 63, 43, 37, 62, 29,  1,
       59, 48,  0, 60, 39, 38, 61, 33, 65, 31, 41, 49,  2,  9, 14],
      dtype=int64), 'cur_cost': 114187.0}, {'tour': array([40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 21, 20, 13,
       23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30,
       34, 33, 31, 24, 29, 32,  3,  7,  1, 11,  9,  5,  4,  8,  2,  6, 10,
        0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63],
      dtype=int64), 'cur_cost': 9975.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [1, 0, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2], 'cur_cost': 11479.0}, {'tour': [0, 65, 1, 2, 64, 3, 4, 63, 5, 6, 62, 7, 8, 61, 9, 10, 60, 11, 12, 59, 13, 14, 58, 15, 16, 57, 17, 18, 56, 19, 20, 55, 21, 22, 54, 23, 24, 53, 25, 26, 52, 27, 28, 51, 29, 30, 50, 31, 32, 49, 33, 34, 48, 35, 36, 47, 37, 38, 46, 39, 40, 45, 41, 42, 44, 43], 'cur_cost': 77143.0}, {'tour': array([62, 30, 24, 14, 34, 43,  8,  7, 65, 48, 27, 12, 39, 20,  1, 56, 37,
       25, 61, 53, 50, 46,  6, 40,  4, 33, 32, 10, 64, 22, 51, 36, 35, 44,
       47,  5, 59, 26, 54,  0, 41, 11, 23, 38, 19, 28, 21, 52,  9, 16, 17,
       18, 29, 60, 58,  2, 15, 57, 45, 13, 42, 31,  3, 63, 55, 49],
      dtype=int64), 'cur_cost': 102547.0}, {'tour': array([42, 22, 20, 21, 38, 39, 27, 40, 13, 62, 65,  3, 32, 56,  1, 31, 15,
       25, 46, 53, 63,  0, 30, 36,  8, 35, 52, 47, 54, 24, 37,  9, 29, 59,
       18,  4, 55, 49, 48, 16,  2, 11, 45, 34, 14, 28, 12, 50, 23, 57, 61,
       17, 33, 60, 51, 44, 58, 43, 10,  5, 19,  6, 64,  7, 41, 26],
      dtype=int64), 'cur_cost': 103954.0}, {'tour': array([26, 49, 42, 53,  8, 37, 30,  2,  0, 40, 51, 47,  3, 22, 45, 54, 46,
       17, 52, 38, 25, 63, 16, 50, 24, 64, 55,  5, 31,  1, 18, 58, 28,  7,
       61, 36, 56, 34,  6, 10, 35, 11, 32,  9, 43, 14, 59, 57, 21, 33, 29,
       48, 23, 15, 27, 41, 39,  4, 44, 20, 60, 12, 13, 65, 19, 62],
      dtype=int64), 'cur_cost': 111708.0}, {'tour': array([12, 41, 49, 33, 62,  6, 55, 46,  5, 17, 23, 45,  7, 14, 30, 36, 34,
       20,  4, 27, 47,  1, 61, 52, 56,  9, 43, 13, 28, 37, 44, 48, 54,  0,
       42, 38, 21, 57, 40, 22, 19, 58, 64, 11, 24, 16, 15, 31, 32, 39, 53,
       50, 60, 35, 51, 63, 25, 18,  3, 29, 10, 26, 59,  2, 65,  8],
      dtype=int64), 'cur_cost': 98977.0}]
2025-07-30 19:48:23,034 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-30 19:48:23,034 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-07-30 19:48:23,034 - __main__ - WARNING - 个体 1 的利用路径生成失败，保留原个体
2025-07-30 19:48:23,034 - __main__ - INFO - 为个体 2 生成探索路径
2025-07-30 19:48:23,034 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-30 19:48:23,034 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 9975.0 (rank 0.0%)
- Population diversity: 0.683076923076923
- Evolution phase: unknown
- Landscape recommends focus: balance
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-30 19:48:23,034 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-30 19:48:23,034 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-30 19:48:28,401 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64],
  "targeted_regions": "Sparse cell exploration; initial node selection prioritized.",
  "strategy_comment": "Prioritized sparse cells and used a randomized insertion approach with basic cost heuristics."
}
```
2025-07-30 19:48:28,402 - utils - INFO - 成功从JSON中提取数据
2025-07-30 19:48:28,402 - ExplorationExpert - INFO - 计算路径成本
2025-07-30 19:48:28,402 - ExplorationExpert - INFO - 探索路径生成完成，成本: 13563.0, 路径: [0, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64]
2025-07-30 19:48:28,402 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64], 'cur_cost': 13563.0}
2025-07-30 19:48:28,402 - __main__ - INFO - 为个体 3 生成利用路径
2025-07-30 19:48:28,402 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-30 19:48:28,402 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-30 19:48:28,403 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 106004.0
2025-07-30 19:48:28,403 - ExploitationExpert - INFO - 发现相似路径，相似度: 1.0000，相似id：4,跳过局部搜索
2025-07-30 19:48:28,403 - ExploitationExpert - INFO - 路径与已搜索路径相似度高，跳过局部搜索，生成新的贪心路径替换
2025-07-30 19:48:28,403 - __main__ - INFO - 个体 3 利用路径生成报告: {'new_tour': array([ 8, 38, 10, 14, 52, 18, 16, 32,  1, 34, 58, 46, 33, 45, 37, 36,  5,
        2,  6, 24, 51, 57, 49, 11, 55, 47, 19, 17, 30, 31, 53, 27, 35, 39,
       64, 60, 44,  7, 25, 26, 29, 61, 23, 63, 65, 59, 40, 54, 50, 43, 22,
       21, 28, 62,  4,  3, 41, 56,  9, 48, 12, 42, 15, 13,  0, 20],
      dtype=int64), 'cur_cost': 106004.0}
2025-07-30 19:48:28,403 - __main__ - INFO - 为个体 4 生成探索路径
2025-07-30 19:48:28,403 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-30 19:48:28,403 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.6953846153846154
- Evolution phase: unknown
- Landscape recommends focus: balance
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-30 19:48:28,403 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-30 19:48:28,403 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-30 19:48:32,406 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "all nodes",
  "strategy_comment": "Creates a simple, ordered path for high diversity, visiting every node exactly once."
}
```
2025-07-30 19:48:32,407 - utils - INFO - 成功从JSON中提取数据
2025-07-30 19:48:32,407 - ExplorationExpert - INFO - 计算路径成本
2025-07-30 19:48:32,407 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-30 19:48:32,407 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-30 19:48:32,407 - __main__ - INFO - 为个体 5 生成利用路径
2025-07-30 19:48:32,407 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-30 19:48:32,408 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-30 19:48:32,408 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 103033.0
2025-07-30 19:48:32,912 - ExploitationExpert - INFO - res_population_num: 16
2025-07-30 19:48:32,912 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9522, 9528, 9536, 9561.0, 9563.0, 9586.0, 9521, 9521, 9521]
2025-07-30 19:48:32,913 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 31, 33, 35, 34, 30, 28, 32, 29, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 56, 58, 60, 54, 57, 64, 62,
       59, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  8,  2,  6,  4,  5,  9,  3, 27, 37, 36, 26, 25, 31,
       24, 29, 32, 33, 28, 30, 35, 34, 42, 50, 51, 38, 41, 45, 44, 39, 47,
       46, 48, 49, 40, 43, 21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14,
       56, 58, 60, 54, 57, 64, 62, 59, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 54, 57, 64, 53, 62,
       59, 56, 60, 58, 49, 40, 43, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50,
       42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 18, 17,
       12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 47, 39, 44, 41, 38, 51, 50, 45, 46, 48, 42, 34, 30,
       28, 35, 26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-07-30 19:48:32,920 - ExploitationExpert - INFO - populations_num: 10
2025-07-30 19:48:32,920 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([21, 57, 16, 56, 23, 53, 26, 51, 35, 32,  8,  5, 12, 13, 36, 28, 64,
       17, 54, 46, 40,  3, 15, 50, 22, 52,  4, 44, 45, 34, 55,  6,  7, 25,
       10, 42, 58, 20, 18, 24, 47, 30, 11, 27, 19, 63, 43, 37, 62, 29,  1,
       59, 48,  0, 60, 39, 38, 61, 33, 65, 31, 41, 49,  2,  9, 14],
      dtype=int64), 'cur_cost': 114187.0}, {'tour': [0, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64], 'cur_cost': 13563.0}, {'tour': array([ 8, 38, 10, 14, 52, 18, 16, 32,  1, 34, 58, 46, 33, 45, 37, 36,  5,
        2,  6, 24, 51, 57, 49, 11, 55, 47, 19, 17, 30, 31, 53, 27, 35, 39,
       64, 60, 44,  7, 25, 26, 29, 61, 23, 63, 65, 59, 40, 54, 50, 43, 22,
       21, 28, 62,  4,  3, 41, 56,  9, 48, 12, 42, 15, 13,  0, 20],
      dtype=int64), 'cur_cost': 106004.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([58, 41, 39, 23,  6, 64, 14, 10, 47, 40, 11, 33, 62, 65, 25, 12, 48,
       61,  4, 59,  7, 16, 31, 30, 50, 55, 17, 29, 28, 60, 15, 54, 18, 24,
       37,  8, 21, 63, 19, 57, 51, 44, 38, 46, 35, 42, 53, 34, 52, 56, 43,
        5, 20, 13, 45, 36, 49,  2, 26,  3,  1,  9,  0, 27, 22, 32],
      dtype=int64), 'cur_cost': 103033.0}, {'tour': array([62, 30, 24, 14, 34, 43,  8,  7, 65, 48, 27, 12, 39, 20,  1, 56, 37,
       25, 61, 53, 50, 46,  6, 40,  4, 33, 32, 10, 64, 22, 51, 36, 35, 44,
       47,  5, 59, 26, 54,  0, 41, 11, 23, 38, 19, 28, 21, 52,  9, 16, 17,
       18, 29, 60, 58,  2, 15, 57, 45, 13, 42, 31,  3, 63, 55, 49],
      dtype=int64), 'cur_cost': 102547.0}, {'tour': array([42, 22, 20, 21, 38, 39, 27, 40, 13, 62, 65,  3, 32, 56,  1, 31, 15,
       25, 46, 53, 63,  0, 30, 36,  8, 35, 52, 47, 54, 24, 37,  9, 29, 59,
       18,  4, 55, 49, 48, 16,  2, 11, 45, 34, 14, 28, 12, 50, 23, 57, 61,
       17, 33, 60, 51, 44, 58, 43, 10,  5, 19,  6, 64,  7, 41, 26],
      dtype=int64), 'cur_cost': 103954.0}, {'tour': array([26, 49, 42, 53,  8, 37, 30,  2,  0, 40, 51, 47,  3, 22, 45, 54, 46,
       17, 52, 38, 25, 63, 16, 50, 24, 64, 55,  5, 31,  1, 18, 58, 28,  7,
       61, 36, 56, 34,  6, 10, 35, 11, 32,  9, 43, 14, 59, 57, 21, 33, 29,
       48, 23, 15, 27, 41, 39,  4, 44, 20, 60, 12, 13, 65, 19, 62],
      dtype=int64), 'cur_cost': 111708.0}, {'tour': array([12, 41, 49, 33, 62,  6, 55, 46,  5, 17, 23, 45,  7, 14, 30, 36, 34,
       20,  4, 27, 47,  1, 61, 52, 56,  9, 43, 13, 28, 37, 44, 48, 54,  0,
       42, 38, 21, 57, 40, 22, 19, 58, 64, 11, 24, 16, 15, 31, 32, 39, 53,
       50, 60, 35, 51, 63, 25, 18,  3, 29, 10, 26, 59,  2, 65,  8],
      dtype=int64), 'cur_cost': 98977.0}]
2025-07-30 19:48:32,923 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-07-30 19:48:32,923 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 1, 'total_searches': 7, 'skip_rate': 0.14285714285714285, 'estimated_time_saved': 5, 'path_optimizer': {'path_count': 6, 'cache_hits': 4, 'similarity_calculations': 20, 'cache_hit_rate': 0.2, 'cache_size': 16}}
2025-07-30 19:48:32,923 - __main__ - WARNING - 个体 5 的利用路径生成失败，保留原个体
2025-07-30 19:48:32,923 - __main__ - INFO - 为个体 6 生成探索路径
2025-07-30 19:48:32,924 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-30 19:48:32,924 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 102547.0 (rank 40.0%)
- Population diversity: 0.7092307692307692
- Evolution phase: unknown
- Landscape recommends focus: balance
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-30 19:48:32,924 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-30 19:48:32,925 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-30 19:48:44,296 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-07-30 19:48:46,296 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 2/3)
2025-07-30 19:49:00,432 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-07-30 19:49:02,434 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 3/3)
2025-07-30 19:49:05,250 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "All nodes, as path order is most important",
  "strategy_comment": "Create a linear path that touches every single node, ensuring all areas are covered efficiently."
}
```
2025-07-30 19:49:05,251 - utils - INFO - 成功从JSON中提取数据
2025-07-30 19:49:05,251 - ExplorationExpert - INFO - 计算路径成本
2025-07-30 19:49:05,251 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-30 19:49:05,251 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-30 19:49:05,251 - __main__ - INFO - 为个体 7 生成利用路径
2025-07-30 19:49:05,251 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-30 19:49:05,251 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-30 19:49:05,251 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 106889.0
2025-07-30 19:49:05,759 - ExploitationExpert - INFO - res_population_num: 17
2025-07-30 19:49:05,760 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9522, 9528, 9536, 9561.0, 9563.0, 9586.0, 9521, 9521, 9521, 9521]
2025-07-30 19:49:05,760 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 31, 33, 35, 34, 30, 28, 32, 29, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 56, 58, 60, 54, 57, 64, 62,
       59, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  8,  2,  6,  4,  5,  9,  3, 27, 37, 36, 26, 25, 31,
       24, 29, 32, 33, 28, 30, 35, 34, 42, 50, 51, 38, 41, 45, 44, 39, 47,
       46, 48, 49, 40, 43, 21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14,
       56, 58, 60, 54, 57, 64, 62, 59, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 54, 57, 64, 53, 62,
       59, 56, 60, 58, 49, 40, 43, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50,
       42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 18, 17,
       12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 47, 39, 44, 41, 38, 51, 50, 45, 46, 48, 42, 34, 30,
       28, 35, 26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64)]
2025-07-30 19:49:05,766 - ExploitationExpert - INFO - populations_num: 10
2025-07-30 19:49:05,766 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([21, 57, 16, 56, 23, 53, 26, 51, 35, 32,  8,  5, 12, 13, 36, 28, 64,
       17, 54, 46, 40,  3, 15, 50, 22, 52,  4, 44, 45, 34, 55,  6,  7, 25,
       10, 42, 58, 20, 18, 24, 47, 30, 11, 27, 19, 63, 43, 37, 62, 29,  1,
       59, 48,  0, 60, 39, 38, 61, 33, 65, 31, 41, 49,  2,  9, 14],
      dtype=int64), 'cur_cost': 114187.0}, {'tour': [0, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64], 'cur_cost': 13563.0}, {'tour': array([ 8, 38, 10, 14, 52, 18, 16, 32,  1, 34, 58, 46, 33, 45, 37, 36,  5,
        2,  6, 24, 51, 57, 49, 11, 55, 47, 19, 17, 30, 31, 53, 27, 35, 39,
       64, 60, 44,  7, 25, 26, 29, 61, 23, 63, 65, 59, 40, 54, 50, 43, 22,
       21, 28, 62,  4,  3, 41, 56,  9, 48, 12, 42, 15, 13,  0, 20],
      dtype=int64), 'cur_cost': 106004.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([58, 41, 39, 23,  6, 64, 14, 10, 47, 40, 11, 33, 62, 65, 25, 12, 48,
       61,  4, 59,  7, 16, 31, 30, 50, 55, 17, 29, 28, 60, 15, 54, 18, 24,
       37,  8, 21, 63, 19, 57, 51, 44, 38, 46, 35, 42, 53, 34, 52, 56, 43,
        5, 20, 13, 45, 36, 49,  2, 26,  3,  1,  9,  0, 27, 22, 32],
      dtype=int64), 'cur_cost': 103033.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([ 1, 34, 60, 55, 40, 30, 50, 52, 29, 20, 46, 59, 14, 47, 44, 25, 31,
       35, 37, 12,  6, 27, 23,  0, 10, 13, 54, 56,  3, 48, 41, 16, 57,  4,
       22, 24, 63, 42, 18, 65, 15, 38, 58,  2, 28, 49, 11, 33, 64, 32, 39,
       62, 21, 51, 61,  8, 45, 19, 43,  7,  9,  5, 53, 26, 36, 17],
      dtype=int64), 'cur_cost': 106889.0}, {'tour': array([26, 49, 42, 53,  8, 37, 30,  2,  0, 40, 51, 47,  3, 22, 45, 54, 46,
       17, 52, 38, 25, 63, 16, 50, 24, 64, 55,  5, 31,  1, 18, 58, 28,  7,
       61, 36, 56, 34,  6, 10, 35, 11, 32,  9, 43, 14, 59, 57, 21, 33, 29,
       48, 23, 15, 27, 41, 39,  4, 44, 20, 60, 12, 13, 65, 19, 62],
      dtype=int64), 'cur_cost': 111708.0}, {'tour': array([12, 41, 49, 33, 62,  6, 55, 46,  5, 17, 23, 45,  7, 14, 30, 36, 34,
       20,  4, 27, 47,  1, 61, 52, 56,  9, 43, 13, 28, 37, 44, 48, 54,  0,
       42, 38, 21, 57, 40, 22, 19, 58, 64, 11, 24, 16, 15, 31, 32, 39, 53,
       50, 60, 35, 51, 63, 25, 18,  3, 29, 10, 26, 59,  2, 65,  8],
      dtype=int64), 'cur_cost': 98977.0}]
2025-07-30 19:49:05,770 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-07-30 19:49:05,770 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 1, 'total_searches': 8, 'skip_rate': 0.125, 'estimated_time_saved': 5, 'path_optimizer': {'path_count': 7, 'cache_hits': 4, 'similarity_calculations': 26, 'cache_hit_rate': 0.15384615384615385, 'cache_size': 22}}
2025-07-30 19:49:05,770 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-07-30 19:49:05,770 - __main__ - INFO - 为个体 8 生成探索路径
2025-07-30 19:49:05,770 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-30 19:49:05,771 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 111708.0 (rank 80.0%)
- Population diversity: 0.6353846153846154
- Evolution phase: unknown
- Landscape recommends focus: balance
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-30 19:49:05,771 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-30 19:49:05,771 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-30 19:49:16,502 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-07-30 19:49:18,504 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 2/3)
2025-07-30 19:49:27,352 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "Explore all nodes.",
  "strategy_comment": "Simple path to ensure all nodes are visited while maintaining diversity."
}
```
2025-07-30 19:49:27,352 - utils - INFO - 成功从JSON中提取数据
2025-07-30 19:49:27,352 - ExplorationExpert - INFO - 计算路径成本
2025-07-30 19:49:27,353 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-30 19:49:27,353 - __main__ - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-30 19:49:27,353 - __main__ - INFO - 为个体 9 生成利用路径
2025-07-30 19:49:27,353 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-30 19:49:27,353 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-30 19:49:27,354 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 103451.0
2025-07-30 19:49:27,856 - ExploitationExpert - INFO - res_population_num: 17
2025-07-30 19:49:27,856 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9522, 9528, 9536, 9561.0, 9563.0, 9586.0, 9521, 9521, 9521, 9521]
2025-07-30 19:49:27,856 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 31, 33, 35, 34, 30, 28, 32, 29, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 56, 58, 60, 54, 57, 64, 62,
       59, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  8,  2,  6,  4,  5,  9,  3, 27, 37, 36, 26, 25, 31,
       24, 29, 32, 33, 28, 30, 35, 34, 42, 50, 51, 38, 41, 45, 44, 39, 47,
       46, 48, 49, 40, 43, 21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14,
       56, 58, 60, 54, 57, 64, 62, 59, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 54, 57, 64, 53, 62,
       59, 56, 60, 58, 49, 40, 43, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50,
       42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 18, 17,
       12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 47, 39, 44, 41, 38, 51, 50, 45, 46, 48, 42, 34, 30,
       28, 35, 26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64)]
2025-07-30 19:49:27,861 - ExploitationExpert - INFO - populations_num: 10
2025-07-30 19:49:27,861 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([21, 57, 16, 56, 23, 53, 26, 51, 35, 32,  8,  5, 12, 13, 36, 28, 64,
       17, 54, 46, 40,  3, 15, 50, 22, 52,  4, 44, 45, 34, 55,  6,  7, 25,
       10, 42, 58, 20, 18, 24, 47, 30, 11, 27, 19, 63, 43, 37, 62, 29,  1,
       59, 48,  0, 60, 39, 38, 61, 33, 65, 31, 41, 49,  2,  9, 14],
      dtype=int64), 'cur_cost': 114187.0}, {'tour': [0, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64], 'cur_cost': 13563.0}, {'tour': array([ 8, 38, 10, 14, 52, 18, 16, 32,  1, 34, 58, 46, 33, 45, 37, 36,  5,
        2,  6, 24, 51, 57, 49, 11, 55, 47, 19, 17, 30, 31, 53, 27, 35, 39,
       64, 60, 44,  7, 25, 26, 29, 61, 23, 63, 65, 59, 40, 54, 50, 43, 22,
       21, 28, 62,  4,  3, 41, 56,  9, 48, 12, 42, 15, 13,  0, 20],
      dtype=int64), 'cur_cost': 106004.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([58, 41, 39, 23,  6, 64, 14, 10, 47, 40, 11, 33, 62, 65, 25, 12, 48,
       61,  4, 59,  7, 16, 31, 30, 50, 55, 17, 29, 28, 60, 15, 54, 18, 24,
       37,  8, 21, 63, 19, 57, 51, 44, 38, 46, 35, 42, 53, 34, 52, 56, 43,
        5, 20, 13, 45, 36, 49,  2, 26,  3,  1,  9,  0, 27, 22, 32],
      dtype=int64), 'cur_cost': 103033.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([ 1, 34, 60, 55, 40, 30, 50, 52, 29, 20, 46, 59, 14, 47, 44, 25, 31,
       35, 37, 12,  6, 27, 23,  0, 10, 13, 54, 56,  3, 48, 41, 16, 57,  4,
       22, 24, 63, 42, 18, 65, 15, 38, 58,  2, 28, 49, 11, 33, 64, 32, 39,
       62, 21, 51, 61,  8, 45, 19, 43,  7,  9,  5, 53, 26, 36, 17],
      dtype=int64), 'cur_cost': 106889.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([43, 54, 42, 41, 20, 53, 21,  0, 46, 39,  7, 48,  2, 47,  4, 14, 22,
       15, 63, 52, 56, 36,  6, 64,  1, 24, 45, 30, 19, 65, 11, 32,  9,  3,
       33, 60, 59, 34,  5, 26, 27, 38, 29, 40, 28, 31, 37, 16, 25,  8, 17,
       51, 49, 61, 12, 13, 58, 44, 18, 35, 57, 62, 55, 23, 50, 10],
      dtype=int64), 'cur_cost': 103451.0}]
2025-07-30 19:49:27,865 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-30 19:49:27,865 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 1, 'total_searches': 9, 'skip_rate': 0.1111111111111111, 'estimated_time_saved': 5, 'path_optimizer': {'path_count': 8, 'cache_hits': 4, 'similarity_calculations': 33, 'cache_hit_rate': 0.12121212121212122, 'cache_size': 29}}
2025-07-30 19:49:27,865 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-07-30 19:49:27,865 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64], 'cur_cost': 13563.0}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 8, 38, 10, 14, 52, 18, 16, 32,  1, 34, 58, 46, 33, 45, 37, 36,  5,
        2,  6, 24, 51, 57, 49, 11, 55, 47, 19, 17, 30, 31, 53, 27, 35, 39,
       64, 60, 44,  7, 25, 26, 29, 61, 23, 63, 65, 59, 40, 54, 50, 43, 22,
       21, 28, 62,  4,  3, 41, 56,  9, 48, 12, 42, 15, 13,  0, 20],
      dtype=int64), 'cur_cost': 106004.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}]
2025-07-30 19:49:27,865 - __main__ - INFO - 进化阶段完成
2025-07-30 19:49:27,865 - StatsExpert - INFO - 开始统计分析
2025-07-30 19:49:27,887 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11479.0, 'max': 114187.0, 'mean': 59304.3, 'std': 47497.06363987146}, 'diversity': 0.7494949494949493, 'clusters': {'clusters': 6, 'cluster_sizes': [5, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-30 19:49:27,888 - __main__ - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-07-30 19:49:27,888 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment ---
2025-07-30 19:49:27,889 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-07-30 19:49:27,889 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 1/5

#### Population metrics
- Min cost: 0 → 0 (Δ 0)
- Mean cost: 0 → 0
- Diversity: 0.000 → 0.000 (Δ 0)

#### Elite metrics
- Elite count: 17 → 17
- Best elite cost: 9521 → 9521 (Δ 0)
- Elite diversity: 0.112 → 0.112 (Δ 0.0)

#### Strategy performance
- Explore: 0 inds, success 0% , avg Δ 0
- Exploit: 0 inds, success 0% , avg Δ 0

#### Other indicators
- No-change individuals: 0
- Regression individuals: 0
- Historical trends: N/A

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

### Output Example
```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.5, "exploit": 0.5},
  "balance_state": "balanced",
  "recommendations": ["maintain_current_ratio"]
}
```

2025-07-30 19:49:27,889 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-30 19:49:38,610 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-07-30 19:49:40,611 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 2/3)
2025-07-30 19:49:51,516 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-07-30 19:49:53,517 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 3/3)
2025-07-30 19:50:04,506 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-07-30 19:50:04,506 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: API请求失败: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-07-30 19:50:04,506 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-07-30 19:50:04,506 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-07-30 19:50:04,506 - __main__ - INFO - composite13_66 开始进化第 3 代
2025-07-30 19:50:04,507 - __main__ - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-07-30 19:50:04,507 - StatsExpert - INFO - 开始统计分析
2025-07-30 19:50:04,529 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11479.0, 'max': 114187.0, 'mean': 59304.3, 'std': 47497.06363987146}, 'diversity': 0.7494949494949493, 'clusters': {'clusters': 6, 'cluster_sizes': [5, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-30 19:50:04,529 - PathExpert - INFO - 开始路径结构分析
2025-07-30 19:50:04,532 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-07-30 19:50:04,532 - PathExpert - INFO - 路径结构分析完成
2025-07-30 19:50:04,532 - EliteExpert - INFO - 开始精英解分析
2025-07-30 19:50:04,540 - EliteExpert - INFO - 精英解分析完成
2025-07-30 19:50:04,542 - LandscapeExpert - INFO - 开始景观分析
2025-07-30 19:50:04,544 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-30 19:50:04,545 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 2/5)
- population_size: 10
- cost_stats: min 11479.0, mean 59304.3, max 114187.0, std 47497.06363987146
- diversity: 0.7494949494949493
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Not available (Δ Not available, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {'common_edges': {}, 'common_edge_ratio': 0.0}
- elite_diversity: {'diversity_score': 0.223596256684492}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

### Output Example
```json
{
  "search_space_features": {"ruggedness": 0.5, "modality": "multi", "deceptiveness": "unknown"},
  "population_state": {"diversity": 0.5, "convergence": 0.4, "clustering": "weak"},
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "unknown",
  "evolution_direction": {"recommended_focus": "explore", "operators": []}
}
```

### Formatting Guidelines (read carefully, DO NOT output this section)
1. Do NOT copy or repeat any *_sample values above; at least 50% of elements in difficult_regions and opportunity_regions must differ from those samples.
2. Use [] (empty array) when a list has no content; never output the string "None" or a null value.
3. For search_space_features use enumerated strings only:
    • ruggedness: "low" | "medium" | "high"
    • modality: "mono" | "multi" | "unknown"
    • deceptiveness: "low" | "moderate" | "high" | "unknown"
4. diversity and convergence must be floats with exactly 3 decimal places (e.g. 0.742).
5. Follow the JSON schema strictly; no additional keys or commentary.

2025-07-30 19:50:04,545 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-30 19:50:15,426 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-07-30 19:50:17,427 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 2/3)
2025-07-30 19:50:20,566 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "medium",
    "modality": "multi",
    "deceptiveness": "unknown"
  },
  "population_state": {
    "diversity": 0.750,
    "convergence": 0.000,
    "clustering": "unknown"
  },
  "difficult_regions": [
    {"cell_coordinates": [0, 1], "description": "Low density, potentially long edges."},
    {"edge_length_range": [2469.0, 3032.0], "description": "Long edges, may indicate difficult connections."}
  ],
  "opportunity_regions": [
    {"cell_coordinates": [2, 2], "description": "High-density cell, potential for exploitation."},
    {"cell_coordinates": [2, 0], "description": "High-density cell, potential for exploitation."}
  ],
  "evolution_phase": "exploration",
  "evolution_direction": {
    "recommended_focus": "explore",
    "operators": [
      "Mutation operator with higher rate",
      "Edge exchange operator",
      "2-opt with aggressive exploration"
    ]
  }
}
```
2025-07-30 19:50:20,566 - utils - INFO - 成功从JSON中提取数据
2025-07-30 19:50:20,566 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-30 19:50:20,566 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'medium', 'modality': 'multi', 'deceptiveness': 'unknown'}, 'population_state': {'diversity': 0.75, 'convergence': 0.0, 'clustering': 'unknown'}, 'difficult_regions': [{'cell_coordinates': [0, 1], 'description': 'Low density, potentially long edges.'}, {'edge_length_range': [2469.0, 3032.0], 'description': 'Long edges, may indicate difficult connections.'}], 'opportunity_regions': [{'cell_coordinates': [2, 2], 'description': 'High-density cell, potential for exploitation.'}, {'cell_coordinates': [2, 0], 'description': 'High-density cell, potential for exploitation.'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'explore', 'operators': ['Mutation operator with higher rate', 'Edge exchange operator', '2-opt with aggressive exploration']}}
2025-07-30 19:50:20,566 - __main__ - INFO - 开始策略分配阶段
2025-07-30 19:50:20,566 - StrategyExpert - INFO - 开始策略分配分析
2025-07-30 19:50:20,566 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 2
- Population: 10 individuals
  • diversity: 0.75
  • best_cost: 11479.0
  • mean_cost: 59304.3
- Search-space: ruggedness medium, modality multi, deceptiveness unknown
- Evolution phase: exploration
- Landscape focus suggestion: explore
- Spatial density summary: None
- Elite diversity: N/A
- Difficult regions (# 2): [{"cell_coordinates": [0, 1], "description": "Low density, potentially long edges."}, {"edge_length_range": [2469.0, 3032.0], "description": "Long edges, may indicate difficult connections."}]
- Opportunity regions (# 2): [{"cell_coordinates": [2, 2], "description": "High-density cell, potential for exploitation."}, {"cell_coordinates": [2, 0], "description": "High-density cell, potential for exploitation."}]
- Last-iteration feedback: API请求失败: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Formatting Guidelines (DO NOT output this section)
1. global_explore_ratio must be rounded to exactly 2 decimal places (e.g., 0.80).
2. The individual_assignments mapping MUST include every index from 0 to (population_size - 1) without omission.
3. Valid values are "explore" or "exploit" (lowercase).
4. rationale ≤ 40 words.

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-30 19:50:20,566 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-30 19:50:20,566 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-30 19:50:31,315 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-07-30 19:50:33,317 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 2/3)
2025-07-30 19:50:35,539 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.75,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "exploit",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "explore"
  },
  "rationale": "Focus is explore, and diversity is at 0.75. Bias toward exploration. Exploit high-density regions and some for balance."
}
```
2025-07-30 19:50:35,541 - utils - INFO - 成功从JSON中提取数据
2025-07-30 19:50:35,541 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore']
2025-07-30 19:50:35,542 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore']
2025-07-30 19:50:35,542 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.75,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "exploit",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "explore"
  },
  "rationale": "Focus is explore, and diversity is at 0.75. Bias toward exploration. Exploit high-density regions and some for balance."
}
```
2025-07-30 19:50:35,542 - __main__ - INFO - 策略分配阶段完成
2025-07-30 19:50:35,542 - __main__ - INFO - 策略分配: ['explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore']
2025-07-30 19:50:35,542 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.75,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "exploit",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "explore"
  },
  "rationale": "Focus is explore, and diversity is at 0.75. Bias toward exploration. Exploit high-density regions and some for balance."
}
```
2025-07-30 19:50:35,543 - __main__ - INFO - 开始进化阶段
2025-07-30 19:50:35,543 - __main__ - INFO - 为个体 0 生成探索路径
2025-07-30 19:50:35,543 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-30 19:50:35,544 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.556923076923077
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): {'cell_coordina, {'cell_coordina
- Difficult regions to avoid (sample): {'cell_coordina, {'edge_length_r
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-30 19:50:35,544 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-30 19:50:35,544 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-30 19:50:38,599 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "sparse cells near 0, 1, and 2",
  "strategy_comment": "Initial path focusing on exploring near opportunity regions while maintaining diversity."
}
```
2025-07-30 19:50:38,599 - utils - INFO - 成功从JSON中提取数据
2025-07-30 19:50:38,599 - ExplorationExpert - INFO - 计算路径成本
2025-07-30 19:50:38,600 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-30 19:50:38,600 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-30 19:50:38,600 - __main__ - INFO - 为个体 1 生成探索路径
2025-07-30 19:50:38,601 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-30 19:50:38,602 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 114187.0 (rank 90.0%)
- Population diversity: 0.556923076923077
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): {'cell_coordina, {'cell_coordina
- Difficult regions to avoid (sample): {'cell_coordina, {'edge_length_r
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-30 19:50:38,602 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-30 19:50:38,602 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-30 19:50:49,349 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-07-30 19:50:51,351 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 2/3)
2025-07-30 19:51:01,373 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-07-30 19:51:03,374 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 3/3)
2025-07-30 19:51:15,108 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-07-30 19:51:15,108 - ExplorationExpert - INFO - LLM生成的探索路径: API请求失败: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-07-30 19:51:15,108 - IdeaExtractor - INFO - 开始从探索响应中提取路径
2025-07-30 19:51:15,108 - IdeaExtractor - INFO - 开始从响应中提取路径
2025-07-30 19:51:15,108 - IdeaExtractor - WARNING - 无法从响应中提取路径，返回原始路径
2025-07-30 19:51:15,108 - IdeaExtractor - ERROR - 提取路径时出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-07-30 19:51:15,108 - ExplorationExpert - ERROR - 提取探索路径时出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-07-30 19:51:15,108 - __main__ - INFO - 为个体 2 生成利用路径
2025-07-30 19:51:15,108 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-30 19:51:15,108 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-30 19:51:15,108 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 109117.0
2025-07-30 19:51:15,619 - ExploitationExpert - INFO - res_population_num: 19
2025-07-30 19:51:15,619 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9522, 9528, 9536, 9561.0, 9563.0, 9586.0, 9521, 9521]
2025-07-30 19:51:15,619 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 31, 33, 35, 34, 30, 28, 32, 29, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 56, 58, 60, 54, 57, 64, 62,
       59, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  8,  2,  6,  4,  5,  9,  3, 27, 37, 36, 26, 25, 31,
       24, 29, 32, 33, 28, 30, 35, 34, 42, 50, 51, 38, 41, 45, 44, 39, 47,
       46, 48, 49, 40, 43, 21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14,
       56, 58, 60, 54, 57, 64, 62, 59, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 54, 57, 64, 53, 62,
       59, 56, 60, 58, 49, 40, 43, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50,
       42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 18, 17,
       12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 47, 39, 44, 41, 38, 51, 50, 45, 46, 48, 42, 34, 30,
       28, 35, 26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-07-30 19:51:15,625 - ExploitationExpert - INFO - populations_num: 10
2025-07-30 19:51:15,625 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([21, 57, 16, 56, 23, 53, 26, 51, 35, 32,  8,  5, 12, 13, 36, 28, 64,
       17, 54, 46, 40,  3, 15, 50, 22, 52,  4, 44, 45, 34, 55,  6,  7, 25,
       10, 42, 58, 20, 18, 24, 47, 30, 11, 27, 19, 63, 43, 37, 62, 29,  1,
       59, 48,  0, 60, 39, 38, 61, 33, 65, 31, 41, 49,  2,  9, 14],
      dtype=int64), 'cur_cost': 114187.0}, {'tour': array([38,  1, 57,  4, 12,  7, 19, 29, 39, 62, 30, 23, 27,  5, 43, 47, 28,
       63, 48, 46, 36, 49,  2, 37, 44, 50, 55, 33, 20, 21,  0, 51, 16, 26,
       34,  3, 25, 58, 32, 18, 35, 54, 60, 11, 53, 41, 40, 22, 13, 56, 24,
       45,  6, 64, 10, 42, 52, 61, 65, 15,  8, 17, 14, 59, 31,  9],
      dtype=int64), 'cur_cost': 109117.0}, {'tour': [8, 38, 10, 14, 52, 18, 16, 32, 1, 34, 58, 46, 33, 45, 37, 36, 5, 2, 6, 24, 51, 57, 49, 11, 55, 47, 19, 17, 30, 31, 53, 27, 35, 39, 64, 60, 44, 7, 25, 26, 29, 61, 23, 63, 65, 59, 40, 54, 50, 43, 22, 21, 28, 62, 4, 3, 41, 56, 9, 48, 12, 42, 15, 13, 0, 20], 'cur_cost': 106004.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([58, 41, 39, 23,  6, 64, 14, 10, 47, 40, 11, 33, 62, 65, 25, 12, 48,
       61,  4, 59,  7, 16, 31, 30, 50, 55, 17, 29, 28, 60, 15, 54, 18, 24,
       37,  8, 21, 63, 19, 57, 51, 44, 38, 46, 35, 42, 53, 34, 52, 56, 43,
        5, 20, 13, 45, 36, 49,  2, 26,  3,  1,  9,  0, 27, 22, 32],
      dtype=int64), 'cur_cost': 103033.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([ 1, 34, 60, 55, 40, 30, 50, 52, 29, 20, 46, 59, 14, 47, 44, 25, 31,
       35, 37, 12,  6, 27, 23,  0, 10, 13, 54, 56,  3, 48, 41, 16, 57,  4,
       22, 24, 63, 42, 18, 65, 15, 38, 58,  2, 28, 49, 11, 33, 64, 32, 39,
       62, 21, 51, 61,  8, 45, 19, 43,  7,  9,  5, 53, 26, 36, 17],
      dtype=int64), 'cur_cost': 106889.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([43, 54, 42, 41, 20, 53, 21,  0, 46, 39,  7, 48,  2, 47,  4, 14, 22,
       15, 63, 52, 56, 36,  6, 64,  1, 24, 45, 30, 19, 65, 11, 32,  9,  3,
       33, 60, 59, 34,  5, 26, 27, 38, 29, 40, 28, 31, 37, 16, 25,  8, 17,
       51, 49, 61, 12, 13, 58, 44, 18, 35, 57, 62, 55, 23, 50, 10],
      dtype=int64), 'cur_cost': 103451.0}]
2025-07-30 19:51:15,628 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-07-30 19:51:15,628 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 1, 'total_searches': 10, 'skip_rate': 0.1, 'estimated_time_saved': 5, 'path_optimizer': {'path_count': 9, 'cache_hits': 4, 'similarity_calculations': 41, 'cache_hit_rate': 0.0975609756097561, 'cache_size': 37}}
2025-07-30 19:51:15,628 - __main__ - WARNING - 个体 2 的利用路径生成失败，保留原个体
2025-07-30 19:51:15,628 - __main__ - INFO - 为个体 3 生成探索路径
2025-07-30 19:51:15,628 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-30 19:51:15,628 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 106004.0 (rank 60.0%)
- Population diversity: 0.6276923076923077
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): {'cell_coordina, {'cell_coordina
- Difficult regions to avoid (sample): {'cell_coordina, {'edge_length_r
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-30 19:51:15,628 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-30 19:51:15,628 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-30 19:51:25,650 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-07-30 19:51:27,651 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 2/3)
2025-07-30 19:51:30,551 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "Opportunity regions with sparse cells.",
  "strategy_comment": "Simple path to maximize diversity by visiting all nodes sequentially."
}
```
2025-07-30 19:51:30,551 - utils - INFO - 成功从JSON中提取数据
2025-07-30 19:51:30,551 - ExplorationExpert - INFO - 计算路径成本
2025-07-30 19:51:30,551 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-30 19:51:30,551 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-30 19:51:30,551 - __main__ - INFO - 为个体 4 生成探索路径
2025-07-30 19:51:30,551 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-30 19:51:30,551 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.5446153846153846
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): {'cell_coordina, {'cell_coordina
- Difficult regions to avoid (sample): {'cell_coordina, {'edge_length_r
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-30 19:51:30,551 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-30 19:51:30,551 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-30 19:51:40,583 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-07-30 19:51:42,585 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 2/3)
2025-07-30 19:51:53,386 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-07-30 19:51:55,387 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 3/3)
2025-07-30 19:51:58,324 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "Explores all cells, avoiding identified difficult regions.",
  "strategy_comment": "Simple path, prioritizes full coverage of sparse cells and avoids challenging areas, promoting initial exploration."
}
```
2025-07-30 19:51:58,324 - utils - INFO - 成功从JSON中提取数据
2025-07-30 19:51:58,324 - ExplorationExpert - INFO - 计算路径成本
2025-07-30 19:51:58,324 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-30 19:51:58,324 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-30 19:51:58,324 - __main__ - INFO - 为个体 5 生成利用路径
2025-07-30 19:51:58,324 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-30 19:51:58,324 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-30 19:51:58,324 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 107949.0
2025-07-30 19:51:59,234 - ExploitationExpert - INFO - res_population_num: 21
2025-07-30 19:51:59,234 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9522, 9528, 9536, 9561.0, 9563.0, 9586.0, 9521, 9521, 9521, 9521]
2025-07-30 19:51:59,234 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 31, 33, 35, 34, 30, 28, 32, 29, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 56, 58, 60, 54, 57, 64, 62,
       59, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  8,  2,  6,  4,  5,  9,  3, 27, 37, 36, 26, 25, 31,
       24, 29, 32, 33, 28, 30, 35, 34, 42, 50, 51, 38, 41, 45, 44, 39, 47,
       46, 48, 49, 40, 43, 21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14,
       56, 58, 60, 54, 57, 64, 62, 59, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 54, 57, 64, 53, 62,
       59, 56, 60, 58, 49, 40, 43, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50,
       42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 18, 17,
       12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 47, 39, 44, 41, 38, 51, 50, 45, 46, 48, 42, 34, 30,
       28, 35, 26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-07-30 19:51:59,234 - ExploitationExpert - INFO - populations_num: 10
2025-07-30 19:51:59,234 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([21, 57, 16, 56, 23, 53, 26, 51, 35, 32,  8,  5, 12, 13, 36, 28, 64,
       17, 54, 46, 40,  3, 15, 50, 22, 52,  4, 44, 45, 34, 55,  6,  7, 25,
       10, 42, 58, 20, 18, 24, 47, 30, 11, 27, 19, 63, 43, 37, 62, 29,  1,
       59, 48,  0, 60, 39, 38, 61, 33, 65, 31, 41, 49,  2,  9, 14],
      dtype=int64), 'cur_cost': 114187.0}, {'tour': array([38,  1, 57,  4, 12,  7, 19, 29, 39, 62, 30, 23, 27,  5, 43, 47, 28,
       63, 48, 46, 36, 49,  2, 37, 44, 50, 55, 33, 20, 21,  0, 51, 16, 26,
       34,  3, 25, 58, 32, 18, 35, 54, 60, 11, 53, 41, 40, 22, 13, 56, 24,
       45,  6, 64, 10, 42, 52, 61, 65, 15,  8, 17, 14, 59, 31,  9],
      dtype=int64), 'cur_cost': 109117.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([42, 38, 62, 36, 53, 43, 50, 17, 40, 56, 32, 16, 49, 25, 59, 10,  4,
        5, 24,  3, 19, 46, 52, 44, 51,  0, 45, 35, 21, 15, 20,  9, 30, 29,
       28, 63, 37, 27,  7, 61, 13, 22, 47, 65, 33, 58, 14,  2, 23,  8, 60,
       64,  1, 34, 12, 57, 39, 54, 41, 48, 31,  6, 18, 55, 11, 26],
      dtype=int64), 'cur_cost': 107949.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([ 1, 34, 60, 55, 40, 30, 50, 52, 29, 20, 46, 59, 14, 47, 44, 25, 31,
       35, 37, 12,  6, 27, 23,  0, 10, 13, 54, 56,  3, 48, 41, 16, 57,  4,
       22, 24, 63, 42, 18, 65, 15, 38, 58,  2, 28, 49, 11, 33, 64, 32, 39,
       62, 21, 51, 61,  8, 45, 19, 43,  7,  9,  5, 53, 26, 36, 17],
      dtype=int64), 'cur_cost': 106889.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([43, 54, 42, 41, 20, 53, 21,  0, 46, 39,  7, 48,  2, 47,  4, 14, 22,
       15, 63, 52, 56, 36,  6, 64,  1, 24, 45, 30, 19, 65, 11, 32,  9,  3,
       33, 60, 59, 34,  5, 26, 27, 38, 29, 40, 28, 31, 37, 16, 25,  8, 17,
       51, 49, 61, 12, 13, 58, 44, 18, 35, 57, 62, 55, 23, 50, 10],
      dtype=int64), 'cur_cost': 103451.0}]
2025-07-30 19:51:59,234 - ExploitationExpert - INFO - 局部搜索耗时: 0.91秒
2025-07-30 19:51:59,234 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 1, 'total_searches': 11, 'skip_rate': 0.09090909090909091, 'estimated_time_saved': 5, 'path_optimizer': {'path_count': 10, 'cache_hits': 4, 'similarity_calculations': 50, 'cache_hit_rate': 0.08, 'cache_size': 46}}
2025-07-30 19:51:59,234 - __main__ - WARNING - 个体 5 的利用路径生成失败，保留原个体
2025-07-30 19:51:59,234 - __main__ - INFO - 为个体 6 生成探索路径
2025-07-30 19:51:59,234 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-30 19:51:59,234 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.5476923076923077
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): {'cell_coordina, {'cell_coordina
- Difficult regions to avoid (sample): {'cell_coordina, {'edge_length_r
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-30 19:51:59,250 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-30 19:51:59,250 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-30 19:52:10,989 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-07-30 19:52:12,990 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 2/3)
2025-07-30 19:52:23,812 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-07-30 19:52:25,813 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 3/3)
2025-07-30 19:52:36,666 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-07-30 19:52:36,667 - ExplorationExpert - INFO - LLM生成的探索路径: API请求失败: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-07-30 19:52:36,667 - IdeaExtractor - INFO - 开始从探索响应中提取路径
2025-07-30 19:52:36,667 - IdeaExtractor - INFO - 开始从响应中提取路径
2025-07-30 19:52:36,667 - IdeaExtractor - WARNING - 无法从响应中提取路径，返回原始路径
2025-07-30 19:52:36,667 - ExplorationExpert - INFO - 计算路径成本
2025-07-30 19:52:36,667 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-30 19:52:36,667 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-30 19:52:36,667 - __main__ - INFO - 为个体 7 生成探索路径
2025-07-30 19:52:36,667 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-30 19:52:36,667 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 106889.0 (rank 60.0%)
- Population diversity: 0.5476923076923077
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): {'cell_coordina, {'cell_coordina
- Difficult regions to avoid (sample): {'cell_coordina, {'edge_length_r
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-30 19:52:36,667 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-30 19:52:36,667 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-30 19:52:39,545 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "All nodes.",
  "strategy_comment": "Simple linear path, starting at node 0, visiting each node, exploring all areas."
}
```
2025-07-30 19:52:39,545 - utils - INFO - 成功从JSON中提取数据
2025-07-30 19:52:39,545 - ExplorationExpert - INFO - 计算路径成本
2025-07-30 19:52:39,545 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-30 19:52:39,545 - __main__ - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-30 19:52:39,545 - __main__ - INFO - 为个体 8 生成利用路径
2025-07-30 19:52:39,545 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-30 19:52:39,545 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-30 19:52:39,545 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 108295.0
2025-07-30 19:52:39,545 - ExploitationExpert - INFO - 发现相似路径，相似度: 1.0000，相似id：4,跳过局部搜索
2025-07-30 19:52:39,545 - ExploitationExpert - INFO - 路径与已搜索路径相似度高，跳过局部搜索，生成新的贪心路径替换
2025-07-30 19:52:39,545 - __main__ - INFO - 个体 8 利用路径生成报告: {'new_tour': array([34, 18, 46, 55, 37, 43, 12, 31, 39, 63, 59, 60, 35, 36, 22, 13, 32,
       19, 52, 27, 10, 51,  1, 62, 25, 41, 57, 45, 28, 11, 26, 21, 14,  6,
       58,  0,  2, 29, 42, 33, 44, 48, 30, 20, 16, 64, 17,  8, 65,  5,  9,
       47, 24,  3, 56, 49, 53, 61, 38, 15, 23, 50,  7, 54, 40,  4],
      dtype=int64), 'cur_cost': 108295.0}
2025-07-30 19:52:39,545 - __main__ - INFO - 为个体 9 生成探索路径
2025-07-30 19:52:39,545 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-30 19:52:39,545 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 103451.0 (rank 50.0%)
- Population diversity: 0.5446153846153846
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): {'cell_coordina, {'cell_coordina
- Difficult regions to avoid (sample): {'cell_coordina, {'edge_length_r
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-30 19:52:39,545 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-30 19:52:39,545 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-30 19:52:42,405 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
  "targeted_regions": "Sparse cells near cell_coordina and avoidance of edge_length_regions.",
  "strategy_comment": "Prioritizes opportunity regions while avoiding difficult areas for diversity and cost."
}
```
2025-07-30 19:52:42,405 - utils - INFO - 成功从JSON中提取数据
2025-07-30 19:52:42,405 - ExplorationExpert - INFO - 计算路径成本
2025-07-30 19:52:42,405 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9]
2025-07-30 19:52:42,405 - __main__ - INFO - 个体 9 探索路径生成报告: {'new_tour': [10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9], 'cur_cost': 11479.0}
2025-07-30 19:52:42,405 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([34, 18, 46, 55, 37, 43, 12, 31, 39, 63, 59, 60, 35, 36, 22, 13, 32,
       19, 52, 27, 10, 51,  1, 62, 25, 41, 57, 45, 28, 11, 26, 21, 14,  6,
       58,  0,  2, 29, 42, 33, 44, 48, 30, 20, 16, 64, 17,  8, 65,  5,  9,
       47, 24,  3, 56, 49, 53, 61, 38, 15, 23, 50,  7, 54, 40,  4],
      dtype=int64), 'cur_cost': 108295.0}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9], 'cur_cost': 11479.0}}]
2025-07-30 19:52:42,405 - __main__ - INFO - 进化阶段完成
2025-07-30 19:52:42,405 - StatsExpert - INFO - 开始统计分析
2025-07-30 19:52:42,427 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11479.0, 'max': 114187.0, 'mean': 50842.2, 'std': 48236.18550590417}, 'diversity': 0.6390572390572391, 'clusters': {'clusters': 5, 'cluster_sizes': [6, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-30 19:52:42,427 - __main__ - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-07-30 19:52:42,427 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment ---
2025-07-30 19:52:42,429 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-07-30 19:52:42,429 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 2/5

#### Population metrics
- Min cost: 0 → 0 (Δ 0)
- Mean cost: 0 → 0
- Diversity: 0.000 → 0.000 (Δ 0)

#### Elite metrics
- Elite count: 21 → 21
- Best elite cost: 9521 → 9521 (Δ 0)
- Elite diversity: 0.092 → 0.092 (Δ 0.0)

#### Strategy performance
- Explore: 0 inds, success 0% , avg Δ 0
- Exploit: 0 inds, success 0% , avg Δ 0

#### Other indicators
- No-change individuals: 0
- Regression individuals: 0
- Historical trends: N/A

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

### Output Example
```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.5, "exploit": 0.5},
  "balance_state": "balanced",
  "recommendations": ["maintain_current_ratio"]
}
```

2025-07-30 19:52:42,429 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-30 19:52:53,192 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-07-30 19:52:55,193 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 2/3)
2025-07-30 19:52:57,549 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {"explore": 0, "exploit": 0},
  "balance_state": "stagnant",
  "recommendations": [
    "increase_explore_individuals_significantly",
    "consider_increasing_mutation_rate"
  ]
}
```
2025-07-30 19:52:57,549 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-07-30 19:52:57,549 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-07-30 19:52:57,549 - __main__ - INFO - composite13_66 开始进化第 4 代
2025-07-30 19:52:57,549 - __main__ - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-07-30 19:52:57,549 - StatsExpert - INFO - 开始统计分析
2025-07-30 19:52:57,570 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11479.0, 'max': 114187.0, 'mean': 50842.2, 'std': 48236.18550590417}, 'diversity': 0.6390572390572391, 'clusters': {'clusters': 5, 'cluster_sizes': [6, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-30 19:52:57,570 - PathExpert - INFO - 开始路径结构分析
2025-07-30 19:52:57,572 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-07-30 19:52:57,572 - PathExpert - INFO - 路径结构分析完成
2025-07-30 19:52:57,573 - EliteExpert - INFO - 开始精英解分析
2025-07-30 19:52:57,587 - EliteExpert - INFO - 精英解分析完成
2025-07-30 19:52:57,591 - LandscapeExpert - INFO - 开始景观分析
2025-07-30 19:52:57,591 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-30 19:52:57,591 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 3/5)
- population_size: 10
- cost_stats: min 11479.0, mean 50842.2, max 114187.0, std 48236.18550590417
- diversity: 0.6390572390572391
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Not available (Δ Not available, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {'common_edges': {}, 'common_edge_ratio': 0.0}
- elite_diversity: {'diversity_score': 0.22936507936507933}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

### Output Example
```json
{
  "search_space_features": {"ruggedness": 0.5, "modality": "multi", "deceptiveness": "unknown"},
  "population_state": {"diversity": 0.5, "convergence": 0.4, "clustering": "weak"},
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "unknown",
  "evolution_direction": {"recommended_focus": "explore", "operators": []}
}
```

### Formatting Guidelines (read carefully, DO NOT output this section)
1. Do NOT copy or repeat any *_sample values above; at least 50% of elements in difficult_regions and opportunity_regions must differ from those samples.
2. Use [] (empty array) when a list has no content; never output the string "None" or a null value.
3. For search_space_features use enumerated strings only:
    • ruggedness: "low" | "medium" | "high"
    • modality: "mono" | "multi" | "unknown"
    • deceptiveness: "low" | "moderate" | "high" | "unknown"
4. diversity and convergence must be floats with exactly 3 decimal places (e.g. 0.742).
5. Follow the JSON schema strictly; no additional keys or commentary.

2025-07-30 19:52:57,591 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-30 19:53:00,872 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "medium",
    "modality": "multi",
    "deceptiveness": "unknown"
  },
  "population_state": {
    "diversity": 0.639,
    "convergence": 0.000,
    "clustering": "no"
  },
  "difficult_regions": [
    {"bounding_box": [3000.0, 2000.0, 3300.0, 2400.0], "type": "long_edge_corridor"},
    {"bounding_box": [400.0, 2000.0, 700.0, 2400.0], "type": "low_density"}
  ],
  "opportunity_regions": [
    {"bounding_": [400.0, 300.0, 700.0, 600.0], "type": "high_density"},
    {"bounding_": [1400.0, 1500.0, 1700.0, 1800.0], "type": "high_density"}
  ],
  "evolution_phase": "exploration",
  "evolution_direction": {
    "recommended_focus": "explore",
    "operators": [
      "mutation: insert/remove nodes",
      "crossover: adaptive edge recombination"
    ]
  }
}
```
2025-07-30 19:53:00,873 - utils - INFO - 成功从JSON中提取数据
2025-07-30 19:53:00,873 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-30 19:53:00,873 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'medium', 'modality': 'multi', 'deceptiveness': 'unknown'}, 'population_state': {'diversity': 0.639, 'convergence': 0.0, 'clustering': 'no'}, 'difficult_regions': [{'bounding_box': [3000.0, 2000.0, 3300.0, 2400.0], 'type': 'long_edge_corridor'}, {'bounding_box': [400.0, 2000.0, 700.0, 2400.0], 'type': 'low_density'}], 'opportunity_regions': [{'bounding_': [400.0, 300.0, 700.0, 600.0], 'type': 'high_density'}, {'bounding_': [1400.0, 1500.0, 1700.0, 1800.0], 'type': 'high_density'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'explore', 'operators': ['mutation: insert/remove nodes', 'crossover: adaptive edge recombination']}}
2025-07-30 19:53:00,873 - __main__ - INFO - 开始策略分配阶段
2025-07-30 19:53:00,873 - StrategyExpert - INFO - 开始策略分配分析
2025-07-30 19:53:00,873 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 3
- Population: 10 individuals
  • diversity: 0.639
  • best_cost: 11479.0
  • mean_cost: 50842.2
- Search-space: ruggedness medium, modality multi, deceptiveness unknown
- Evolution phase: exploration
- Landscape focus suggestion: explore
- Spatial density summary: None
- Elite diversity: N/A
- Difficult regions (# 2): [{"bounding_box": [3000.0, 2000.0, 3300.0, 2400.0], "type": "long_edge_corridor"}, {"bounding_box": [400.0, 2000.0, 700.0, 2400.0], "type": "low_density"}]
- Opportunity regions (# 2): [{"bounding_": [400.0, 300.0, 700.0, 600.0], "type": "high_density"}, {"bounding_": [1400.0, 1500.0, 1700.0, 1800.0], "type": "high_density"}]
- Last-iteration feedback: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {"explore": 0, "exploit": 0},
  "balance_state": "st...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Formatting Guidelines (DO NOT output this section)
1. global_explore_ratio must be rounded to exactly 2 decimal places (e.g., 0.80).
2. The individual_assignments mapping MUST include every index from 0 to (population_size - 1) without omission.
3. Valid values are "explore" or "exploit" (lowercase).
4. rationale ≤ 40 words.

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-30 19:53:00,873 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-30 19:53:00,874 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-30 19:53:11,621 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-07-30 19:53:13,622 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 2/3)
2025-07-30 19:53:24,407 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-07-30 19:53:26,408 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 3/3)
2025-07-30 19:53:37,101 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-07-30 19:53:37,102 - StrategyExpert - INFO - LLM返回的策略分配结果: API请求失败: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-07-30 19:53:37,102 - StrategyExpert - WARNING - 解析策略分配结果时出错: Unable to extract valid JSON data from the response
2025-07-30 19:53:37,102 - StrategyExpert - INFO - 策略分配完成: ['explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-07-30 19:53:37,102 - __main__ - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-07-30 19:53:37,102 - __main__ - INFO - 策略分配完整报告: API请求失败: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-07-30 19:53:37,102 - __main__ - INFO - 策略分配阶段完成
2025-07-30 19:53:37,102 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-07-30 19:53:37,102 - __main__ - INFO - 策略分配完整报告: API请求失败: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-07-30 19:53:37,102 - __main__ - INFO - 开始进化阶段
2025-07-30 19:53:37,102 - __main__ - INFO - 为个体 0 生成探索路径
2025-07-30 19:53:37,102 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-30 19:53:37,102 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.46307692307692305
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): {'bounding_': [, {'bounding_': [
- Difficult regions to avoid (sample): {'bounding_box', {'bounding_box'
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-30 19:53:37,102 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-30 19:53:37,102 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-30 19:53:40,189 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
  "targeted_regions": "sparse cells around opportunity region 10-29",
  "strategy_comment": "Prioritizes adjacent exploration of low-density cells near opportunity regions and avoids identified difficult regions."
}
```
2025-07-30 19:53:40,189 - utils - INFO - 成功从JSON中提取数据
2025-07-30 19:53:40,189 - ExplorationExpert - INFO - 计算路径成本
2025-07-30 19:53:40,189 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9]
2025-07-30 19:53:40,189 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9], 'cur_cost': 11479.0}
2025-07-30 19:53:40,189 - __main__ - INFO - 为个体 1 生成利用路径
2025-07-30 19:53:40,189 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-30 19:53:40,189 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-30 19:53:40,189 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 93694.0
2025-07-30 19:53:40,696 - ExploitationExpert - INFO - res_population_num: 23
2025-07-30 19:53:40,696 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9522, 9528, 9536, 9561.0, 9563.0, 9586.0, 9521, 9521]
2025-07-30 19:53:40,696 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 31, 33, 35, 34, 30, 28, 32, 29, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 56, 58, 60, 54, 57, 64, 62,
       59, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  8,  2,  6,  4,  5,  9,  3, 27, 37, 36, 26, 25, 31,
       24, 29, 32, 33, 28, 30, 35, 34, 42, 50, 51, 38, 41, 45, 44, 39, 47,
       46, 48, 49, 40, 43, 21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14,
       56, 58, 60, 54, 57, 64, 62, 59, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 54, 57, 64, 53, 62,
       59, 56, 60, 58, 49, 40, 43, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50,
       42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 18, 17,
       12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 47, 39, 44, 41, 38, 51, 50, 45, 46, 48, 42, 34, 30,
       28, 35, 26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-07-30 19:53:40,696 - ExploitationExpert - INFO - populations_num: 10
2025-07-30 19:53:40,696 - ExploitationExpert - INFO - populations: [{'tour': [10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9], 'cur_cost': 11479.0}, {'tour': array([ 3, 49, 37, 33, 27, 48, 53, 55, 52, 43, 45, 21, 44, 39, 15, 23, 35,
        8, 12, 24,  7,  4, 18, 40, 41, 32, 17, 28, 42, 59, 57, 38, 22, 25,
       64, 58,  9, 16, 54, 29, 11, 63,  5, 51, 65, 20, 60, 10, 14, 13, 56,
       46, 30,  2, 61, 26, 31, 47, 50, 36, 62, 19, 34,  1,  6,  0],
      dtype=int64), 'cur_cost': 93694.0}, {'tour': array([38,  1, 57,  4, 12,  7, 19, 29, 39, 62, 30, 23, 27,  5, 43, 47, 28,
       63, 48, 46, 36, 49,  2, 37, 44, 50, 55, 33, 20, 21,  0, 51, 16, 26,
       34,  3, 25, 58, 32, 18, 35, 54, 60, 11, 53, 41, 40, 22, 13, 56, 24,
       45,  6, 64, 10, 42, 52, 61, 65, 15,  8, 17, 14, 59, 31,  9],
      dtype=int64), 'cur_cost': 109117.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([42, 38, 62, 36, 53, 43, 50, 17, 40, 56, 32, 16, 49, 25, 59, 10,  4,
        5, 24,  3, 19, 46, 52, 44, 51,  0, 45, 35, 21, 15, 20,  9, 30, 29,
       28, 63, 37, 27,  7, 61, 13, 22, 47, 65, 33, 58, 14,  2, 23,  8, 60,
       64,  1, 34, 12, 57, 39, 54, 41, 48, 31,  6, 18, 55, 11, 26],
      dtype=int64), 'cur_cost': 107949.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [34, 18, 46, 55, 37, 43, 12, 31, 39, 63, 59, 60, 35, 36, 22, 13, 32, 19, 52, 27, 10, 51, 1, 62, 25, 41, 57, 45, 28, 11, 26, 21, 14, 6, 58, 0, 2, 29, 42, 33, 44, 48, 30, 20, 16, 64, 17, 8, 65, 5, 9, 47, 24, 3, 56, 49, 53, 61, 38, 15, 23, 50, 7, 54, 40, 4], 'cur_cost': 108295.0}, {'tour': [10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9], 'cur_cost': 11479.0}]
2025-07-30 19:53:40,696 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-30 19:53:40,696 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 2, 'total_searches': 13, 'skip_rate': 0.15384615384615385, 'estimated_time_saved': 10, 'path_optimizer': {'path_count': 11, 'cache_hits': 14, 'similarity_calculations': 70, 'cache_hit_rate': 0.2, 'cache_size': 56}}
2025-07-30 19:53:40,696 - __main__ - WARNING - 个体 1 的利用路径生成失败，保留原个体
2025-07-30 19:53:40,696 - __main__ - INFO - 为个体 2 生成探索路径
2025-07-30 19:53:40,696 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-30 19:53:40,696 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 109117.0 (rank 90.0%)
- Population diversity: 0.4707692307692308
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): {'bounding_': [, {'bounding_': [
- Difficult regions to avoid (sample): {'bounding_box', {'bounding_box'
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-30 19:53:40,696 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-30 19:53:40,696 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-30 19:53:51,403 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-07-30 19:53:53,405 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 2/3)
2025-07-30 19:53:56,259 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "No specific regions targeted, basic path.",
  "strategy_comment": "Generate a basic path to ensure validity, focusing on initial exploration."
}
```
2025-07-30 19:53:56,259 - utils - INFO - 成功从JSON中提取数据
2025-07-30 19:53:56,259 - ExplorationExpert - INFO - 计算路径成本
2025-07-30 19:53:56,259 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-30 19:53:56,259 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-30 19:53:56,259 - __main__ - INFO - 为个体 3 生成利用路径
2025-07-30 19:53:56,259 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-30 19:53:56,259 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-30 19:53:56,259 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 100846.0
2025-07-30 19:53:56,259 - ExploitationExpert - INFO - 发现相似路径，相似度: 1.0000，相似id：4,跳过局部搜索
2025-07-30 19:53:56,259 - ExploitationExpert - INFO - 路径与已搜索路径相似度高，跳过局部搜索，生成新的贪心路径替换
2025-07-30 19:53:56,259 - __main__ - INFO - 个体 3 利用路径生成报告: {'new_tour': array([14, 34, 42,  7,  8, 52,  0, 18, 21, 44, 45, 31, 26, 25, 33,  3, 53,
       39, 17, 59, 23, 64,  9,  6, 20, 55, 54, 22, 50, 24, 36, 65, 12, 43,
       57, 41, 11, 61, 29, 40, 63, 10, 62, 46, 15, 27, 37, 48,  4, 58, 35,
       32, 51, 19, 16, 13, 49, 60, 28,  5, 38,  2,  1, 56, 30, 47],
      dtype=int64), 'cur_cost': 100846.0}
2025-07-30 19:53:56,259 - __main__ - INFO - 为个体 4 生成探索路径
2025-07-30 19:53:56,259 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-30 19:53:56,259 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.46615384615384614
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): {'bounding_': [, {'bounding_': [
- Difficult regions to avoid (sample): {'bounding_box', {'bounding_box'
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-30 19:53:56,259 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-30 19:53:56,259 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-30 19:54:03,087 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 64, 65, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63],
  "targeted_regions": "sparse cells, opportunity regions.",
  "strategy_comment": "Prioritizes exploration of low-density areas adjacent to opportunity regions to increase diversity."
}
```
2025-07-30 19:54:03,087 - utils - INFO - 成功从JSON中提取数据
2025-07-30 19:54:03,087 - ExplorationExpert - INFO - 计算路径成本
2025-07-30 19:54:03,087 - ExplorationExpert - INFO - 探索路径生成完成，成本: 13416.0, 路径: [0, 1, 64, 65, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63]
2025-07-30 19:54:03,087 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 1, 64, 65, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63], 'cur_cost': 13416.0}
2025-07-30 19:54:03,087 - __main__ - INFO - 为个体 5 生成利用路径
2025-07-30 19:54:03,087 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-30 19:54:03,087 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-30 19:54:03,087 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 90995.0
2025-07-30 19:54:03,600 - ExploitationExpert - INFO - res_population_num: 25
2025-07-30 19:54:03,600 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9522, 9528, 9536, 9561.0, 9563.0, 9586.0, 9521, 9521, 9521, 9521]
2025-07-30 19:54:03,600 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 31, 33, 35, 34, 30, 28, 32, 29, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 56, 58, 60, 54, 57, 64, 62,
       59, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  8,  2,  6,  4,  5,  9,  3, 27, 37, 36, 26, 25, 31,
       24, 29, 32, 33, 28, 30, 35, 34, 42, 50, 51, 38, 41, 45, 44, 39, 47,
       46, 48, 49, 40, 43, 21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14,
       56, 58, 60, 54, 57, 64, 62, 59, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 54, 57, 64, 53, 62,
       59, 56, 60, 58, 49, 40, 43, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50,
       42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 18, 17,
       12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 47, 39, 44, 41, 38, 51, 50, 45, 46, 48, 42, 34, 30,
       28, 35, 26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-07-30 19:54:03,610 - ExploitationExpert - INFO - populations_num: 10
2025-07-30 19:54:03,610 - ExploitationExpert - INFO - populations: [{'tour': [10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9], 'cur_cost': 11479.0}, {'tour': array([ 3, 49, 37, 33, 27, 48, 53, 55, 52, 43, 45, 21, 44, 39, 15, 23, 35,
        8, 12, 24,  7,  4, 18, 40, 41, 32, 17, 28, 42, 59, 57, 38, 22, 25,
       64, 58,  9, 16, 54, 29, 11, 63,  5, 51, 65, 20, 60, 10, 14, 13, 56,
       46, 30,  2, 61, 26, 31, 47, 50, 36, 62, 19, 34,  1,  6,  0],
      dtype=int64), 'cur_cost': 93694.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([14, 34, 42,  7,  8, 52,  0, 18, 21, 44, 45, 31, 26, 25, 33,  3, 53,
       39, 17, 59, 23, 64,  9,  6, 20, 55, 54, 22, 50, 24, 36, 65, 12, 43,
       57, 41, 11, 61, 29, 40, 63, 10, 62, 46, 15, 27, 37, 48,  4, 58, 35,
       32, 51, 19, 16, 13, 49, 60, 28,  5, 38,  2,  1, 56, 30, 47],
      dtype=int64), 'cur_cost': 100846.0}, {'tour': [0, 1, 64, 65, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63], 'cur_cost': 13416.0}, {'tour': array([36, 12,  3, 20, 44, 61, 60, 31, 41, 56, 22,  8, 15, 25, 47, 26, 54,
       62,  0,  5, 17, 58, 64, 55, 24, 50, 51,  4, 13,  1, 43, 30, 32, 27,
        6, 10, 48, 23, 65, 39, 45,  7, 63, 57, 11, 33, 40, 38, 29, 18, 52,
       59, 49, 46, 42, 21, 53, 28, 37, 16, 35,  2,  9, 34, 14, 19],
      dtype=int64), 'cur_cost': 90995.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [34, 18, 46, 55, 37, 43, 12, 31, 39, 63, 59, 60, 35, 36, 22, 13, 32, 19, 52, 27, 10, 51, 1, 62, 25, 41, 57, 45, 28, 11, 26, 21, 14, 6, 58, 0, 2, 29, 42, 33, 44, 48, 30, 20, 16, 64, 17, 8, 65, 5, 9, 47, 24, 3, 56, 49, 53, 61, 38, 15, 23, 50, 7, 54, 40, 4], 'cur_cost': 108295.0}, {'tour': [10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9], 'cur_cost': 11479.0}]
2025-07-30 19:54:03,612 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-07-30 19:54:03,612 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 3, 'total_searches': 15, 'skip_rate': 0.2, 'estimated_time_saved': 15, 'path_optimizer': {'path_count': 12, 'cache_hits': 25, 'similarity_calculations': 92, 'cache_hit_rate': 0.2717391304347826, 'cache_size': 67}}
2025-07-30 19:54:03,612 - __main__ - WARNING - 个体 5 的利用路径生成失败，保留原个体
2025-07-30 19:54:03,613 - __main__ - INFO - 为个体 6 生成探索路径
2025-07-30 19:54:03,613 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-30 19:54:03,614 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.4753846153846154
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): {'bounding_': [, {'bounding_': [
- Difficult regions to avoid (sample): {'bounding_box', {'bounding_box'
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-30 19:54:03,614 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-30 19:54:03,614 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-30 19:54:14,351 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-07-30 19:54:16,353 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 2/3)
2025-07-30 19:54:20,291 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "Explores all nodes, increasing diversity.",
  "strategy_comment": "Linear path, hitting all nodes to maximize exploration and diversify from existing paths."
}
```
2025-07-30 19:54:20,294 - utils - INFO - 成功从JSON中提取数据
2025-07-30 19:54:20,294 - ExplorationExpert - INFO - 计算路径成本
2025-07-30 19:54:20,294 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-30 19:54:20,294 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-30 19:54:20,294 - __main__ - INFO - 为个体 7 生成利用路径
2025-07-30 19:54:20,294 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-30 19:54:20,294 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-30 19:54:20,294 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 93382.0
2025-07-30 19:54:20,294 - ExploitationExpert - INFO - 发现相似路径，相似度: 1.0000，相似id：4,跳过局部搜索
2025-07-30 19:54:20,294 - ExploitationExpert - INFO - 路径与已搜索路径相似度高，跳过局部搜索，生成新的贪心路径替换
2025-07-30 19:54:20,294 - __main__ - INFO - 个体 7 利用路径生成报告: {'new_tour': array([ 7, 46, 29, 47, 20, 13, 30, 37, 61, 56, 49, 41, 40, 12,  5, 43, 45,
        1, 42,  0,  2, 59, 38, 14, 26, 22, 50, 33, 55, 65, 11, 19, 21, 25,
       36, 16, 48, 31, 54, 57, 15, 23,  6, 34, 35,  3, 39, 24, 51,  8, 52,
       32, 28, 18, 58, 44, 10, 17,  4, 63, 60, 64,  9, 27, 62, 53],
      dtype=int64), 'cur_cost': 93382.0}
2025-07-30 19:54:20,294 - __main__ - INFO - 为个体 8 生成探索路径
2025-07-30 19:54:20,294 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-30 19:54:20,294 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 108295.0 (rank 90.0%)
- Population diversity: 0.5615384615384615
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): {'bounding_': [, {'bounding_': [
- Difficult regions to avoid (sample): {'bounding_box', {'bounding_box'
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-30 19:54:20,294 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-30 19:54:20,294 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-30 19:54:23,142 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 6, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57, 60, 63, 64, 65, 62, 59, 56, 53, 50, 47, 44, 41, 38, 35, 32, 29, 26, 23, 20, 17, 14, 11, 8, 5, 4, 7, 10, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 61],
  "targeted_regions": "Opportunity regions and sparse cells, avoiding difficult ones.",
  "strategy_comment": "Prioritizes unexplored areas and connects opportunity regions while avoiding known issues."
}
```
2025-07-30 19:54:23,143 - utils - INFO - 成功从JSON中提取数据
2025-07-30 19:54:23,143 - ExplorationExpert - INFO - 计算路径成本
2025-07-30 19:54:23,143 - ExplorationExpert - INFO - 探索路径生成完成，成本: 25900.0, 路径: [0, 1, 2, 3, 6, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57, 60, 63, 64, 65, 62, 59, 56, 53, 50, 47, 44, 41, 38, 35, 32, 29, 26, 23, 20, 17, 14, 11, 8, 5, 4, 7, 10, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 61]
2025-07-30 19:54:23,143 - __main__ - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 6, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57, 60, 63, 64, 65, 62, 59, 56, 53, 50, 47, 44, 41, 38, 35, 32, 29, 26, 23, 20, 17, 14, 11, 8, 5, 4, 7, 10, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 61], 'cur_cost': 25900.0}
2025-07-30 19:54:23,143 - __main__ - INFO - 为个体 9 生成利用路径
2025-07-30 19:54:23,143 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-30 19:54:23,143 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-30 19:54:23,143 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 117279.0
2025-07-30 19:54:23,143 - ExploitationExpert - INFO - 发现相似路径，相似度: 1.0000，相似id：4,跳过局部搜索
2025-07-30 19:54:23,143 - ExploitationExpert - INFO - 路径与已搜索路径相似度高，跳过局部搜索，生成新的贪心路径替换
2025-07-30 19:54:23,143 - __main__ - INFO - 个体 9 利用路径生成报告: {'new_tour': array([51, 10,  3, 34, 65, 39, 13, 42, 49, 24,  4,  6, 48, 62, 38,  5, 41,
       33, 61, 35, 20, 44, 31, 52, 30, 23, 45, 53, 15, 54, 28, 12, 17, 64,
       11, 46,  8, 36, 55,  0, 58, 56, 43, 18, 59, 40, 19,  1,  9, 16, 63,
       47, 21,  2, 25, 26, 29, 14, 22, 32, 57,  7, 50, 60, 37, 27],
      dtype=int64), 'cur_cost': 117279.0}
2025-07-30 19:54:23,143 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9], 'cur_cost': 11479.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([14, 34, 42,  7,  8, 52,  0, 18, 21, 44, 45, 31, 26, 25, 33,  3, 53,
       39, 17, 59, 23, 64,  9,  6, 20, 55, 54, 22, 50, 24, 36, 65, 12, 43,
       57, 41, 11, 61, 29, 40, 63, 10, 62, 46, 15, 27, 37, 48,  4, 58, 35,
       32, 51, 19, 16, 13, 49, 60, 28,  5, 38,  2,  1, 56, 30, 47],
      dtype=int64), 'cur_cost': 100846.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 64, 65, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63], 'cur_cost': 13416.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 7, 46, 29, 47, 20, 13, 30, 37, 61, 56, 49, 41, 40, 12,  5, 43, 45,
        1, 42,  0,  2, 59, 38, 14, 26, 22, 50, 33, 55, 65, 11, 19, 21, 25,
       36, 16, 48, 31, 54, 57, 15, 23,  6, 34, 35,  3, 39, 24, 51,  8, 52,
       32, 28, 18, 58, 44, 10, 17,  4, 63, 60, 64,  9, 27, 62, 53],
      dtype=int64), 'cur_cost': 93382.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 6, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57, 60, 63, 64, 65, 62, 59, 56, 53, 50, 47, 44, 41, 38, 35, 32, 29, 26, 23, 20, 17, 14, 11, 8, 5, 4, 7, 10, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 61], 'cur_cost': 25900.0}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([51, 10,  3, 34, 65, 39, 13, 42, 49, 24,  4,  6, 48, 62, 38,  5, 41,
       33, 61, 35, 20, 44, 31, 52, 30, 23, 45, 53, 15, 54, 28, 12, 17, 64,
       11, 46,  8, 36, 55,  0, 58, 56, 43, 18, 59, 40, 19,  1,  9, 16, 63,
       47, 21,  2, 25, 26, 29, 14, 22, 32, 57,  7, 50, 60, 37, 27],
      dtype=int64), 'cur_cost': 117279.0}}]
2025-07-30 19:54:23,143 - __main__ - INFO - 进化阶段完成
2025-07-30 19:54:23,143 - StatsExpert - INFO - 开始统计分析
2025-07-30 19:54:23,167 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11479.0, 'max': 117279.0, 'mean': 56994.9, 'std': 42970.97655499582}, 'diversity': 0.8360269360269361, 'clusters': {'clusters': 7, 'cluster_sizes': [4, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-30 19:54:23,167 - __main__ - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-07-30 19:54:23,167 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment ---
2025-07-30 19:54:23,169 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-07-30 19:54:23,169 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 3/5

#### Population metrics
- Min cost: 0 → 0 (Δ 0)
- Mean cost: 0 → 0
- Diversity: 0.000 → 0.000 (Δ 0)

#### Elite metrics
- Elite count: 25 → 25
- Best elite cost: 9521 → 9521 (Δ 0)
- Elite diversity: 0.078 → 0.078 (Δ 0.0)

#### Strategy performance
- Explore: 0 inds, success 0% , avg Δ 0
- Exploit: 0 inds, success 0% , avg Δ 0

#### Other indicators
- No-change individuals: 0
- Regression individuals: 0
- Historical trends: N/A

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

### Output Example
```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.5, "exploit": 0.5},
  "balance_state": "balanced",
  "recommendations": ["maintain_current_ratio"]
}
```

2025-07-30 19:54:23,169 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-30 19:54:33,183 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-07-30 19:54:35,184 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 2/3)
2025-07-30 19:54:36,975 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "early_stagnation",
  "strategy_effectiveness": {"explore": 0, "exploit": 0},
  "balance_state": "stagnant",
  "recommendations": ["increase_explore_ratio_to_0.2", "increase_diversity_weight_to_0.5"]
}
```
2025-07-30 19:54:36,975 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-07-30 19:54:36,975 - __main__ - INFO - --- Finished Evolution Iteration 4 ---
2025-07-30 19:54:36,975 - __main__ - INFO - composite13_66 开始进化第 5 代
2025-07-30 19:54:36,975 - __main__ - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-07-30 19:54:36,975 - StatsExpert - INFO - 开始统计分析
2025-07-30 19:54:37,000 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11479.0, 'max': 117279.0, 'mean': 56994.9, 'std': 42970.97655499582}, 'diversity': 0.8360269360269361, 'clusters': {'clusters': 7, 'cluster_sizes': [4, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-30 19:54:37,000 - PathExpert - INFO - 开始路径结构分析
2025-07-30 19:54:37,004 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-07-30 19:54:37,005 - PathExpert - INFO - 路径结构分析完成
2025-07-30 19:54:37,005 - EliteExpert - INFO - 开始精英解分析
2025-07-30 19:54:37,021 - EliteExpert - INFO - 精英解分析完成
2025-07-30 19:54:37,023 - LandscapeExpert - INFO - 开始景观分析
2025-07-30 19:54:37,024 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-30 19:54:37,025 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 4/5)
- population_size: 10
- cost_stats: min 11479.0, mean 56994.9, max 117279.0, std 42970.97655499582
- diversity: 0.8360269360269361
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Not available (Δ Not available, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {'common_edges': {}, 'common_edge_ratio': 0.0}
- elite_diversity: {'diversity_score': 0.22722222222222221}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

### Output Example
```json
{
  "search_space_features": {"ruggedness": 0.5, "modality": "multi", "deceptiveness": "unknown"},
  "population_state": {"diversity": 0.5, "convergence": 0.4, "clustering": "weak"},
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "unknown",
  "evolution_direction": {"recommended_focus": "explore", "operators": []}
}
```

### Formatting Guidelines (read carefully, DO NOT output this section)
1. Do NOT copy or repeat any *_sample values above; at least 50% of elements in difficult_regions and opportunity_regions must differ from those samples.
2. Use [] (empty array) when a list has no content; never output the string "None" or a null value.
3. For search_space_features use enumerated strings only:
    • ruggedness: "low" | "medium" | "high"
    • modality: "mono" | "multi" | "unknown"
    • deceptiveness: "low" | "moderate" | "high" | "unknown"
4. diversity and convergence must be floats with exactly 3 decimal places (e.g. 0.742).
5. Follow the JSON schema strictly; no additional keys or commentary.

2025-07-30 19:54:37,025 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-30 19:54:40,007 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "medium",
    "modality": "multi",
    "deceptiveness": "unknown"
  },
  "population_state": {
    "diversity": 0.836,
    "convergence": 0.000,
    "clustering": "no"
  },
  "difficult_regions": [
    {"coordinates": [436.0, 321.0, 800.0, 600.0], "description": "Low node density, potential long edge corridor."},
    {"coordinates": [2800.0, 1800.0, 3345.0, 2463.0], "description": "High long edge ratio suggests difficulty."}
  ],
  "opportunity_regions": [
    {"cell": [2, 2], "description": "High node density cell in density_grid."},
    {"cell": [0, 0], "description": "High node density cell in density_grid."}
  ],
  "evolution_phase": "early exploration",
  "evolution_direction": {
    "recommended_focus": "explore",
    "operators": [
      "Mutation operator with larger step size to explore diverse solutions.",
      "Crossover operator to combine promising subpaths."
    ]
  }
}
```
2025-07-30 19:54:40,007 - utils - INFO - 成功从JSON中提取数据
2025-07-30 19:54:40,007 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-30 19:54:40,007 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'medium', 'modality': 'multi', 'deceptiveness': 'unknown'}, 'population_state': {'diversity': 0.836, 'convergence': 0.0, 'clustering': 'no'}, 'difficult_regions': [{'coordinates': [436.0, 321.0, 800.0, 600.0], 'description': 'Low node density, potential long edge corridor.'}, {'coordinates': [2800.0, 1800.0, 3345.0, 2463.0], 'description': 'High long edge ratio suggests difficulty.'}], 'opportunity_regions': [{'cell': [2, 2], 'description': 'High node density cell in density_grid.'}, {'cell': [0, 0], 'description': 'High node density cell in density_grid.'}], 'evolution_phase': 'early exploration', 'evolution_direction': {'recommended_focus': 'explore', 'operators': ['Mutation operator with larger step size to explore diverse solutions.', 'Crossover operator to combine promising subpaths.']}}
2025-07-30 19:54:40,007 - __main__ - INFO - 开始策略分配阶段
2025-07-30 19:54:40,007 - StrategyExpert - INFO - 开始策略分配分析
2025-07-30 19:54:40,007 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 4
- Population: 10 individuals
  • diversity: 0.836
  • best_cost: 11479.0
  • mean_cost: 56994.9
- Search-space: ruggedness medium, modality multi, deceptiveness unknown
- Evolution phase: early exploration
- Landscape focus suggestion: explore
- Spatial density summary: None
- Elite diversity: N/A
- Difficult regions (# 2): [{"coordinates": [436.0, 321.0, 800.0, 600.0], "description": "Low node density, potential long edge corridor."}, {"coordinates": [2800.0, 1800.0, 3345.0, 2463.0], "description": "High long edge ratio suggests difficulty."}]
- Opportunity regions (# 2): [{"cell": [2, 2], "description": "High node density cell in density_grid."}, {"cell": [0, 0], "description": "High node density cell in density_grid."}]
- Last-iteration feedback: ```json
{
  "evolution_stage": "early_stagnation",
  "strategy_effectiveness": {"explore": 0, "exploit": 0},
  "balance_...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Formatting Guidelines (DO NOT output this section)
1. global_explore_ratio must be rounded to exactly 2 decimal places (e.g., 0.80).
2. The individual_assignments mapping MUST include every index from 0 to (population_size - 1) without omission.
3. Valid values are "explore" or "exploit" (lowercase).
4. rationale ≤ 40 words.

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-30 19:54:40,007 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-30 19:54:40,007 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-30 19:54:42,010 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.75,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early exploration phase and focus on exploring the landscape. Low node density areas suggest exploration and high node density areas, exploitation."
}
```
2025-07-30 19:54:42,010 - utils - INFO - 成功从JSON中提取数据
2025-07-30 19:54:42,010 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'exploit', 'exploit']
2025-07-30 19:54:42,010 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'exploit', 'exploit']
2025-07-30 19:54:42,010 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.75,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early exploration phase and focus on exploring the landscape. Low node density areas suggest exploration and high node density areas, exploitation."
}
```
2025-07-30 19:54:42,011 - __main__ - INFO - 策略分配阶段完成
2025-07-30 19:54:42,011 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'exploit', 'exploit']
2025-07-30 19:54:42,011 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.75,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early exploration phase and focus on exploring the landscape. Low node density areas suggest exploration and high node density areas, exploitation."
}
```
2025-07-30 19:54:42,011 - __main__ - INFO - 开始进化阶段
2025-07-30 19:54:42,012 - __main__ - INFO - 为个体 0 生成探索路径
2025-07-30 19:54:42,012 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-30 19:54:42,012 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.6338461538461538
- Evolution phase: early exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): {'cell': [2, 2], {'cell': [0, 0]
- Difficult regions to avoid (sample): {'coordinates':, {'coordinates':
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-30 19:54:42,012 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-30 19:54:42,013 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-30 19:54:44,755 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 2, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "Opportunity cell (0,0) and (2,2)",
  "strategy_comment": "Prioritizes opportunity regions while maintaining path validity, early stage exploration."
}
```
2025-07-30 19:54:44,755 - utils - INFO - 成功从JSON中提取数据
2025-07-30 19:54:44,755 - ExplorationExpert - INFO - 计算路径成本
2025-07-30 19:54:44,755 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11474.0, 路径: [0, 2, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-30 19:54:44,755 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 2, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11474.0}
2025-07-30 19:54:44,755 - __main__ - INFO - 为个体 1 生成探索路径
2025-07-30 19:54:44,755 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-30 19:54:44,755 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 93694.0 (rank 70.0%)
- Population diversity: 0.6338461538461538
- Evolution phase: early exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): {'cell': [2, 2], {'cell': [0, 0]
- Difficult regions to avoid (sample): {'coordinates':, {'coordinates':
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-30 19:54:44,755 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-30 19:54:44,755 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-30 19:54:47,547 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 8, 9, 10, 11, 12, 13, 14, 15, 21, 20, 19, 18, 17, 16, 22, 23, 29, 30, 31, 32, 33, 34, 35, 41, 40, 39, 38, 37, 36, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 7, 6, 5, 4, 24, 25, 26, 27, 28],
  "targeted_regions": "Explores cells [2,2] and [0,0]. Avoids hard coordinates.",
  "strategy_comment": "Focus on opportunity cells. Diversifies the path by sampling unexplored areas."
}
```
2025-07-30 19:54:47,547 - utils - INFO - 成功从JSON中提取数据
2025-07-30 19:54:47,547 - ExplorationExpert - INFO - 计算路径成本
2025-07-30 19:54:47,547 - ExplorationExpert - INFO - 探索路径生成完成，成本: 19376.0, 路径: [0, 1, 2, 8, 9, 10, 11, 12, 13, 14, 15, 21, 20, 19, 18, 17, 16, 22, 23, 29, 30, 31, 32, 33, 34, 35, 41, 40, 39, 38, 37, 36, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 7, 6, 5, 4, 24, 25, 26, 27, 28, 3]
2025-07-30 19:54:47,547 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 1, 2, 8, 9, 10, 11, 12, 13, 14, 15, 21, 20, 19, 18, 17, 16, 22, 23, 29, 30, 31, 32, 33, 34, 35, 41, 40, 39, 38, 37, 36, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 7, 6, 5, 4, 24, 25, 26, 27, 28, 3], 'cur_cost': 19376.0}
2025-07-30 19:54:47,547 - __main__ - INFO - 为个体 2 生成探索路径
2025-07-30 19:54:47,547 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-30 19:54:47,550 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 10.0%)
- Population diversity: 0.5646153846153846
- Evolution phase: early exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): {'cell': [2, 2], {'cell': [0, 0]
- Difficult regions to avoid (sample): {'coordinates':, {'coordinates':
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-30 19:54:47,550 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-30 19:54:47,550 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-30 19:54:50,290 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "cell(2,2) and cell(0,0).",
  "strategy_comment": "Started path at 0, explore cell(0,0) then cell(2,2) followed by rest of the nodes, exploring opportunity regions. "
}
```
2025-07-30 19:54:50,290 - utils - INFO - 成功从JSON中提取数据
2025-07-30 19:54:50,290 - ExplorationExpert - INFO - 计算路径成本
2025-07-30 19:54:50,290 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-30 19:54:50,290 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-30 19:54:50,290 - __main__ - INFO - 为个体 3 生成探索路径
2025-07-30 19:54:50,290 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-30 19:54:50,290 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 100846.0 (rank 80.0%)
- Population diversity: 0.5646153846153846
- Evolution phase: early exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): {'cell': [2, 2], {'cell': [0, 0]
- Difficult regions to avoid (sample): {'coordinates':, {'coordinates':
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-30 19:54:50,290 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-30 19:54:50,290 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-30 19:54:53,334 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 2, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "cell(2,2) and cell(0,0) are covered at the start",
  "strategy_comment": "Explores opportunities with minimal changes, starting at sparse regions."
}
```
2025-07-30 19:54:53,334 - utils - INFO - 成功从JSON中提取数据
2025-07-30 19:54:53,334 - ExplorationExpert - INFO - 计算路径成本
2025-07-30 19:54:53,335 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11474.0, 路径: [0, 2, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-30 19:54:53,335 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 2, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11474.0}
2025-07-30 19:54:53,335 - __main__ - INFO - 为个体 4 生成探索路径
2025-07-30 19:54:53,335 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-30 19:54:53,335 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 13416.0 (rank 40.0%)
- Population diversity: 0.4907692307692308
- Evolution phase: early exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): {'cell': [2, 2], {'cell': [0, 0]
- Difficult regions to avoid (sample): {'coordinates':, {'coordinates':
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-30 19:54:53,335 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-30 19:54:53,335 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-30 19:55:03,350 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-07-30 19:55:05,350 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 2/3)
2025-07-30 19:55:08,075 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 2, 3, 4, 5],
  "targeted_regions": "Explore cells (2,2) and (0,0). Avoid Difficult regions.",
  "strategy_comment": "Prioritizes opportunity regions and sparse cells, while avoiding known difficulties and maintaining path diversity."
}
```
2025-07-30 19:55:08,075 - utils - INFO - 成功从JSON中提取数据
2025-07-30 19:55:08,075 - ExplorationExpert - INFO - 计算路径成本
2025-07-30 19:55:08,075 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11453.0, 路径: [0, 1, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 2, 3, 4, 5]
2025-07-30 19:55:08,075 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 1, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 2, 3, 4, 5], 'cur_cost': 11453.0}
2025-07-30 19:55:08,075 - __main__ - INFO - 为个体 5 生成利用路径
2025-07-30 19:55:08,075 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-30 19:55:08,075 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-30 19:55:08,075 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 115666.0
2025-07-30 19:55:08,582 - ExploitationExpert - INFO - res_population_num: 28
2025-07-30 19:55:08,582 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9522, 9528, 9536, 9561.0, 9563.0, 9586.0, 9521, 9521, 9521]
2025-07-30 19:55:08,582 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 31, 33, 35, 34, 30, 28, 32, 29, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 56, 58, 60, 54, 57, 64, 62,
       59, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  8,  2,  6,  4,  5,  9,  3, 27, 37, 36, 26, 25, 31,
       24, 29, 32, 33, 28, 30, 35, 34, 42, 50, 51, 38, 41, 45, 44, 39, 47,
       46, 48, 49, 40, 43, 21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14,
       56, 58, 60, 54, 57, 64, 62, 59, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 54, 57, 64, 53, 62,
       59, 56, 60, 58, 49, 40, 43, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50,
       42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 18, 17,
       12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 47, 39, 44, 41, 38, 51, 50, 45, 46, 48, 42, 34, 30,
       28, 35, 26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64)]
2025-07-30 19:55:08,585 - ExploitationExpert - INFO - populations_num: 10
2025-07-30 19:55:08,585 - ExploitationExpert - INFO - populations: [{'tour': [0, 2, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11474.0}, {'tour': [0, 1, 2, 8, 9, 10, 11, 12, 13, 14, 15, 21, 20, 19, 18, 17, 16, 22, 23, 29, 30, 31, 32, 33, 34, 35, 41, 40, 39, 38, 37, 36, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 7, 6, 5, 4, 24, 25, 26, 27, 28, 3], 'cur_cost': 19376.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 2, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11474.0}, {'tour': [0, 1, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 2, 3, 4, 5], 'cur_cost': 11453.0}, {'tour': array([17, 64, 18, 47, 55, 29, 14, 21, 11, 58, 20, 39, 32, 43, 37, 63, 15,
       41,  8,  2, 19, 54, 22,  1, 42, 28,  0, 12,  5, 40, 23, 44, 16, 50,
       57, 51, 10, 46, 27, 56, 31, 45, 33, 52,  3, 61, 34, 24, 36,  6, 35,
        7, 30, 59,  4, 62, 60, 38, 48, 13, 49, 53, 65, 26, 25,  9],
      dtype=int64), 'cur_cost': 115666.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [7, 46, 29, 47, 20, 13, 30, 37, 61, 56, 49, 41, 40, 12, 5, 43, 45, 1, 42, 0, 2, 59, 38, 14, 26, 22, 50, 33, 55, 65, 11, 19, 21, 25, 36, 16, 48, 31, 54, 57, 15, 23, 6, 34, 35, 3, 39, 24, 51, 8, 52, 32, 28, 18, 58, 44, 10, 17, 4, 63, 60, 64, 9, 27, 62, 53], 'cur_cost': 93382.0}, {'tour': [0, 1, 2, 3, 6, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57, 60, 63, 64, 65, 62, 59, 56, 53, 50, 47, 44, 41, 38, 35, 32, 29, 26, 23, 20, 17, 14, 11, 8, 5, 4, 7, 10, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 61], 'cur_cost': 25900.0}, {'tour': [51, 10, 3, 34, 65, 39, 13, 42, 49, 24, 4, 6, 48, 62, 38, 5, 41, 33, 61, 35, 20, 44, 31, 52, 30, 23, 45, 53, 15, 54, 28, 12, 17, 64, 11, 46, 8, 36, 55, 0, 58, 56, 43, 18, 59, 40, 19, 1, 9, 16, 63, 47, 21, 2, 25, 26, 29, 14, 22, 32, 57, 7, 50, 60, 37, 27], 'cur_cost': 117279.0}]
2025-07-30 19:55:08,585 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-30 19:55:08,585 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 5, 'total_searches': 18, 'skip_rate': 0.2777777777777778, 'estimated_time_saved': 25, 'path_optimizer': {'path_count': 13, 'cache_hits': 37, 'similarity_calculations': 128, 'cache_hit_rate': 0.2890625, 'cache_size': 91}}
2025-07-30 19:55:08,585 - __main__ - WARNING - 个体 5 的利用路径生成失败，保留原个体
2025-07-30 19:55:08,585 - __main__ - INFO - 为个体 6 生成探索路径
2025-07-30 19:55:08,585 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-30 19:55:08,585 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 30.0%)
- Population diversity: 0.4876923076923077
- Evolution phase: early exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): {'cell': [2, 2], {'cell': [0, 0]
- Difficult regions to avoid (sample): {'coordinates':, {'coordinates':
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-30 19:55:08,585 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-30 19:55:08,585 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-30 19:55:19,351 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-07-30 19:55:21,352 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 2/3)
2025-07-30 19:55:24,232 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1],
  "targeted_regions": "cell(2,2), cell(0,0)",
  "strategy_comment": "Prioritizes opportunity regions while maintaining path integrity and introducing diversity in node sequence."
}
```
2025-07-30 19:55:24,232 - utils - INFO - 成功从JSON中提取数据
2025-07-30 19:55:24,232 - ExplorationExpert - INFO - 计算路径成本
2025-07-30 19:55:24,232 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1]
2025-07-30 19:55:24,232 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1], 'cur_cost': 11479.0}
2025-07-30 19:55:24,232 - __main__ - INFO - 为个体 7 生成利用路径
2025-07-30 19:55:24,232 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-30 19:55:24,232 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-30 19:55:24,232 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 105637.0
2025-07-30 19:55:24,742 - ExploitationExpert - INFO - res_population_num: 30
2025-07-30 19:55:24,742 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9522, 9528, 9536, 9561.0, 9563.0, 9586.0, 9521, 9521, 9521, 9521, 9521]
2025-07-30 19:55:24,742 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 31, 33, 35, 34, 30, 28, 32, 29, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 56, 58, 60, 54, 57, 64, 62,
       59, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  8,  2,  6,  4,  5,  9,  3, 27, 37, 36, 26, 25, 31,
       24, 29, 32, 33, 28, 30, 35, 34, 42, 50, 51, 38, 41, 45, 44, 39, 47,
       46, 48, 49, 40, 43, 21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14,
       56, 58, 60, 54, 57, 64, 62, 59, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 54, 57, 64, 53, 62,
       59, 56, 60, 58, 49, 40, 43, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50,
       42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 18, 17,
       12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 47, 39, 44, 41, 38, 51, 50, 45, 46, 48, 42, 34, 30,
       28, 35, 26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-07-30 19:55:24,753 - ExploitationExpert - INFO - populations_num: 10
2025-07-30 19:55:24,753 - ExploitationExpert - INFO - populations: [{'tour': [0, 2, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11474.0}, {'tour': [0, 1, 2, 8, 9, 10, 11, 12, 13, 14, 15, 21, 20, 19, 18, 17, 16, 22, 23, 29, 30, 31, 32, 33, 34, 35, 41, 40, 39, 38, 37, 36, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 7, 6, 5, 4, 24, 25, 26, 27, 28, 3], 'cur_cost': 19376.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 2, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11474.0}, {'tour': [0, 1, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 2, 3, 4, 5], 'cur_cost': 11453.0}, {'tour': array([17, 64, 18, 47, 55, 29, 14, 21, 11, 58, 20, 39, 32, 43, 37, 63, 15,
       41,  8,  2, 19, 54, 22,  1, 42, 28,  0, 12,  5, 40, 23, 44, 16, 50,
       57, 51, 10, 46, 27, 56, 31, 45, 33, 52,  3, 61, 34, 24, 36,  6, 35,
        7, 30, 59,  4, 62, 60, 38, 48, 13, 49, 53, 65, 26, 25,  9],
      dtype=int64), 'cur_cost': 115666.0}, {'tour': [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1], 'cur_cost': 11479.0}, {'tour': array([ 4, 15, 62, 32, 59,  5, 47, 40, 13, 63,  9, 11, 33, 44, 31, 49, 18,
       10,  0, 14,  1, 19, 38, 48, 53, 35,  7, 43, 45, 64, 17, 24, 26, 37,
       23, 41, 20, 50, 29,  8, 55, 42, 21, 28, 22, 52,  6, 54, 16, 61,  3,
       12, 65, 36, 27, 34, 58, 30, 39, 46, 60,  2, 56, 51, 57, 25],
      dtype=int64), 'cur_cost': 105637.0}, {'tour': [0, 1, 2, 3, 6, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57, 60, 63, 64, 65, 62, 59, 56, 53, 50, 47, 44, 41, 38, 35, 32, 29, 26, 23, 20, 17, 14, 11, 8, 5, 4, 7, 10, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 61], 'cur_cost': 25900.0}, {'tour': [51, 10, 3, 34, 65, 39, 13, 42, 49, 24, 4, 6, 48, 62, 38, 5, 41, 33, 61, 35, 20, 44, 31, 52, 30, 23, 45, 53, 15, 54, 28, 12, 17, 64, 11, 46, 8, 36, 55, 0, 58, 56, 43, 18, 59, 40, 19, 1, 9, 16, 63, 47, 21, 2, 25, 26, 29, 14, 22, 32, 57, 7, 50, 60, 37, 27], 'cur_cost': 117279.0}]
2025-07-30 19:55:24,754 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-07-30 19:55:24,754 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 5, 'total_searches': 19, 'skip_rate': 0.2631578947368421, 'estimated_time_saved': 25, 'path_optimizer': {'path_count': 14, 'cache_hits': 37, 'similarity_calculations': 141, 'cache_hit_rate': 0.2624113475177305, 'cache_size': 104}}
2025-07-30 19:55:24,754 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-07-30 19:55:24,754 - __main__ - INFO - 为个体 8 生成利用路径
2025-07-30 19:55:24,754 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-30 19:55:24,754 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-30 19:55:24,754 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 108422.0
2025-07-30 19:55:25,263 - ExploitationExpert - INFO - res_population_num: 30
2025-07-30 19:55:25,263 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9522, 9528, 9536, 9561.0, 9563.0, 9586.0, 9521, 9521, 9521, 9521, 9521]
2025-07-30 19:55:25,263 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 31, 33, 35, 34, 30, 28, 32, 29, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 56, 58, 60, 54, 57, 64, 62,
       59, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  8,  2,  6,  4,  5,  9,  3, 27, 37, 36, 26, 25, 31,
       24, 29, 32, 33, 28, 30, 35, 34, 42, 50, 51, 38, 41, 45, 44, 39, 47,
       46, 48, 49, 40, 43, 21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14,
       56, 58, 60, 54, 57, 64, 62, 59, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 54, 57, 64, 53, 62,
       59, 56, 60, 58, 49, 40, 43, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50,
       42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 18, 17,
       12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 47, 39, 44, 41, 38, 51, 50, 45, 46, 48, 42, 34, 30,
       28, 35, 26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-07-30 19:55:25,263 - ExploitationExpert - INFO - populations_num: 10
2025-07-30 19:55:25,263 - ExploitationExpert - INFO - populations: [{'tour': [0, 2, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11474.0}, {'tour': [0, 1, 2, 8, 9, 10, 11, 12, 13, 14, 15, 21, 20, 19, 18, 17, 16, 22, 23, 29, 30, 31, 32, 33, 34, 35, 41, 40, 39, 38, 37, 36, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 7, 6, 5, 4, 24, 25, 26, 27, 28, 3], 'cur_cost': 19376.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 2, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11474.0}, {'tour': [0, 1, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 2, 3, 4, 5], 'cur_cost': 11453.0}, {'tour': array([17, 64, 18, 47, 55, 29, 14, 21, 11, 58, 20, 39, 32, 43, 37, 63, 15,
       41,  8,  2, 19, 54, 22,  1, 42, 28,  0, 12,  5, 40, 23, 44, 16, 50,
       57, 51, 10, 46, 27, 56, 31, 45, 33, 52,  3, 61, 34, 24, 36,  6, 35,
        7, 30, 59,  4, 62, 60, 38, 48, 13, 49, 53, 65, 26, 25,  9],
      dtype=int64), 'cur_cost': 115666.0}, {'tour': [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1], 'cur_cost': 11479.0}, {'tour': array([ 4, 15, 62, 32, 59,  5, 47, 40, 13, 63,  9, 11, 33, 44, 31, 49, 18,
       10,  0, 14,  1, 19, 38, 48, 53, 35,  7, 43, 45, 64, 17, 24, 26, 37,
       23, 41, 20, 50, 29,  8, 55, 42, 21, 28, 22, 52,  6, 54, 16, 61,  3,
       12, 65, 36, 27, 34, 58, 30, 39, 46, 60,  2, 56, 51, 57, 25],
      dtype=int64), 'cur_cost': 105637.0}, {'tour': array([35, 51, 14, 13, 49, 45, 55, 53,  8,  2, 28, 10, 11, 48,  1, 38, 56,
       42,  9, 12,  7, 39, 25, 44, 21, 46, 15, 58, 57, 23,  4, 26, 59, 18,
       41,  0, 19, 65,  6, 22, 17, 43, 32, 62, 30, 37, 27, 54,  5, 16, 24,
       36, 60, 61, 63, 20, 29, 40, 33, 64, 50,  3, 31, 52, 47, 34],
      dtype=int64), 'cur_cost': 108422.0}, {'tour': [51, 10, 3, 34, 65, 39, 13, 42, 49, 24, 4, 6, 48, 62, 38, 5, 41, 33, 61, 35, 20, 44, 31, 52, 30, 23, 45, 53, 15, 54, 28, 12, 17, 64, 11, 46, 8, 36, 55, 0, 58, 56, 43, 18, 59, 40, 19, 1, 9, 16, 63, 47, 21, 2, 25, 26, 29, 14, 22, 32, 57, 7, 50, 60, 37, 27], 'cur_cost': 117279.0}]
2025-07-30 19:55:25,263 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-30 19:55:25,263 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 5, 'total_searches': 20, 'skip_rate': 0.25, 'estimated_time_saved': 25, 'path_optimizer': {'path_count': 15, 'cache_hits': 37, 'similarity_calculations': 155, 'cache_hit_rate': 0.23870967741935484, 'cache_size': 118}}
2025-07-30 19:55:25,263 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-07-30 19:55:25,263 - __main__ - INFO - 为个体 9 生成利用路径
2025-07-30 19:55:25,263 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-30 19:55:25,263 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-30 19:55:25,263 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 103216.0
2025-07-30 19:55:25,764 - ExploitationExpert - INFO - res_population_num: 31
2025-07-30 19:55:25,764 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9522, 9528, 9536, 9561.0, 9563.0, 9586.0, 9521, 9521, 9521, 9521, 9521, 9521]
2025-07-30 19:55:25,764 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 31, 33, 35, 34, 30, 28, 32, 29, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 56, 58, 60, 54, 57, 64, 62,
       59, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  8,  2,  6,  4,  5,  9,  3, 27, 37, 36, 26, 25, 31,
       24, 29, 32, 33, 28, 30, 35, 34, 42, 50, 51, 38, 41, 45, 44, 39, 47,
       46, 48, 49, 40, 43, 21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14,
       56, 58, 60, 54, 57, 64, 62, 59, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 54, 57, 64, 53, 62,
       59, 56, 60, 58, 49, 40, 43, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50,
       42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 18, 17,
       12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 47, 39, 44, 41, 38, 51, 50, 45, 46, 48, 42, 34, 30,
       28, 35, 26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-07-30 19:55:25,766 - ExploitationExpert - INFO - populations_num: 10
2025-07-30 19:55:25,766 - ExploitationExpert - INFO - populations: [{'tour': [0, 2, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11474.0}, {'tour': [0, 1, 2, 8, 9, 10, 11, 12, 13, 14, 15, 21, 20, 19, 18, 17, 16, 22, 23, 29, 30, 31, 32, 33, 34, 35, 41, 40, 39, 38, 37, 36, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 7, 6, 5, 4, 24, 25, 26, 27, 28, 3], 'cur_cost': 19376.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 2, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11474.0}, {'tour': [0, 1, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 2, 3, 4, 5], 'cur_cost': 11453.0}, {'tour': array([17, 64, 18, 47, 55, 29, 14, 21, 11, 58, 20, 39, 32, 43, 37, 63, 15,
       41,  8,  2, 19, 54, 22,  1, 42, 28,  0, 12,  5, 40, 23, 44, 16, 50,
       57, 51, 10, 46, 27, 56, 31, 45, 33, 52,  3, 61, 34, 24, 36,  6, 35,
        7, 30, 59,  4, 62, 60, 38, 48, 13, 49, 53, 65, 26, 25,  9],
      dtype=int64), 'cur_cost': 115666.0}, {'tour': [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1], 'cur_cost': 11479.0}, {'tour': array([ 4, 15, 62, 32, 59,  5, 47, 40, 13, 63,  9, 11, 33, 44, 31, 49, 18,
       10,  0, 14,  1, 19, 38, 48, 53, 35,  7, 43, 45, 64, 17, 24, 26, 37,
       23, 41, 20, 50, 29,  8, 55, 42, 21, 28, 22, 52,  6, 54, 16, 61,  3,
       12, 65, 36, 27, 34, 58, 30, 39, 46, 60,  2, 56, 51, 57, 25],
      dtype=int64), 'cur_cost': 105637.0}, {'tour': array([35, 51, 14, 13, 49, 45, 55, 53,  8,  2, 28, 10, 11, 48,  1, 38, 56,
       42,  9, 12,  7, 39, 25, 44, 21, 46, 15, 58, 57, 23,  4, 26, 59, 18,
       41,  0, 19, 65,  6, 22, 17, 43, 32, 62, 30, 37, 27, 54,  5, 16, 24,
       36, 60, 61, 63, 20, 29, 40, 33, 64, 50,  3, 31, 52, 47, 34],
      dtype=int64), 'cur_cost': 108422.0}, {'tour': array([49, 25, 41, 54, 46, 32,  9, 36, 11, 44, 55, 64, 33,  8,  0, 28, 14,
       22, 50, 62, 23, 35, 40, 34, 17, 56, 60, 13, 38, 24, 43, 26, 29, 52,
       10, 30, 53, 18, 63, 61,  2, 31, 20, 47,  3,  1, 59, 65, 42, 15,  5,
        7,  4, 58, 45, 51, 27, 57, 48,  6, 12, 21, 19, 37, 16, 39],
      dtype=int64), 'cur_cost': 103216.0}]
2025-07-30 19:55:25,766 - ExploitationExpert - INFO - 局部搜索耗时: 0.50秒
2025-07-30 19:55:25,766 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 5, 'total_searches': 21, 'skip_rate': 0.23809523809523808, 'estimated_time_saved': 25, 'path_optimizer': {'path_count': 16, 'cache_hits': 37, 'similarity_calculations': 170, 'cache_hit_rate': 0.21764705882352942, 'cache_size': 133}}
2025-07-30 19:55:25,766 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-07-30 19:55:25,766 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11474.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 8, 9, 10, 11, 12, 13, 14, 15, 21, 20, 19, 18, 17, 16, 22, 23, 29, 30, 31, 32, 33, 34, 35, 41, 40, 39, 38, 37, 36, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 7, 6, 5, 4, 24, 25, 26, 27, 28, 3], 'cur_cost': 19376.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11474.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 2, 3, 4, 5], 'cur_cost': 11453.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1], 'cur_cost': 11479.0}}]
2025-07-30 19:55:25,778 - __main__ - INFO - 进化阶段完成
2025-07-30 19:55:25,778 - StatsExpert - INFO - 开始统计分析
2025-07-30 19:55:25,797 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11453.0, 'max': 115666.0, 'mean': 50967.6, 'std': 46907.515801201844}, 'diversity': 0.675084175084175, 'clusters': {'clusters': 5, 'cluster_sizes': [6, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-30 19:55:25,798 - __main__ - INFO - --- Running Assessment Phase (Iteration 4) ---
2025-07-30 19:55:25,798 - EvolutionAssessmentExpert - INFO - --- Iteration 4 Assessment ---
2025-07-30 19:55:25,800 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-07-30 19:55:25,800 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 4/5

#### Population metrics
- Min cost: 0 → 0 (Δ 0)
- Mean cost: 0 → 0
- Diversity: 0.000 → 0.000 (Δ 0)

#### Elite metrics
- Elite count: 31 → 31
- Best elite cost: 9521 → 9521 (Δ 0)
- Elite diversity: 0.063 → 0.063 (Δ 0.0)

#### Strategy performance
- Explore: 0 inds, success 0% , avg Δ 0
- Exploit: 0 inds, success 0% , avg Δ 0

#### Other indicators
- No-change individuals: 0
- Regression individuals: 0
- Historical trends: N/A

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

### Output Example
```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.5, "exploit": 0.5},
  "balance_state": "balanced",
  "recommendations": ["maintain_current_ratio"]
}
```

2025-07-30 19:55:25,801 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-30 19:55:28,044 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "stagnant",
  "strategy_effectiveness": {"explore": 0.0, "exploit": 0.0},
  "balance_state": "unbalanced_stagnation",
  "recommendations": [
    "Increase exploration. No individuals are being explored with zero success.",
    "Consider increasing the explore ratio (e.g., to 0.8) to introduce more diversity.",
    "Analyze the search space for potential optima that are difficult to find.",
    "Check the mutation/crossover parameters to ensure sufficient variation within the explore step."
  ]
}
```
2025-07-30 19:55:28,044 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-07-30 19:55:28,044 - __main__ - INFO - --- Finished Evolution Iteration 5 ---
2025-07-30 19:55:28,064 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0701\MoE-main\solution\composite13_66_solution.json
2025-07-30 19:55:28,065 - __main__ - INFO - 实例 composite13_66 处理完成
